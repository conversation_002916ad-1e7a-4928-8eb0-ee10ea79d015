import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { paymentMethodService } from '../../services';
import { validateCryptoAddress } from '../../utils/web3';
import type {
  CreatePaymentMethodRequest,
  UpdatePaymentMethodRequest,
  PaymentMethod,
} from '../../types';
import toast from 'react-hot-toast';

type PaymentMethodType = 'bank' | 'crypto';

export const usePaymentMethodForm = (
  initialData?: Partial<
    CreatePaymentMethodRequest | UpdatePaymentMethodRequest
  >,
  paymentMethod?: PaymentMethod
) => {
  const navigate = useNavigate();
  const [type, setType] = useState<PaymentMethodType>(
    paymentMethod?.type || 'bank'
  );
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState<
    Partial<CreatePaymentMethodRequest | UpdatePaymentMethodRequest>
  >({
    type: 'bank',
    bank_name: '',
    bank_code: '',
    account_number: '',
    account_name: '',
    crypto_network: '',
    wallet_address: '',
    ...initialData,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const handleInputChange = (field: string, value: string) => {
    setFormData(
      (
        prev: Partial<CreatePaymentMethodRequest | UpdatePaymentMethodRequest>
      ) => ({
        ...prev,
        [field]: value,
      })
    );

    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };
  const updateFormData = (
    data: Partial<CreatePaymentMethodRequest | UpdatePaymentMethodRequest>
  ) => {
    setFormData(
      (
        prev: Partial<CreatePaymentMethodRequest | UpdatePaymentMethodRequest>
      ) => ({ ...prev, ...data })
    );
  };

  const clearError = (field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  const setFormErrors = (newErrors: Record<string, string>) => {
    setErrors(newErrors);
  };

  const validateForm = (
    accountValidated: boolean = false,
    needsValidation: boolean = false
  ): boolean => {
    const newErrors: Record<string, string> = {};

    if (type === 'bank') {
      if (!formData.bank_code) {
        newErrors.bank_code = 'Please select a bank';
      }
      if (!formData.account_number?.trim()) {
        newErrors.account_number = 'Account number is required';
      }
      if (!formData.account_name?.trim()) {
        newErrors.account_name = 'Account name is required';
      }
      if (!accountValidated && formData.bank_code && formData.account_number) {
        newErrors.account_number = 'Please verify the account';
      }
      if (needsValidation && !accountValidated) {
        newErrors.account_number = 'Please verify the account';
      }
    } else {
      if (!formData.crypto_network?.trim()) {
        newErrors.crypto_network = 'Crypto network is required';
      }
      if (!formData.wallet_address?.trim()) {
        newErrors.wallet_address = 'Wallet address is required';
      } else {
        // Validate crypto address format
        const network = formData.crypto_network || '';
        const validationResult = validateCryptoAddress(
          formData.wallet_address,
          network
        );

        if (!validationResult.isValid) {
          newErrors.wallet_address =
            validationResult.error || 'Invalid wallet address';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (
    e: React.FormEvent,
    additionalValidation?: () => boolean,
    onSuccess?: () => void,
    confirmationCode?: string
  ) => {
    e.preventDefault();

    if (additionalValidation && !additionalValidation()) {
      return;
    }

    try {
      const loadingState = paymentMethod ? setSaving : setLoading;
      loadingState(true);

      // Prepare form data with confirmation code
      const submitData = {
        ...formData,
        ...(confirmationCode && { confirmation_code: confirmationCode }),
      };

      let response;
      if (paymentMethod) {
        // Update existing payment method
        response = await paymentMethodService.updatePaymentMethod(
          paymentMethod.id,
          submitData as UpdatePaymentMethodRequest
        );
      } else {
        // Create new payment method
        response = await paymentMethodService.createPaymentMethod({
          ...submitData,
          type,
        } as CreatePaymentMethodRequest);
      }

      if (response.status === 'success') {
        const message = paymentMethod
          ? 'Payment method updated successfully'
          : 'Payment method created successfully';
        toast.success(message);

        if (onSuccess) {
          onSuccess();
        } else {
          navigate('/payment-methods');
        }
      } else {
        const errorMessage = paymentMethod
          ? 'Failed to update payment method'
          : 'Failed to create payment method';
        toast.error(response.message || errorMessage);
      }
    } catch (error) {
      console.error('Error submitting payment method:', error);
      const errorMessage = paymentMethod
        ? 'Failed to update payment method'
        : 'Failed to create payment method';
      toast.error(errorMessage);
    } finally {
      const loadingState = paymentMethod ? setSaving : setLoading;
      loadingState(false);
    }
  };

  return {
    type,
    setType,
    formData,
    setFormData,
    errors,
    loading,
    saving,
    handleInputChange,
    updateFormData,
    clearError,
    setFormErrors,
    validateForm,
    handleSubmit,
  };
};
