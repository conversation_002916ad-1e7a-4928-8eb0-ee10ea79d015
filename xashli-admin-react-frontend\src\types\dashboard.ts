export interface AdminDashboardData {
  users: {
    counts: {
      total: number;
      active: number;
      inactive: number;
      new_today: number;
    };
  };
  funds: {
    counts: {
      total: number;
      statuses: {
        pending: number;
        matched: number;
        completed: number;
        cancelled: number;
      };
      new_today: number;
    };
    amounts: {
      fiat: number;
      crypto: number;
    };
  };
  withdraws: {
    counts: {
      total: number;
      statuses: {
        pending: number;
        matched: number;
        completed: number;
      };
      new_today: number;
    };
    amounts: {
      fiat: number;
      crypto: number;
    };
  };
  matches: {
    counts: {
      total: number;
      statuses: {
        pending: number;
        paid: number;
        confirmed: number;
        disputed: number;
      };
      new_today: number;
    };
    amounts: {
      fiat: number;
      crypto: number;
    };
  };
}
