import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Badge } from '../../components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import {
  Search,
  Filter,
  Eye,
  TrendingUp,
  User as UserIcon,
  X,
} from 'lucide-react';
import { fundService } from '../../services/fund';
import { userService } from '../../services/user';
import { Pagination } from '../../components/ui/pagination';
import type { Fund, FundFilters } from '../../types/fund';
import { FundStatsCards } from '../../components/funds/FundStatsCards';
import { LoadingSpinner } from '../../components/ui/loading';
import { MainLayout } from '../../components/layout/MainLayout';
import { format } from 'date-fns';

export const FundsPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [filters, setFilters] = useState<FundFilters>({});
  const [pendingFilters, setPendingFilters] = useState<FundFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const navigate = useNavigate();

  // Initialize filters from URL search params
  useEffect(() => {
    const userId = searchParams.get('user_id');
    if (userId) {
      const initialFilters = { user_id: userId };
      setFilters(initialFilters);
      setPendingFilters(initialFilters);
      setShowFilters(true); // Show filters when user_id is present
    }
  }, [searchParams]);

  // Initialize pending filters with current filters
  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Fetch funds with current filters and pagination
  const {
    data: fundsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['funds', filters, currentPage, pageSize],
    queryFn: () =>
      fundService.getFunds({
        ...filters,
        page: currentPage,
        per_page: pageSize,
      }),
  });

  // Fetch fund statistics
  const { data: statsResponse, isLoading: statsLoading } = useQuery({
    queryKey: [
      'fund-statistics',
      filters.user_id,
      filters.email,
      filters.sort_field,
      filters.sort_direction,
      filters.date_from,
      filters.date_to,
    ],
    queryFn: () =>
      fundService.getFundStats({
        user_id: filters.user_id,
        email: filters.email,
        date_from: filters.date_from,
        date_to: filters.date_to,
      }),
  });

  // Fetch user data when user_id is present in filters
  const userId = searchParams.get('user_id');
  const { data: userResponse } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => userService.getUserById(userId!),
    enabled: !!userId,
  });

  const funds = fundsResponse?.data?.funds || [];
  const pagination = fundsResponse?.data?.pagination || null;
  const statsData = statsResponse?.data || null;

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Failed to load funds. Please try again.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleFilterChange = (
    key: keyof FundFilters,
    value: string | undefined
  ) => {
    setPendingFilters(prev => {
      const newFilters = { ...prev };
      if (value && value !== '') {
        (newFilters as any)[key] = value;
      } else {
        delete newFilters[key];
      }
      return newFilters;
    });
  };

  const applyFilters = () => {
    setFilters(pendingFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const clearFilters = () => {
    setPendingFilters({});
    setFilters({});
    setCurrentPage(1); // Reset to first page when clearing filters
    // Navigate to base URL to remove user_id query parameter
    navigate('/transactions/funds');
  };

  const hasFilterChanges = () => {
    return JSON.stringify(filters) !== JSON.stringify(pendingFilters);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const getStatusBadgeColor = (status: Fund['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'matched':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number, currency: Fund['currency']) => {
    const numericAmount = Number(amount);
    if (currency === 'fiat') {
      return `₦${numericAmount.toLocaleString()}`;
    }
    return `${numericAmount.toFixed(3)} SOL`;
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Fund Management</h1>
            <p className="text-gray-600">Manage and monitor all user funds</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>

        {/* User Filter Indicator */}
        {userId && userResponse?.data && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <UserIcon className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">
                      Showing funds for:{' '}
                      <span className="font-semibold">
                        {userResponse.data.full_name}
                      </span>
                    </p>
                    <p className="text-xs text-blue-700">
                      {userResponse.data.email}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                  className="text-blue-700 hover:text-blue-900 hover:bg-blue-100"
                >
                  <X className="h-4 w-4 mr-1" />
                  Show All Funds
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Stats */}
        <FundStatsCards stats={statsData} isLoading={statsLoading} />
        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
              <CardDescription>
                Filter funds by various criteria
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Email Search Filter */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">User Email</label>
                    {pendingFilters.email && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFilterChange('email', undefined)}
                        className="h-auto p-1 text-xs"
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by user email..."
                      value={pendingFilters.email || ''}
                      onChange={e =>
                        handleFilterChange('email', e.target.value)
                      }
                      className="pl-10"
                    />
                  </div>
                </div>
                {/* Status Filter */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Status</label>
                    {pendingFilters.status && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFilterChange('status', undefined)}
                        className="h-auto p-1 text-xs"
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                  <Select
                    value={pendingFilters.status || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'status',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="matched">Matched</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Currency Filter */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Currency</label>
                    {pendingFilters.currency && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleFilterChange('currency', undefined)
                        }
                        className="h-auto p-1 text-xs"
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                  <Select
                    value={pendingFilters.currency || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'currency',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All currencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Currencies</SelectItem>
                      <SelectItem value="fiat">Fiat (NGN)</SelectItem>
                      <SelectItem value="crypto">Crypto (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>{' '}
                {/* Sort by Amount */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      Sort by Amount
                    </label>
                    {pendingFilters.sort_direction && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          handleFilterChange('sort_direction', undefined)
                        }
                        className="h-auto p-1 text-xs"
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                  <Select
                    value={pendingFilters.sort_direction || 'none'}
                    onValueChange={value => {
                      if (value === 'none') {
                        handleFilterChange('sort_direction', undefined);
                        handleFilterChange('sort_field', undefined);
                      } else {
                        handleFilterChange('sort_direction', value);
                        handleFilterChange('sort_field', 'amount');
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="No sorting" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Sorting</SelectItem>
                      <SelectItem value="asc">Amount: Low to High</SelectItem>
                      <SelectItem value="desc">Amount: High to Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Date Range */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">From Date</label>
                  <Input
                    type="date"
                    value={pendingFilters.date_from || ''}
                    onChange={e =>
                      handleFilterChange('date_from', e.target.value)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">To Date</label>
                  <Input
                    type="date"
                    value={pendingFilters.date_to || ''}
                    onChange={e =>
                      handleFilterChange('date_to', e.target.value)
                    }
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters {hasFilterChanges() && '(*)'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Funds Table */}
        <Card>
          <CardHeader>
            <CardTitle>Funds ({funds.length})</CardTitle>
            <CardDescription>All user funds in the system</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>SN</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Payment Matches</TableHead>
                      <TableHead>Growth</TableHead>
                      <TableHead>Start Date</TableHead>
                      <TableHead>Maturity Date</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {funds.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center py-8">
                          <div className="text-gray-500">
                            <TrendingUp className="h-12 w-12 mx-auto mb-2 opacity-50" />
                            <p>No funds found</p>
                            <p className="text-sm">
                              Try adjusting your filters
                            </p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      funds.map((fund, index) => {
                        return (
                          <TableRow key={fund.id}>
                            <TableCell>
                              <span className="text-sm font-medium text-gray-900">
                                {(currentPage - 1) * pageSize + index + 1}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {fund.user?.full_name || 'Unknown User'}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {fund.user?.email || fund.user_id}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="font-medium">
                                {formatAmount(fund.amount, fund.currency)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <Badge
                                className={getStatusBadgeColor(fund.status)}
                              >
                                {fund.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {fund.payment_matches &&
                              fund.payment_matches.length > 0 ? (
                                <div className="space-y-1">
                                  <div className="text-sm font-medium">
                                    Total: {fund.payment_matches.length}
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {[
                                      'pending',
                                      'paid',
                                      'confirmed',
                                      'disputed',
                                      'cancelled',
                                    ].map(status => {
                                      const count =
                                        fund.payment_matches!.filter(
                                          match => match.status === status
                                        ).length;
                                      return count > 0 ? (
                                        <Badge
                                          key={status}
                                          variant="outline"
                                          className={`text-xs whitespace-nowrap ${
                                            status === 'pending'
                                              ? 'bg-yellow-50 text-yellow-700 border-yellow-200'
                                              : status === 'paid'
                                                ? 'bg-blue-50 text-blue-700 border-blue-200'
                                                : status === 'confirmed'
                                                  ? 'bg-green-50 text-green-700 border-green-200'
                                                  : status === 'disputed'
                                                    ? 'bg-red-50 text-red-700 border-red-200'
                                                    : status === 'cancelled'
                                                      ? 'bg-gray-50 text-gray-700 border-gray-200'
                                                      : ''
                                          }`}
                                        >
                                          {status}: {count}
                                        </Badge>
                                      ) : null;
                                    })}
                                  </div>
                                </div>
                              ) : (
                                <span className="text-sm text-gray-500">
                                  No matches
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div>{fund.growth_percentage}%</div>
                                <div className="text-gray-500">
                                  {formatAmount(
                                    fund.growth_amount,
                                    fund.currency
                                  )}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm">
                                {fund.start_date
                                  ? format(
                                      new Date(fund.start_date),
                                      'MMM dd, yyyy'
                                    )
                                  : 'TBD'}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm">
                                {fund.maturity_date
                                  ? format(
                                      new Date(fund.maturity_date),
                                      'MMM dd, yyyy'
                                    )
                                  : 'TBD'}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-500">
                                {format(
                                  new Date(fund.created_at),
                                  'MMM dd, yyyy'
                                )}
                              </span>
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  navigate(`/transactions/funds/${fund.id}`)
                                }
                                className="flex items-center gap-2"
                              >
                                <Eye className="h-4 w-4" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Pagination */}
            <Pagination
              pagination={pagination}
              currentPage={currentPage}
              pageSize={pageSize}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              isLoading={isLoading}
              itemLabel="Rows"
            />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};
