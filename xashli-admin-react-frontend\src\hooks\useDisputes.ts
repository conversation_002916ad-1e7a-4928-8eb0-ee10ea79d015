import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { disputeService } from '../services/dispute';
import type {
  DisputeFilters,
  ResolveDisputeRequest,
  RejectDisputeRequest,
} from '../types/dispute';

// Query Keys
export const disputeKeys = {
  all: ['disputes'] as const,
  lists: () => [...disputeKeys.all, 'list'] as const,
  list: (filters?: DisputeFilters) =>
    [...disputeKeys.lists(), filters] as const,
  details: () => [...disputeKeys.all, 'detail'] as const,
  detail: (id: string) => [...disputeKeys.details(), id] as const,
  stats: (params?: any) => [...disputeKeys.all, 'stats', params] as const,
} as const;

// Hooks for Disputes
export const useDisputes = (filters?: DisputeFilters) => {
  return useQuery({
    queryKey: disputeKeys.list(filters),
    queryFn: () => disputeService.getDisputes(filters),
    staleTime: 30 * 1000, // 30 seconds
  });
};

export const useDispute = (id: string) => {
  return useQuery({
    queryKey: disputeKeys.detail(id),
    queryFn: () => disputeService.getDisputeById(id),
    enabled: !!id,
  });
};

export const useDisputeStats = (params?: {
  date_from?: string;
  date_to?: string;
  currency?: string;
}) => {
  return useQuery({
    queryKey: disputeKeys.stats(params),
    queryFn: () => disputeService.getDisputeStats(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useResolveDispute = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ResolveDisputeRequest }) =>
      disputeService.resolveDispute(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: disputeKeys.all });
      queryClient.invalidateQueries({ queryKey: disputeKeys.detail(id) });
      toast.success('Dispute resolved successfully');
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to resolve dispute'
      );
    },
  });
};

export const useRejectDispute = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: RejectDisputeRequest }) =>
      disputeService.rejectDispute(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: disputeKeys.all });
      queryClient.invalidateQueries({ queryKey: disputeKeys.detail(id) });
      toast.success('Dispute rejected successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to reject dispute');
    },
  });
};
