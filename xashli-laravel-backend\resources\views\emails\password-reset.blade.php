<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reset Your Password - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2d3748;
        }
        .reset-button {
            background: #f7fafc;
            border: 2px solid #4299e1;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            margin: 30px 0;
        }
        .reset-button .label {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #4299e1;
            color: white !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: #3182ce;
        }
        .expiry-notice {
            background: #fff5f5;
            border-left: 4px solid #fc8181;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .expiry-notice .icon {
            color: #e53e3e;
            font-weight: bold;
        }
        .footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            font-size: 14px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
        }
        .security-note {
            background: #f0fff4;
            border-left: 4px solid #68d391;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .security-note .icon {
            color: #38a169;
            font-weight: bold;
        }
        .alternative-link {
            background: #fafafa;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-size: 12px;
            color: #718096;
            word-break: break-all;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 4px;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .btn {
                padding: 12px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>Password Reset</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{ $user->full_name }}! 👋
            </div>
            
            <p>We received a request to reset your password for your {{ config('app.name') }} account. Click the button below to create a new password:</p>
            
            <div class="reset-button">
                <div class="label">Reset Your Password</div>
                <a href="{{ $resetUrl }}" class="btn">Reset Password</a>
            </div>

            <div class="expiry-notice">
                <span class="icon">⏰</span>
                <strong>Important:</strong> This reset link will expire in <strong>1 hour</strong> for security reasons.
            </div>
            
            <div class="security-note">
                <span class="icon">🔒</span>
                <strong>Security Notice:</strong> If you did not request a password reset, please ignore this email. Your password will remain unchanged.
            </div>

            <p><strong>Trouble clicking the button?</strong> Copy and paste the link below into your browser:</p>
            <div class="alternative-link">
                {{ $resetUrl }}
            </div>

            <p>If you continue to have problems, please contact our support team.</p>
        </div>
        
        <div class="footer">
            <p><strong>Best regards,</strong><br>
            The {{ config('app.name') }} Team</p>
            
            <p style="margin-top: 20px; font-size: 12px;">
                This is an automated message. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html>
