<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('platform_fees', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('fund_id');
            $table->uuid('admin_user_id'); // Admin account that collected the fee
            $table->uuid('fee_withdraw_id'); // The withdrawal record created for platform fee
            $table->decimal('fund_amount', 20, 8); // Original fund amount
            $table->decimal('fee_amount', 20, 8); // Collected fee amount
            $table->decimal('fee_percentage', 5, 2); // Platform fee percentage at time of fee capture
            $table->enum('currency', ['fiat', 'crypto']);
            $table->timestamp('collected_at');
            $table->json('metadata')->nullable(); // Store additional data like original match info
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('fund_id')->references('id')->on('funds')->onDelete('cascade');
            $table->foreign('admin_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('fee_withdraw_id')->references('id')->on('withdraws')->onDelete('cascade');

            // Indexes for better query performance
            $table->index('fund_id');
            $table->index('admin_user_id');
            $table->index('currency');
            $table->index('collected_at');
            $table->index(['currency', 'collected_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('platform_fees');
    }
};
