import { useState, useEffect, useCallback } from 'react';
import { withdrawService } from '../../services';
import type { Withdraw, WithdrawFilters, PaginationMeta } from '../../types';

export function useWithdraws(initialFilters: WithdrawFilters = {}) {
  const [withdraws, setWithdraws] = useState<Withdraw[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<WithdrawFilters>(initialFilters);
  const [pagination, setPagination] = useState<PaginationMeta>({
    current_page: 1,
    last_page: 1,
    per_page: 10,
    total: 0,
    from: 0,
    to: 0,
  });

  const fetchWithdraws = useCallback(
    async (currentFilters: WithdrawFilters = filters) => {
      setLoading(true);
      setError(null);
      try {
        const response = await withdrawService.getWithdraws(currentFilters);
        if (response.status === 'success' && response.data) {
          setWithdraws(response.data.withdraws || []);
          setPagination(response.data.pagination);
        } else {
          setError(response.message || 'Failed to fetch withdraws');
          setWithdraws([]);
        }
      } catch (err) {
        setError('An unexpected error occurred');
        setWithdraws([]);
      } finally {
        setLoading(false);
      }
    },
    [filters]
  );

  // Initial data fetch
  useEffect(() => {
    fetchWithdraws();
  }, [fetchWithdraws]);

  // Update filters and refetch data
  const updateFilters = useCallback(
    (newFilters: WithdrawFilters) => {
      const updatedFilters = { ...filters, ...newFilters };
      setFilters(updatedFilters);
      fetchWithdraws(updatedFilters);
    },
    [filters, fetchWithdraws]
  );

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({});
    fetchWithdraws({});
  }, [fetchWithdraws]);

  // Pagination handlers
  const goToPage = useCallback(
    (page: number) => {
      const newFilters = { ...filters, page };
      setFilters(newFilters);
      fetchWithdraws(newFilters);
    },
    [filters, fetchWithdraws]
  );

  const changePageSize = useCallback(
    (pageSize: number) => {
      const newFilters = { ...filters, per_page: pageSize, page: 1 };
      setFilters(newFilters);
      fetchWithdraws(newFilters);
    },
    [filters, fetchWithdraws]
  );

  return {
    withdraws,
    loading,
    error,
    filters,
    pagination,
    updateFilters,
    resetFilters,
    refetch: fetchWithdraws,
    goToPage,
    changePageSize,
  };
}
