import React from 'react';
import { Button } from '../ui/button';
import { Bell, User, LogOut, Menu } from 'lucide-react';
import { useAuth, useSidebar } from '../../contexts';
import { showToast } from '../../utils';

export const TopBar: React.FC = () => {
  const { user, logout } = useAuth();
  const { toggle, isMobile } = useSidebar();

  const handleLogout = async () => {
    try {
      await logout();
      showToast.auth.logoutSuccess();
    } catch (error) {
      showToast.error('Logout failed. Please try again.');
    }
  };

  return (
    <header className="h-16 bg-white border-b border-brand-grey-200 flex items-center px-6">
      {/* Left Section - Mobile Menu */}
      <div className="flex items-center">
        {isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggle}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Center Section - Can be used for title or breadcrumb if needed */}
      <div className="flex-1"></div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            3
          </span>
        </Button>

        {/* User Profile */}
        <div className="flex items-center space-x-3">
          <div className="text-right">
            <p className="text-sm font-medium text-brand-black">
              {user?.full_name || user?.email}
            </p>
            <p className="text-xs text-brand-grey-500">Administrator</p>
          </div>

          <div className="h-8 w-8 bg-brand-gold-500 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-black" />
          </div>
        </div>

        {/* Logout - Aligned to the end */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="border-brand-grey-300 hover:bg-brand-grey-100 ml-2"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>
    </header>
  );
};
