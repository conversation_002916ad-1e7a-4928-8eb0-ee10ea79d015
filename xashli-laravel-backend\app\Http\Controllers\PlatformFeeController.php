<?php

namespace App\Http\Controllers;

use App\Models\PlatformFee;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlatformFeeController extends Controller
{
    /**
     * Display a listing of platform fees.
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency' => 'sometimes|in:fiat,crypto',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $query = PlatformFee::with([
            'fund:id,user_id,amount,currency,status,created_at',
            'fund.user:id,full_name,email',
            'admin:id,full_name,email',
            'withdraw:id,status,created_at',
        ])->orderBy('collected_at', 'desc');

        // Apply filters
        if ($request->has('currency')) {
            $query->currency($request->currency);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        // Pagination
        $user = auth()->user();
        $isAdmin = $user && $user->isAdmin();
        $perPage = min($request->get('per_page', 20), $isAdmin ? 100 : 50);
        $paginatedFees = $query->paginate($perPage);

        // Structure response with separate fees and pagination
        $response = [
            'platformFees' => $paginatedFees->items(),
            'pagination' => [
                'current_page' => $paginatedFees->currentPage(),
                'last_page' => $paginatedFees->lastPage(),
                'per_page' => $paginatedFees->perPage(),
                'total' => $paginatedFees->total(),
                'from' => $paginatedFees->firstItem(),
                'to' => $paginatedFees->lastItem(),
            ],
        ];

        return $this->success($response, 'Platform fees retrieved successfully');
    }

    /**
     * Get platform fee statistics and summary.
     */
    public function statistics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'user_id' => 'sometimes|uuid',
            'email' => 'sometimes|string',
            'currency' => 'sometimes|in:fiat,crypto',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Base query for platform fees
        $query = PlatformFee::query();

        // Apply all filters to the base query
        if ($request->has('user_id') && ! empty($request->user_id)) {
            $query->whereHas('fund', function ($q) use ($request) {
                $q->where('user_id', $request->user_id);
            });
        }

        if ($request->has('email') && ! empty($request->email)) {
            $query->whereHas('fund.user', function ($q) use ($request) {
                $q->where('email', 'like', '%' . $request->email . '%');
            });
        }

        if ($request->has('currency')) {
            $query->where('currency', $request->currency);
        }

        if ($request->has('start_date')) {
            $query->whereDate('collected_at', '>=', $request->start_date);
        }

        if ($request->has('end_date')) {
            $query->whereDate('collected_at', '<=', $request->end_date);
        }

        // Get all platform fees matching the criteria
        $fees = $query->get();

        $statistics = [
            'counts' => [
                'total' => $fees->count(),
                'fiat' => $fees->where('currency', 'fiat')->count(),
                'crypto' => $fees->where('currency', 'crypto')->count(),
            ],
            'amounts' => [
                'total' => [
                    'fiat' => round($fees->where('currency', 'fiat')->sum('fee_amount'), 2),
                    'crypto' => round($fees->where('currency', 'crypto')->sum('fee_amount'), 6),
                ],
            ],
        ];

        return $this->success($statistics, 'Platform fee statistics retrieved successfully');
    }

    /**
     * Display details of a specific fee.
     */
    public function show(string $id): JsonResponse
    {
        $fee = PlatformFee::with([
            'fund:id,user_id,amount,currency,platform_fee_percentage,status,created_at,maturity_date',
            'fund.user:id,full_name,email,phone',
            'fund.paymentMethod:id,type,bank_name,account_number,account_name,wallet_address',
            'admin:id,full_name,email',
            'withdraw:id,status,created_at,updated_at',
        ])->find($id);

        if (! $fee) {
            return $this->notFound('Platform fee not found');
        }

        return $this->success($fee, 'Platform fee retrieved successfully');
    }
}
