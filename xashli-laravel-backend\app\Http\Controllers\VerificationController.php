<?php

namespace App\Http\Controllers;

use App\Models\VerificationCode;
use App\Services\EmailService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VerificationController extends Controller
{
    protected EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Send email verification code.
     */
    public function sendEmailVerification(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            if ($user->hasVerifiedEmail()) {
                return $this->error('Email is already verified', 400);
            }

            // Create new verification code
            $verificationCode = VerificationCode::generate($user, 'email');

            // Send via email service
            $sent = $this->emailService->sendVerificationCode($user, $verificationCode->code);

            if (! $sent) {
                return $this->serverError('Failed to send verification email');
            }

            return $this->success([
                'message' => 'Verification code sent to your email',
                'expires_at' => $verificationCode->expires_at,
            ], 'Email verification code sent successfully');

        } catch (\Exception $e) {
            return $this->serverError('Failed to send verification code: ' . $e->getMessage());
        }
    }

    /**
     * Verify email verification code.
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = auth()->user();

            if ($user->hasVerifiedEmail()) {
                return $this->error('Email is already verified', 400);
            }

            $verificationCode = VerificationCode::findValidCode($user, 'email', $request->code);

            if (! $verificationCode) {
                return $this->error('Invalid or expired verification code', 400);
            }

            // Verify the code
            $verified = $verificationCode->verify();

            if (! $verified) {
                return $this->error('Failed to verify code', 400);
            }

            return $this->success([
                'user' => $user->fresh(),
            ], 'Email verified successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to verify email: ' . $e->getMessage());
        }
    }

    /**
     * Get verification status for the authenticated user.
     */
    public function getVerificationStatus(): JsonResponse
    {
        $user = auth()->user();

        return $this->success([
            'email_verified' => $user->hasVerifiedEmail(),
            'email_verified_at' => $user->email_verified_at,
        ], 'Verification status retrieved successfully');
    }
}
