import { Button } from '@/components/ui/Button';
import { Accordion, AccordionItem } from '@/components/ui/Accordion';
import { useNavigate } from 'react-router-dom';
import { useState } from 'react';
import {
  ArrowRight,
  Shield,
  Users,
  TrendingUp,
  UserPlus,
  Wallet,
  Gift,
  Star,
  Check,
  MessageCircle,
  Send,
  X,
  Info,
} from 'lucide-react';
import heroImg from '@/assets/hero-img.jpg';
import xashliLogo from '@/assets/xashli_logo.png';
import {
  PLATFORM_CONFIG,
  PRIVILEGES_DATA,
  formatCurrencyAmount,
  type PrivilegeData,
} from '@/data/privileges';

export function HomePage() {
  const navigate = useNavigate();
  const [selectedPrivilege, setSelectedPrivilege] =
    useState<PrivilegeData | null>(null);

  // Get icon component by name
  const getIconComponent = (iconName: string) => {
    const icons: Record<string, any> = {
      Users,
      Star,
      Shield,
    };
    return icons[iconName] || Users;
  };

  // Helper function to format maturity period
  const formatMaturityPeriod = (privilege: PrivilegeData) => {
    if (privilege.id.startsWith('elite')) {
      return '7*/10 days';
    }
    return `${privilege.benefits.maturity_period} days`;
  };

  // Modal component for privilege details
  const PrivilegeModal = ({
    privilege,
    onClose,
  }: {
    privilege: PrivilegeData;
    onClose: () => void;
  }) => {
    const Icon = getIconComponent(privilege.icon);
    return (
      <div className='fixed inset-0 bg-black/60 backdrop-blur-md z-50 p-4 overflow-y-auto'>
        <div className='min-h-screen flex items-start justify-center pt-8 pb-8'>
          <div className='bg-background border border-border rounded-lg max-w-md w-full shadow-2xl'>
            {' '}
            {/* Header */}
            <div className='bg-primary p-6 rounded-t-lg border-b border-primary-dark'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-12 h-12 bg-primary-foreground/20 backdrop-blur-sm rounded-lg flex items-center justify-center mr-4'>
                    <Icon className='h-6 w-6 text-primary-foreground' />
                  </div>
                  <div>
                    <h3 className='text-xl font-bold text-primary-foreground'>
                      {privilege.name}
                    </h3>
                    <p className='text-sm text-primary-foreground/80'>
                      {privilege.badge}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className='text-primary-foreground/70 hover:text-primary-foreground transition-colors p-1 rounded-md hover:bg-primary-foreground/10'
                >
                  <X className='h-6 w-6' />
                </button>
              </div>
            </div>
            {/* Content */}
            <div className='p-6 bg-background'>
              <p className='text-foreground-secondary mb-6'>
                {privilege.description}
              </p>
              {/* Benefits */}
              <div className='mb-6'>
                <h4 className='font-semibold text-foreground mb-3'>Benefits</h4>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-foreground-secondary'>
                      Max Fiat Fund:
                    </span>
                    <span className='font-medium text-foreground'>
                      {formatCurrencyAmount(
                        privilege.benefits.max_fiat_fund,
                        'fiat'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-foreground-secondary'>
                      Max Crypto Fund:
                    </span>
                    <span className='font-medium text-foreground'>
                      {formatCurrencyAmount(
                        privilege.benefits.max_crypto_fund,
                        'crypto'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-foreground-secondary'>
                      Maturity Period:
                    </span>
                    <span className='font-medium text-foreground'>
                      {formatMaturityPeriod(privilege)}
                    </span>
                  </div>
                  {privilege.id.startsWith('elite') && (
                    <div className='text-xs text-primary mt-1 font-medium'>
                      * 7 days with Premium privilege, 10 days otherwise
                    </div>
                  )}
                  {privilege.benefits.multiplier && (
                    <div className='flex justify-between'>
                      <span className='text-foreground-secondary'>
                        Fund Limit Multiplier:
                      </span>
                      <span className={`font-bold ${privilege.color.primary}`}>
                        {privilege.benefits.multiplier}x
                      </span>
                    </div>
                  )}
                </div>
              </div>
              {/* Requirements */}
              <div className='mb-6'>
                <h4 className='font-semibold text-foreground mb-3'>
                  Requirements
                </h4>
                <p className='text-foreground-secondary mb-3'>
                  {privilege.requirements.description}
                </p>

                {privilege.requirements.conditions && (
                  <div className='space-y-4'>
                    {privilege.requirements.conditions.transactions && (
                      <div className='border border-border rounded-lg p-4 bg-background/50'>
                        <h5 className='font-medium text-foreground mb-2'>
                          Transaction Path:{' '}
                          {privilege.requirements.conditions.transactions.count}{' '}
                          successful transactions
                        </h5>
                        <ul className='text-sm text-foreground-secondary space-y-1'>
                          {privilege.requirements.conditions.transactions.conditions.map(
                            (condition, index) => (
                              <li key={index} className='flex items-start'>
                                <Check className='h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0' />
                                {condition}
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {privilege.requirements.conditions.downlines && (
                      <div className='border border-border rounded-lg p-4 bg-background/50'>
                        <h5 className='font-medium text-foreground mb-2'>
                          Referral Path:{' '}
                          {privilege.requirements.conditions.downlines.count}{' '}
                          active referrers
                        </h5>
                        <p className='text-sm text-foreground-secondary'>
                          {
                            privilege.requirements.conditions.downlines
                              .requirement
                          }
                        </p>
                      </div>
                    )}

                    {privilege.requirements.conditions.referral_funding && (
                      <div className='border border-border rounded-lg p-4 bg-background/50'>
                        <h5 className='font-medium text-foreground mb-2'>
                          Referral Funding Requirement
                        </h5>
                        <p className='text-sm text-foreground-secondary'>
                          Your direct referrers must collectively fund at least{' '}
                          <span className='font-medium text-foreground'>
                            {privilege.requirements.conditions.referral_funding
                              .fiat_amount &&
                              formatCurrencyAmount(
                                privilege.requirements.conditions
                                  .referral_funding.fiat_amount,
                                'fiat'
                              )}
                            {privilege.requirements.conditions.referral_funding
                              .fiat_amount &&
                              privilege.requirements.conditions.referral_funding
                                .crypto_amount &&
                              ' or '}
                            {privilege.requirements.conditions.referral_funding
                              .crypto_amount &&
                              formatCurrencyAmount(
                                privilege.requirements.conditions
                                  .referral_funding.crypto_amount,
                                'crypto'
                              )}
                          </span>
                        </p>
                      </div>
                    )}

                    {privilege.requirements.type === 'either' && (
                      <div className='bg-primary/10 border border-primary/30 rounded-lg p-3'>
                        <p className='text-sm text-primary'>
                          <Info className='h-4 w-4 inline mr-1' />
                          You can achieve this level through either path above.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>{' '}
              {/* Action Button */}
              <Button
                onClick={() => {
                  onClose();
                  navigate('/auth/register');
                }}
                className='w-full bg-primary hover:bg-primary/90 text-white'
              >
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className='min-h-screen bg-background'>
      {/* Modal */}
      {selectedPrivilege && (
        <PrivilegeModal
          privilege={selectedPrivilege}
          onClose={() => setSelectedPrivilege(null)}
        />
      )}

      {/* Header */}
      <header className='fixed top-0 left-0 right-0 z-20 bg-black/95 backdrop-blur-md shadow-lg transition-all duration-300'>
        <div className='container mx-auto px-8 lg:px-12 py-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <div className='rounded-lg p-2'>
                <img
                  src={xashliLogo}
                  alt='Xashli Logo'
                  className='h-8 w-auto'
                />
              </div>
            </div>
            <div className='flex items-center'>
              <Button
                variant='outline'
                onClick={() => navigate('/auth/login')}
                className='text-primary border-primary hover:bg-primary hover:text-black'
              >
                Login
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section
        className='relative h-screen flex items-center justify-center bg-cover bg-center bg-no-repeat pt-20'
        style={{ backgroundImage: `url(${heroImg})` }}
      >
        {/* Dark gradient overlay */}
        <div className='absolute inset-0 bg-gradient-to-r from-black/70 via-black/60 to-black/70'></div>

        {/* Hero content */}
        <div className='relative z-10 container mx-auto px-8 lg:px-12 text-center text-white'>
          <div className='max-w-4xl mx-auto'>
            <h1 className='text-4xl md:text-6xl font-bold mb-6'>
              <span className='text-primary block mb-2'>XASHLI</span>
              Grow Your Funds Smarter, Together
            </h1>{' '}
            <p className='text-lg md:text-xl mb-8 leading-relaxed max-w-3xl mx-auto text-gray-200'>
              Turn your idle funds into growing earnings. Earn{' '}
              {PLATFORM_CONFIG.growth_rates.fiat}% on Naira or{' '}
              {PLATFORM_CONFIG.growth_rates.crypto}% on Solana in just 10 days.
              Continuous growth through our community-powered recommitment
              system.
            </p>
            <div className='flex flex-col sm:flex-row gap-4 justify-center'>
              <Button
                size='lg'
                onClick={() => navigate('/auth/register')}
                className='group bg-primary hover:bg-primary/90'
              >
                Create Account
              </Button>
              <Button
                size='lg'
                variant='outline'
                onClick={() => navigate('/auth/login')}
                className='border-white text-white hover:bg-white hover:text-black'
              >
                Login to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className='container mx-auto px-8 lg:px-12 py-16'>
        <div className='max-w-6xl mx-auto'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
              About Xashli
            </h2>
            <p className='text-lg text-foreground-secondary max-w-3xl mx-auto'>
              Xashli is a community-powered financial platform where members
              help each other grow their funds through strategic cycles and
              mutual support.
            </p>
          </div>

          <div className='grid md:grid-cols-2 gap-12 items-center'>
            <div>
              <h3 className='text-2xl font-bold text-foreground mb-6'>
                Real Community, Real Returns
              </h3>
              <ul className='space-y-4'>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-3 flex-shrink-0' />
                  <span className='text-foreground'>
                    No bots or artificial manipulation
                  </span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-3 flex-shrink-0' />
                  <span className='text-foreground'>
                    Transparent cycle tracking system
                  </span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-3 flex-shrink-0' />
                  <span className='text-foreground'>
                    Community-driven growth model
                  </span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-3 flex-shrink-0' />
                  <span className='text-foreground'>
                    Support for both fiat and cryptocurrency
                  </span>
                </li>
              </ul>
            </div>

            <div className='grid grid-cols-2 gap-4 md:gap-6'>
              <div className='bg-primary/5 p-4 md:p-6 rounded-lg text-center'>
                <div className='text-2xl md:text-3xl font-bold text-primary mb-2'>
                  {PLATFORM_CONFIG.growth_rates.fiat}%
                </div>
                <div className='text-xs md:text-sm text-foreground-secondary'>
                  Fiat ROI
                </div>
                <div className='text-xs text-foreground-secondary mt-1'>
                  in 10 days
                </div>
              </div>
              <div className='bg-primary/5 p-4 md:p-6 rounded-lg text-center'>
                <div className='text-2xl md:text-3xl font-bold text-primary mb-2'>
                  {PLATFORM_CONFIG.growth_rates.crypto}%
                </div>
                <div className='text-xs md:text-sm text-foreground-secondary'>
                  Crypto ROI
                </div>
                <div className='text-xs text-foreground-secondary mt-1'>
                  in 10 days
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section className='bg-primary/5 py-16'>
        <div className='container mx-auto px-8 lg:px-12 text-center'>
          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto'>
            <div className='bg-background p-6 rounded-lg shadow-sm'>
              <TrendingUp className='h-12 w-12 text-primary mx-auto mb-4' />
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                High Returns
              </h3>
              <p className='text-foreground-secondary'>
                Fiat: {PLATFORM_CONFIG.growth_rates.fiat}% ROI in 10 days
                <br />
                Crypto: {PLATFORM_CONFIG.growth_rates.crypto}% ROI in 10 days
              </p>
            </div>
            <div className='bg-background p-6 rounded-lg shadow-sm'>
              <Shield className='h-12 w-12 text-primary mx-auto mb-4' />
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                Secure & Transparent
              </h3>
              <p className='text-foreground-secondary'>
                No bots, no manipulation. Transparent rules and secure
                transactions.
              </p>
            </div>
            <div className='bg-background p-6 rounded-lg shadow-sm'>
              <Users className='h-12 w-12 text-primary mx-auto mb-4' />
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                Community-Powered
              </h3>
              <p className='text-foreground-secondary'>
                Built on community trust with automated cycle tracking.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className='container mx-auto px-8 lg:px-12 py-16'>
        <div className='text-center mb-16'>
          <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
            How It Works
          </h2>
          <p className='text-lg text-foreground-secondary max-w-3xl mx-auto'>
            Get started in 3 simple steps and watch your funds grow through our
            proven community system
          </p>
        </div>{' '}
        <div className='grid md:grid-cols-3 gap-8 max-w-5xl mx-auto'>
          <div className='text-center'>
            <div className='w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6'>
              <UserPlus className='h-8 w-8 text-white' />
            </div>
            <h3 className='text-xl font-bold text-foreground mb-4'>
              1. Create Account
            </h3>
            <p className='text-foreground-secondary'>
              Sign up and complete your profile setup to join our community
            </p>
          </div>

          <div className='text-center'>
            <div className='w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6'>
              <Wallet className='h-8 w-8 text-white' />
            </div>
            <h3 className='text-xl font-bold text-foreground mb-4'>
              2. Fund Your Account
            </h3>
            <p className='text-foreground-secondary'>
              Add funds to start your first cycle - choose between Naira or
              Solana
            </p>
          </div>

          <div className='text-center'>
            <div className='w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-6'>
              <TrendingUp className='h-8 w-8 text-white' />
            </div>
            <h3 className='text-xl font-bold text-foreground mb-4'>
              3. Watch It Grow
            </h3>
            <p className='text-foreground-secondary'>
              Your funds mature in 10 days (7 days for premium accounts) and
              grow by {PLATFORM_CONFIG.growth_rates.fiat}%-
              {PLATFORM_CONFIG.growth_rates.crypto}%
            </p>
          </div>
        </div>
        {/* Recommitment Info */}
        <div className='mt-16 max-w-4xl mx-auto'>
          <div className='bg-primary/5 p-6 rounded-xl border border-primary/20'>
            <div className='text-center mb-4'>
              <div className='w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3'>
                <Info className='h-6 w-6 text-primary' />
              </div>
              <h4 className='text-lg font-semibold text-foreground mb-2'>
                Important: Recommitment Requirement
              </h4>
            </div>
            <p className='text-foreground-secondary text-center max-w-2xl mx-auto'>
              To withdraw your matured funds, you must first create a new fund
              (recommitment) with an amount equal to or greater than your
              original funding. This ensures continuous platform growth and
              maintains the community cycle.
            </p>
          </div>
        </div>
      </section>

      {/* Investment Options */}
      <section className='bg-primary/5 py-16'>
        <div className='container mx-auto px-8 lg:px-12'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
              Choose Your Investment Path
            </h2>
            <p className='text-lg text-foreground-secondary'>
              Start with what works best for you
            </p>
          </div>

          <div className='grid md:grid-cols-2 gap-8 max-w-4xl mx-auto'>
            <div className='bg-background p-8 rounded-lg shadow-lg border'>
              <h3 className='text-2xl font-bold text-foreground mb-4'>
                Fiat (Naira)
              </h3>
              <div className='text-4xl font-bold text-primary mb-2'>
                {PLATFORM_CONFIG.growth_rates.fiat}% ROI
              </div>
              <div className='text-lg text-foreground-secondary mb-6'>
                in 10 days
              </div>
              <ul className='space-y-3 mb-8'>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Bank Transfer</span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Minimum: ₦10,000</span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Local currency convenience</span>
                </li>
              </ul>
              <Button
                variant='outline'
                className='w-full'
                onClick={() => navigate('/auth/register')}
              >
                Start with Naira
              </Button>
            </div>

            <div className='bg-background p-8 rounded-lg shadow-lg border border-primary'>
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-2xl font-bold text-foreground'>
                  Crypto (Solana)
                </h3>
                <Star className='h-6 w-6 text-yellow-500' />
              </div>
              <div className='text-4xl font-bold text-primary mb-2'>
                {PLATFORM_CONFIG.growth_rates.crypto}% ROI
              </div>
              <div className='text-lg text-foreground-secondary mb-6'>
                in 10 days
              </div>
              <ul className='space-y-3 mb-8'>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Fast blockchain payments</span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Minimum: 0.1 SOL</span>
                </li>
                <li className='flex items-center'>
                  <Check className='h-5 w-5 text-green-500 mr-2' />
                  <span>Higher returns</span>
                </li>
              </ul>
              <Button
                className='w-full bg-primary'
                onClick={() => navigate('/auth/register')}
              >
                Start with Solana
              </Button>
            </div>
          </div>

          <div className='text-center mt-8'>
            <p className='text-foreground-secondary'>
              <Gift className='h-4 w-4 inline mr-2' />
              Join now and enjoy{' '}
              <span className='font-semibold text-primary'>
                7-day maturity
              </span>{' '}
              for all your future stakes!
            </p>
          </div>
        </div>
      </section>

      {/* Account Privileges */}
      <section className='container mx-auto px-8 lg:px-12 py-16'>
        <div className='text-center mb-16'>
          <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
            Account Privileges
          </h2>
          <p className='text-lg text-foreground-secondary max-w-3xl mx-auto'>
            Unlock exclusive benefits and faster returns by building your
            referral network and maintaining consistent staking activity
          </p>
        </div>

        {/* Desktop: 3 per row layout */}
        <div className='hidden lg:block'>
          {' '}
          {/* First row: Standard, Premium, Elite 1 */}
          <div className='grid lg:grid-cols-3 gap-6 max-w-6xl mx-auto mb-8'>
            {PRIVILEGES_DATA.slice(0, 3).map(privilege => {
              const Icon = getIconComponent(privilege.icon);
              return (
                <div
                  key={privilege.id}
                  className={`bg-background p-6 rounded-lg shadow-lg border ${privilege.color.border} relative hover:shadow-xl transition-shadow`}
                >
                  {privilege.badge !== 'Default level' && (
                    <div className='absolute -top-3 left-1/2 transform -translate-x-1/2'>
                      <span
                        className={`${privilege.color.primary.replace('text-', 'bg-').replace('-600', '-500')} text-white px-3 py-1 rounded-full text-xs font-medium`}
                      >
                        {privilege.badge}
                      </span>
                    </div>
                  )}
                  <div className='flex items-center mb-4'>
                    <div
                      className={`w-10 h-10 ${privilege.color.bg} rounded-lg flex items-center justify-center mr-3`}
                    >
                      <Icon className={`h-5 w-5 ${privilege.color.primary}`} />
                    </div>
                    <div>
                      <h3 className='text-lg font-bold text-foreground'>
                        {privilege.name}
                      </h3>
                      <p className='text-xs text-foreground-secondary'>
                        {privilege.benefits.multiplier
                          ? `${privilege.benefits.multiplier}x multiplier`
                          : privilege.badge}
                      </p>
                    </div>
                  </div>
                  <div className='space-y-3 mb-6'>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Fiat Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.multiplier ? privilege.color.primary : ''}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_fiat_fund,
                          'fiat'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Crypto Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.multiplier ? privilege.color.primary : ''}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_crypto_fund,
                          'crypto'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Maturity Period
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.maturity_period === 7 ? privilege.color.primary : ''}`}
                      >
                        {privilege.benefits.maturity_period} days
                      </span>
                    </div>
                  </div>{' '}
                  <div className='space-y-2'>
                    <Button
                      variant='outline'
                      onClick={() => setSelectedPrivilege(privilege)}
                      className='w-full border-primary text-primary hover:bg-primary hover:text-white transition-colors'
                    >
                      <Info className='h-4 w-4 mr-2' />
                      Check Criteria
                    </Button>
                    <Button
                      onClick={() => navigate('/auth/register')}
                      className={`w-full ${
                        privilege.id === 'standard'
                          ? 'bg-gray-500 hover:bg-gray-600'
                          : privilege.color.primary.includes('amber')
                            ? 'bg-amber-500 hover:bg-amber-600'
                            : privilege.color.primary.includes('yellow')
                              ? 'bg-yellow-500 hover:bg-yellow-600'
                              : ''
                      }`}
                      variant={
                        privilege.id === 'standard' ? 'outline' : 'primary'
                      }
                    >
                      {privilege.id === 'standard'
                        ? 'Get Started'
                        : `Achieve ${privilege.name}`}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
          {/* Second row: Elite 2, Elite 3, Elite 4 */}
          <div className='grid lg:grid-cols-3 gap-6 max-w-6xl mx-auto'>
            {PRIVILEGES_DATA.slice(3).map(privilege => {
              const Icon = getIconComponent(privilege.icon);
              return (
                <div
                  key={privilege.id}
                  className={`bg-background p-6 rounded-lg shadow-lg border ${privilege.color.border} relative hover:shadow-xl transition-shadow`}
                >
                  <div className='absolute -top-3 left-1/2 transform -translate-x-1/2'>
                    <span
                      className={`${privilege.color.primary.replace('text-', 'bg-').replace('-600', '-500')} text-white px-3 py-1 rounded-full text-xs font-medium`}
                    >
                      {privilege.badge}
                    </span>
                  </div>
                  <div className='flex items-center mb-4'>
                    <div
                      className={`w-10 h-10 ${privilege.color.bg} rounded-lg flex items-center justify-center mr-3`}
                    >
                      <Icon className={`h-5 w-5 ${privilege.color.primary}`} />
                    </div>
                    <div>
                      <h3 className='text-lg font-bold text-foreground'>
                        {privilege.name}
                      </h3>
                      <p className='text-xs text-foreground-secondary'>
                        {privilege.benefits.multiplier}x multiplier
                      </p>
                    </div>
                  </div>
                  <div className='space-y-3 mb-6'>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Fiat Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.color.primary}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_fiat_fund,
                          'fiat'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Crypto Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.color.primary}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_crypto_fund,
                          'crypto'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Maturity Period
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.color.primary}`}
                      >
                        {formatMaturityPeriod(privilege)}
                      </span>
                    </div>
                  </div>{' '}
                  <div className='space-y-2'>
                    <Button
                      variant='outline'
                      onClick={() => setSelectedPrivilege(privilege)}
                      className='w-full border-primary text-primary hover:bg-primary hover:text-white transition-colors'
                    >
                      <Info className='h-4 w-4 mr-2' />
                      Check Criteria
                    </Button>
                    <Button
                      onClick={() => navigate('/auth/register')}
                      className={`w-full ${
                        privilege.color.primary.includes('orange')
                          ? 'bg-orange-500 hover:bg-orange-600'
                          : privilege.color.primary.includes('red')
                            ? 'bg-red-500 hover:bg-red-600'
                            : privilege.color.primary.includes('purple')
                              ? 'bg-purple-500 hover:bg-purple-600'
                              : ''
                      }`}
                    >
                      Achieve {privilege.name}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {/* Mobile: Horizontal scroll */}
        <div className='lg:hidden overflow-x-auto pb-4 pt-4'>
          <div className='flex gap-4 w-max px-8'>
            {PRIVILEGES_DATA.map(privilege => {
              const Icon = getIconComponent(privilege.icon);
              return (
                <div
                  key={privilege.id}
                  className={`bg-background p-6 rounded-lg shadow-lg border ${privilege.color.border} w-72 flex-shrink-0 relative hover:shadow-xl transition-shadow`}
                >
                  {privilege.badge !== 'Default level' && (
                    <div className='absolute -top-3 left-1/2 transform -translate-x-1/2'>
                      <span
                        className={`${privilege.color.primary.replace('text-', 'bg-').replace('-600', '-500')} text-white px-3 py-1 rounded-full text-xs font-medium`}
                      >
                        {privilege.badge}
                      </span>
                    </div>
                  )}
                  <div className='flex items-center mb-4'>
                    <div
                      className={`w-10 h-10 ${privilege.color.bg} rounded-lg flex items-center justify-center mr-3`}
                    >
                      <Icon className={`h-5 w-5 ${privilege.color.primary}`} />
                    </div>
                    <div>
                      <h3 className='text-lg font-bold text-foreground'>
                        {privilege.name}
                      </h3>
                      <p className='text-xs text-foreground-secondary'>
                        {privilege.benefits.multiplier
                          ? `${privilege.benefits.multiplier}x multiplier`
                          : privilege.badge}
                      </p>
                    </div>
                  </div>
                  <div className='space-y-3 mb-6'>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Fiat Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.multiplier ? privilege.color.primary : ''}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_fiat_fund,
                          'fiat'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Max Crypto Fund
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.multiplier ? privilege.color.primary : ''}`}
                      >
                        {formatCurrencyAmount(
                          privilege.benefits.max_crypto_fund,
                          'crypto'
                        )}
                      </span>
                    </div>
                    <div className='flex items-center justify-between py-1 border-b border-border'>
                      <span className='text-xs text-foreground-secondary'>
                        Maturity Period
                      </span>
                      <span
                        className={`text-sm font-medium ${privilege.benefits.maturity_period === 7 ? privilege.color.primary : ''}`}
                      >
                        {privilege.benefits.maturity_period} days
                      </span>
                    </div>
                  </div>{' '}
                  <div className='space-y-2'>
                    <Button
                      variant='outline'
                      onClick={() => setSelectedPrivilege(privilege)}
                      className='w-full border-primary text-primary hover:bg-primary hover:text-white transition-colors'
                    >
                      <Info className='h-4 w-4 mr-2' />
                      Check Criteria
                    </Button>
                    <Button
                      onClick={() => navigate('/auth/register')}
                      className={`w-full ${
                        privilege.id === 'standard'
                          ? 'bg-gray-500 hover:bg-gray-600'
                          : privilege.color.primary.includes('amber')
                            ? 'bg-amber-500 hover:bg-amber-600'
                            : privilege.color.primary.includes('yellow')
                              ? 'bg-yellow-500 hover:bg-yellow-600'
                              : privilege.color.primary.includes('orange')
                                ? 'bg-orange-500 hover:bg-orange-600'
                                : privilege.color.primary.includes('red')
                                  ? 'bg-red-500 hover:bg-red-600'
                                  : privilege.color.primary.includes('purple')
                                    ? 'bg-purple-500 hover:bg-purple-600'
                                    : ''
                      }`}
                      variant={
                        privilege.id === 'standard' ? 'outline' : 'primary'
                      }
                    >
                      {privilege.id === 'standard'
                        ? 'Get Started'
                        : `Achieve ${privilege.name}`}
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Additional Info */}
        <div className='text-center mt-12'>
          <p className='text-foreground-secondary max-w-2xl mx-auto'>
            Your account privileges are automatically upgraded based on your
            activity and performance. Level requirements can be met through
            either transaction consistency or referral network building.
          </p>
        </div>
      </section>

      {/* Referral System */}
      <section className='bg-primary/5 py-16'>
        <div className='container mx-auto px-8 lg:px-12'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
              Referral System: Earn as You Share
            </h2>
            <p className='text-lg text-foreground-secondary'>
              Earn passively just by referring others
            </p>
          </div>

          <div className='grid md:grid-cols-3 gap-8 max-w-4xl mx-auto'>
            <div className='bg-background p-6 rounded-lg shadow-sm text-center'>
              <div className='w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4'>
                <UserPlus className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                Direct Referrals
              </h3>
              <p className='text-foreground-secondary mb-4'>
                Earn from every person you directly refer to the platform
              </p>
              <div className='text-2xl font-bold text-primary'>Immediate</div>
            </div>

            <div className='bg-background p-6 rounded-lg shadow-sm text-center'>
              <div className='w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4'>
                <TrendingUp className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                Passive Income
              </h3>
              <p className='text-foreground-secondary mb-4'>
                Earn a percentage from each successful cycle of your referrals
              </p>
              <div className='text-2xl font-bold text-green-500'>10%</div>
            </div>

            <div className='bg-background p-6 rounded-lg shadow-sm text-center'>
              <div className='w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Users className='h-8 w-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                Build Your Network
              </h3>
              <p className='text-foreground-secondary mb-4'>
                Larger networks unlock higher account privileges and benefits
              </p>
              <div className='text-2xl font-bold text-purple-500'>∞</div>
            </div>
          </div>

          <div className='text-center mt-8'>
            <p className='text-foreground-secondary'>
              <Info className='h-4 w-4 inline mr-2' />
              Only direct referrals earn you commissions - simple and
              transparent
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className='container mx-auto px-8 lg:px-12 py-16'>
        <div className='text-center mb-12'>
          <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
            Frequently Asked Questions
          </h2>
          <p className='text-lg text-foreground-secondary'>
            Got questions? We've got answers.
          </p>
        </div>

        <div className='max-w-4xl mx-auto'>
          <Accordion>
            <AccordionItem title='How does the cycle system work?'>
              <p className='text-foreground-secondary'>
                Each cycle lasts 10 days (7 days for premium accounts). You fund
                your account, get matched with other participants, and receive
                your returns at the end of the cycle.
              </p>
            </AccordionItem>

            <AccordionItem title='What is the recommitment requirement?'>
              <div>
                <p className='text-foreground-secondary mb-3'>
                  Before withdrawing your matured funds, you must create and
                  complete a new funding (recommitment) with an amount equal to
                  or greater than your original fund. This ensures continuous
                  platform growth and maintains your funding momentum.
                </p>
                <div className='bg-primary/5 p-3 rounded-lg border border-primary/20'>
                  <p className='text-sm text-primary'>
                    <Info className='h-4 w-4 inline mr-2' />
                    This requirement keeps the community cycle active and
                    ensures sustainable returns for all participants.
                  </p>
                </div>
              </div>
            </AccordionItem>

            <AccordionItem title='What are the minimum fund amounts?'>
              <p className='text-foreground-secondary'>
                Minimum funding is ₦10,000 for Naira and 0.1 SOL for Solana.
                Maximum amounts depend on your account privilege level.
              </p>
            </AccordionItem>

            <AccordionItem title='How do I upgrade my account privilege?'>
              <p className='text-foreground-secondary'>
                Account privileges are upgraded automatically based on your
                activity - either through consistent transactions or building
                your referral network.
              </p>
            </AccordionItem>

            <AccordionItem title='Is my funding safe?'>
              <p className='text-foreground-secondary'>
                The platform operates on community trust and transparency. All
                transactions are tracked, and there are no bots or artificial
                manipulation.
              </p>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* CTA Section */}
      <section className='bg-primary/5 py-16'>
        <div className='container mx-auto px-8 lg:px-12 text-center'>
          <div className='max-w-3xl mx-auto'>
            <h2 className='text-3xl md:text-4xl font-bold text-foreground mb-4'>
              Ready to Grow Your Funds?
            </h2>
            <p className='text-lg text-foreground-secondary mb-8'>
              Join thousands of satisfied members who are already earning with
              Xashli
            </p>
            <div className='flex flex-col sm:flex-row gap-4 justify-center'>
              <Button
                size='lg'
                onClick={() => navigate('/auth/register')}
                className='group bg-primary hover:bg-primary/90'
              >
                Create Account
                <ArrowRight className='ml-2 h-4 w-4 transition-transform group-hover:translate-x-1' />
              </Button>
              <Button
                size='lg'
                variant='outline'
                onClick={() => navigate('/auth/login')}
              >
                Login to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className='bg-black text-white py-16'>
        <div className='container mx-auto px-8 lg:px-12'>
          <div className='grid md:grid-cols-4 gap-8'>
            <div className='col-span-2'>
              <div className='flex items-center space-x-3 mb-4'>
                <img
                  src={xashliLogo}
                  alt='Xashli Logo'
                  className='h-8 w-auto'
                />
              </div>
              <p className='text-gray-400 mb-6 max-w-md'>
                Xashli is a community-powered financial platform where members
                help each other grow their funds through strategic cycles and
                mutual support.
              </p>
              <div className='flex space-x-4'>
                <a
                  href='https://t.me/XashliOfficial'
                  target='_blank'
                  rel='noopener noreferrer'
                  className='text-gray-400 hover:text-primary cursor-pointer transition-colors'
                >
                  <Send className='h-5 w-5' />
                </a>
                <a
                  href='mailto:<EMAIL>'
                  className='text-gray-400 hover:text-primary cursor-pointer transition-colors'
                >
                  <MessageCircle className='h-5 w-5' />
                </a>
              </div>
            </div>

            <div>
              <h3 className='font-semibold mb-4'>Quick Links</h3>
              <ul className='space-y-2 text-gray-400'>
                <li>
                  <button
                    onClick={() => navigate('/auth/register')}
                    className='hover:text-primary'
                  >
                    Create Account
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => navigate('/auth/login')}
                    className='hover:text-primary'
                  >
                    Login
                  </button>
                </li>
                <li>
                  <a href='#' className='hover:text-primary'>
                    How It Works
                  </a>
                </li>
                <li>
                  <a href='#' className='hover:text-primary'>
                    Account Privileges
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h3 className='font-semibold mb-4'>Support</h3>
              <ul className='space-y-2 text-gray-400'>
                <li>
                  <a href='#' className='hover:text-primary'>
                    FAQ
                  </a>
                </li>
                <li>
                  <a href='#' className='hover:text-primary'>
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href='#' className='hover:text-primary'>
                    Terms of Service
                  </a>
                </li>
                <li>
                  <a href='#' className='hover:text-primary'>
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className='border-t border-gray-800 mt-12 pt-8 text-center text-gray-400'>
            <p>
              &copy; {new Date().getFullYear()} Xashli. All rights reserved.
              Built with community in mind.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
