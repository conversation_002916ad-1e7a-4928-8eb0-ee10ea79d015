<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class PlatformSetting extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'updated_at' => 'datetime',
    ];

    /**
     * Get the value of the setting based on its type.
     *
     * @return mixed
     */
    public function getTypedValue()
    {
        return match ($this->type) {
            'int' => (int) $this->value,
            'decimal' => (float) $this->value,
            'boolean' => $this->value === 'true',
            default => $this->value,
        };
    }

    /**
     * Get a setting by key.
     *
     * @return mixed
     */
    public static function getSetting(string $key)
    {
        $setting = self::where('key', $key)->first();

        return $setting ? $setting->getTypedValue() : null;
    }

    /**
     * Update a setting by key.
     *
     * @param  mixed  $value
     */
    public static function updateSetting(string $key, $value): bool
    {
        $setting = self::where('key', $key)->first();
        if (! $setting) {
            return false;
        }

        $setting->value = (string) $value;
        $setting->updated_at = now();

        return $setting->save();
    }
}
