/**
 * Convert various value types to number with comprehensive validation
 */
export function toNumber(value: unknown, defaultValue: number = 0): number {
  // Handle null and undefined
  if (value === null || value === undefined) {
    return defaultValue;
  }

  // If already a number, validate it's not NaN or Infinity
  if (typeof value === 'number') {
    return isNaN(value) || !isFinite(value) ? defaultValue : value;
  }

  // Handle string conversion
  if (typeof value === 'string') {
    // Handle empty strings
    if (value.trim() === '') {
      return defaultValue;
    }

    // Remove common non-numeric characters (commas, currency symbols, etc.)
    const cleanedValue = value
      .replace(/[,$₦\s]/g, '') // Remove commas, dollar signs, naira symbols, spaces
      .trim();

    // Check if it's a valid number string
    const converted = Number(cleanedValue);
    return isNaN(converted) || !isFinite(converted) ? defaultValue : converted;
  }

  // Handle boolean (true = 1, false = 0)
  if (typeof value === 'boolean') {
    return value ? 1 : 0;
  }

  // If all else fails, try Number() conversion
  try {
    const converted = Number(value);
    return isNaN(converted) || !isFinite(converted) ? defaultValue : converted;
  } catch {
    return defaultValue;
  }
}

/**
 * Convert value to integer with validation
 */
export function toInteger(value: unknown, defaultValue: number = 0): number {
  const numberValue = toNumber(value, defaultValue);
  return Math.floor(numberValue);
}

/**
 * Convert value to number with specified decimal places
 */
export function toFixedFloat(
  value: unknown,
  decimalPlaces: number,
  defaultValue: number = 0
): number {
  const numberValue = toNumber(value, defaultValue);
  return (
    Math.round(numberValue * Math.pow(10, decimalPlaces)) /
    Math.pow(10, decimalPlaces)
  );
}
