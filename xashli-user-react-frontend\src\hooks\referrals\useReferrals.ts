import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { referralService } from '../../services';
import type {
  ReferralInfoApiResponse,
  RefereesApiResponse,
  BonusesApiResponse,
  ReferralBonusesFilters,
} from '../../types/referral';

export function useReferrals() {
  const [referralInfo, setReferralInfo] =
    useState<ReferralInfoApiResponse | null>(null);
  const [referees, setReferees] = useState<RefereesApiResponse | null>(null);
  const [bonuses, setBonuses] = useState<BonusesApiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [loadingReferees, setLoadingReferees] = useState(false);
  const [loadingBonuses, setLoadingBonuses] = useState(false);
  const fetchReferralInfo = useCallback(async () => {
    try {
      setLoading(true);
      const response = await referralService.getReferralInfo();
      if (response.status === 'success' && response.data) {
        setReferralInfo(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch referral information');
      }
    } catch (error) {
      console.error('Error fetching referral info:', error);
      toast.error('Failed to fetch referral information');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchReferees = useCallback(async () => {
    try {
      setLoadingReferees(true);
      const response = await referralService.getReferees();
      if (response.status === 'success' && response.data) {
        setReferees(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch referees');
      }
    } catch (error) {
      console.error('Error fetching referees:', error);
      toast.error('Failed to fetch referees');
    } finally {
      setLoadingReferees(false);
    }
  }, []);

  const fetchBonuses = useCallback(async (filters?: ReferralBonusesFilters) => {
    try {
      setLoadingBonuses(true);
      const response = await referralService.getBonuses(filters);
      if (response.status === 'success' && response.data) {
        setBonuses(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch referral bonuses');
      }
    } catch (error) {
      console.error('Error fetching bonuses:', error);
      toast.error('Failed to fetch referral bonuses');
    } finally {
      setLoadingBonuses(false);
    }
  }, []);

  const copyReferralLink = useCallback(() => {
    if (referralInfo?.referral_link) {
      navigator.clipboard
        .writeText(referralInfo.referral_link)
        .then(() => {
          toast.success('Referral link copied to clipboard!');
        })
        .catch(() => {
          toast.error('Failed to copy referral link');
        });
    }
  }, [referralInfo]);

  const copyReferralCode = useCallback(() => {
    if (referralInfo?.referral_code) {
      navigator.clipboard
        .writeText(referralInfo.referral_code)
        .then(() => {
          toast.success('Referral code copied to clipboard!');
        })
        .catch(() => {
          toast.error('Failed to copy referral code');
        });
    }
  }, [referralInfo]);

  useEffect(() => {
    fetchReferralInfo();
  }, [fetchReferralInfo]);

  return {
    referralInfo,
    referees,
    bonuses,
    loading,
    loadingReferees,
    loadingBonuses,
    fetchReferralInfo,
    fetchReferees,
    fetchBonuses,
    copyReferralLink,
    copyReferralCode,
  };
}
