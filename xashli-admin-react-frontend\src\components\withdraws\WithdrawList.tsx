import React from 'react';
import { format } from 'date-fns';
import { Eye, CheckCircle, AlertTriangle, Clock, XCircle } from 'lucide-react';

import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';

import type { Withdraw } from '../../types/withdraw';

interface WithdrawListProps {
  withdraws: Withdraw[];
  isLoading?: boolean;
  onWithdrawSelect?: (withdraw: Withdraw) => void;
}

const WITHDRAW_STATUSES = {
  pending: {
    label: 'Pending',
    variant: 'secondary' as const,
    icon: AlertTriangle,
    color: 'text-yellow-600',
  },
  matched: {
    label: 'Matched',
    variant: 'default' as const,
    icon: Clock,
    color: 'text-blue-600',
  },
  completed: {
    label: 'Completed',
    variant: 'success' as const,
    icon: CheckCircle,
    color: 'text-green-600',
  },
  cancelled: {
    label: 'Cancelled',
    variant: 'destructive' as const,
    icon: XCircle,
    color: 'text-red-600',
  },
};

export const WithdrawList: React.FC<WithdrawListProps> = ({
  withdraws,
  isLoading = false,
  onWithdrawSelect,
}) => {
  const formatCurrencyAmount = (
    amount: number,
    currency: 'fiat' | 'crypto' = 'fiat'
  ) => {
    if (currency === 'fiat') {
      return `₦${amount.toLocaleString()}`;
    }
    return `${amount.toFixed(3)} SOL`;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig =
      WITHDRAW_STATUSES[status as keyof typeof WITHDRAW_STATUSES];
    if (!statusConfig) return null;

    const Icon = statusConfig.icon;
    return (
      <Badge variant={statusConfig.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {statusConfig.label}
      </Badge>
    );
  };

  const getCurrencyDisplay = (currency: string) => {
    return currency === 'fiat' ? 'Fiat (₦)' : 'Crypto (SOL)';
  };

  const getUserDisplay = (user: any) => {
    if (!user) return 'N/A';

    return (
      <div>
        <div className="font-medium">
          {user.full_name || user.first_name + ' ' + user.last_name}
        </div>
        <div className="text-sm text-muted-foreground">{user.email}</div>
      </div>
    );
  };

  const getProgressPercentage = (withdraw: Withdraw) => {
    if (withdraw.status === 'pending') return 0;
    if (withdraw.status === 'completed') return 100;

    const totalAmount =
      withdraw.base_withdrawable_amount + withdraw.withdrawable_referral_bonus;
    if (totalAmount === 0) return 0;

    return Math.round((withdraw.amount_matched / totalAmount) * 100);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading withdraws...</div>
      </div>
    );
  }

  if (!withdraws || withdraws.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">No withdraws found</div>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>SN</TableHead>
          <TableHead>User</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Currency</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Progress</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {withdraws.map((withdraw, index) => {
          const totalAmount =
            withdraw.base_withdrawable_amount +
            withdraw.withdrawable_referral_bonus;
          const progress = getProgressPercentage(withdraw);

          return (
            <TableRow
              key={withdraw.id}
              className="cursor-pointer hover:bg-muted/50"
            >
              <TableCell>
                <span className="text-sm font-medium text-gray-900">
                  {index + 1}
                </span>
              </TableCell>
              <TableCell>{getUserDisplay(withdraw.user)}</TableCell>

              <TableCell>
                <div>
                  <div className="font-medium">
                    {formatCurrencyAmount(
                      withdraw.base_withdrawable_amount,
                      withdraw.fund?.currency
                    )}
                  </div>
                  {withdraw.withdrawable_referral_bonus > 0 && (
                    <div className="text-sm text-green-600">
                      +
                      {formatCurrencyAmount(
                        withdraw.withdrawable_referral_bonus,
                        withdraw.fund?.currency
                      )}{' '}
                      bonus
                    </div>
                  )}
                  <div className="text-xs text-muted-foreground">
                    Total:{' '}
                    {formatCurrencyAmount(totalAmount, withdraw.fund?.currency)}
                  </div>
                </div>
              </TableCell>

              <TableCell>
                <Badge variant="outline">
                  {getCurrencyDisplay(withdraw.fund?.currency || 'fiat')}
                </Badge>
              </TableCell>

              <TableCell>{getStatusBadge(withdraw.status)}</TableCell>

              <TableCell>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span>Matched</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        progress === 100
                          ? 'bg-green-500'
                          : progress > 0
                            ? 'bg-blue-500'
                            : 'bg-gray-300'
                      }`}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrencyAmount(
                      withdraw.amount_matched,
                      withdraw.fund?.currency
                    )}{' '}
                    matched
                  </div>
                </div>
              </TableCell>

              <TableCell>
                <div className="text-sm">
                  {format(new Date(withdraw.created_at), 'MMM dd, yyyy')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {format(new Date(withdraw.created_at), 'HH:mm')}
                </div>
              </TableCell>

              <TableCell>
                {onWithdrawSelect && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onWithdrawSelect(withdraw)}
                    className="h-8 w-8 p-0"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                )}
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
};

export default WithdrawList;
