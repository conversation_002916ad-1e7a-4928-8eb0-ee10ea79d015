# Xashli Admin React Frontend

A robust, modern admin dashboard for the Xashli platform built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

### ✅ Completed Features

- **Authentication System**: Complete login, logout, forgot password, and reset password functionality
- **Modern UI Components**: Beautiful, responsive components built with Tailwind CSS and shadcn/ui principles
- **Type Safety**: Full TypeScript implementation with strict type checking
- **State Management**: Zustand store with persistence for authentication state
- **API Integration**: Robust API client with JWT token management and automatic refresh
- **Form Validation**: React Hook Form integration with comprehensive validation
- **Error Handling**: Consistent error handling across the application
- **Responsive Design**: Mobile-first responsive design approach
- **Development Setup**: Complete development environment with Vite and hot reloading

### 🎨 Design System

- **Brand Colors**: Black (#000000), Grey shades, and Gold (#F59E0B)
- **Modern UI**: Clean, professional interface with glassmorphism effects
- **Accessibility**: WCAG compliant components with proper focus management

### 🔐 Security

- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **Cookie Management**: Secure, HTTP-only cookie storage for tokens
- **Protected Routes**: Route-level authentication guards
- **Input Validation**: Client-side and server-side validation

## 🛠️ Tech Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and building
- **Styling**: Tailwind CSS with custom brand configuration
- **State Management**: Zustand with persistence
- **Forms**: React Hook Form for performant forms
- **HTTP Client**: Axios with interceptors
- **Data Fetching**: TanStack Query (React Query) for server state
- **Routing**: React Router v6
- **Icons**: Lucide React for consistent iconography
- **Cookie Management**: js-cookie for secure token storage

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Environment Setup**
   Configure your environment variables in `.env`:

   ```env
   VITE_API_BASE_URL=http://localhost:8000/api
   VITE_APP_NAME=Xashli Admin
   VITE_APP_VERSION=1.0.0
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` (or the port shown in your terminal)

### Building for Production

```bash
npm run build
npm run preview
```

## 🎯 Next Features to Implement

- [ ] User Management (CRUD operations)
- [ ] Transaction Management
- [ ] Dashboard Analytics
- [ ] Settings and Configuration
- [ ] Reports and Export functionality
- [ ] Real-time notifications
- [ ] Audit logs

---

**Built with ❤️ for the Xashli platform**
