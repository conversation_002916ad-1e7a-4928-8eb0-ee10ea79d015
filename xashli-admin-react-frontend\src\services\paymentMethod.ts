import { apiClient } from './api';
import type { ApiResponse } from '../types';
import type {
  PaymentMethod,
  PaymentMethodsParams,
  PaymentMethodUpdateData,
} from '../types/paymentMethod';

export const paymentMethodService = {
  /**
   * Get all payment methods with optional filters (admin only)
   */ async getPaymentMethods(
    params?: PaymentMethodsParams
  ): Promise<ApiResponse<PaymentMethod[]>> {
    return apiClient.get<PaymentMethod[]>('/payment-methods', { params });
  },

  /**
   * Get a specific payment method by ID
   */
  async getPaymentMethod(id: string): Promise<ApiResponse<PaymentMethod>> {
    return apiClient.get<PaymentMethod>(`/payment-methods/${id}`);
  },

  /**
   * Update a payment method (admin only for platform payment methods)
   */
  async updatePaymentMethod(
    id: string,
    data: PaymentMethodUpdateData
  ): Promise<ApiResponse<PaymentMethod>> {
    return apiClient.put<PaymentMethod>(`/payment-methods/${id}`, data);
  },
};
