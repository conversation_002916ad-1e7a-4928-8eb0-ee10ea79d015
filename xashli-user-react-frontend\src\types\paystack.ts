export interface PaystackConfig {
  publicKey: string;
  email: string;
  amount: number; // Amount in kobo (NGN * 100)
  currency: 'NGN';
  channels: string[];
  metadata: {
    payment_match_id: string;
    fund_id: string;
    is_admin_withdraw: boolean;
  };
  onSuccess: (response: PaystackSuccessResponse) => void;
  onClose: () => void;
}

export interface PaystackSuccessResponse {
  reference: string;
  status: 'success' | 'failed';
  trans: string;
  transaction: string;
  trxref: string;
  redirecturl?: string;
}
