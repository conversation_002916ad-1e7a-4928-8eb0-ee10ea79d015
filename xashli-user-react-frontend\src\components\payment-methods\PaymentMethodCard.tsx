import { Credit<PERSON><PERSON>, Wallet, Edit, Trash2, Eye } from 'lucide-react';
import type { PaymentMethod } from '../../types';
import { Card, CardContent } from '../ui';
import { Button } from '../ui/Button';

interface PaymentMethodCardProps {
  paymentMethod: PaymentMethod;
  onEdit: () => void;
  onView: () => void;
  onDelete: () => void;
}

export default function PaymentMethodCard({
  paymentMethod,
  onEdit,
  onView,
  onDelete,
}: PaymentMethodCardProps) {
  const isBankAccount = paymentMethod.type === 'bank';

  return (
    <Card className='hover:shadow-md transition-shadow duration-200'>
      <CardContent className='p-6'>
        {/* Status Badge */}
        <div className='flex justify-end mb-2'>
          <span
            className={`px-3 py-1 rounded-full text-xs font-medium ${
              paymentMethod.status === 'active'
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400'
            }`}
          >
            {paymentMethod.status === 'active' ? 'Active' : 'Inactive'}
          </span>
        </div>

        {/* Header */}
        <div className='flex items-center gap-3 mb-3'>
          <div className='p-2 bg-primary/10 rounded-lg'>
            {isBankAccount ? (
              <CreditCard className='h-5 w-5 text-primary' />
            ) : (
              <Wallet className='h-5 w-5 text-primary' />
            )}
          </div>
          <div className='flex-1 min-w-0'>
            <h3 className='font-semibold text-lg text-foreground truncate'>
              {isBankAccount
                ? paymentMethod.account_name
                : `${paymentMethod.crypto_network} Wallet`}
            </h3>
            <p className='text-sm text-foreground-secondary truncate'>
              {isBankAccount
                ? paymentMethod.bank_name
                : 'Cryptocurrency Wallet'}
            </p>
          </div>
        </div>

        {/* Details */}
        <div className='space-y-2 my-4'>
          {isBankAccount ? (
            <>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Type
                </span>
                <span className='font-semibold text-foreground'>
                  Bank Account
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Account Number
                </span>
                <span className='font-mono font-semibold text-primary truncate ml-2 text-right'>
                  {paymentMethod.account_number}
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Bank
                </span>
                <span className='font-semibold text-primary truncate ml-2 text-right'>
                  {paymentMethod.bank_name}
                </span>
              </div>
            </>
          ) : (
            <>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Type
                </span>
                <span className='font-semibold text-foreground'>
                  Cryptocurrency
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Network
                </span>
                <span className='font-semibold text-primary truncate ml-2 text-right'>
                  {paymentMethod.crypto_network}
                </span>
              </div>
              <div className='flex justify-between text-sm'>
                <span className='text-foreground-secondary font-medium'>
                  Wallet Address
                </span>
                <span className='font-mono font-semibold text-primary text-xs truncate ml-2 text-right'>
                  {paymentMethod.wallet_address?.slice(0, 8)}...
                  {paymentMethod.wallet_address?.slice(-8)}
                </span>
              </div>
            </>
          )}
        </div>

        {/* Actions */}
        <div className='flex justify-center gap-2'>
          <Button variant='outline' size='sm' onClick={onView}>
            <Eye className='h-4 w-4 mr-2' />
            View
          </Button>
          <Button variant='outline' size='sm' onClick={onEdit}>
            <Edit className='h-4 w-4 mr-2' />
            Edit
          </Button>
          <Button
            variant='outline'
            size='sm'
            onClick={onDelete}
            className='text-destructive hover:text-destructive-foreground hover:bg-destructive'
          >
            <Trash2 className='h-4 w-4 mr-2' />
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
