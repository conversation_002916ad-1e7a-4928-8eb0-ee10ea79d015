import { useEffect, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Eye, EyeOff, Check, X } from 'lucide-react';
import {
  useRegister,
  usePasswordVisibility,
  usePasswordRequirements,
} from '@/hooks/auth';
import { TermsOfUseModal, PrivacyPolicyModal } from '@/components/auth';

const registerSchema = z
  .object({
    firstName: z.string().min(2, 'First name must be at least 2 characters'),
    lastName: z.string().min(2, 'Last name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    phone: z
      .string()
      .min(10, 'Phone number must be at least 10 digits')
      .regex(/^\+?[\d\s-()]+$/, 'Please enter a valid phone number'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain uppercase, lowercase and number'
      ),
    confirmPassword: z.string(),
    referrerCode: z.string().optional(),
    acceptTerms: z
      .boolean()
      .refine(val => val === true, 'You must accept the terms and conditions'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type RegisterFormData = z.infer<typeof registerSchema>;

export function RegisterPage() {
  const [searchParams] = useSearchParams();
  const { handleRegister, isLoading } = useRegister();
  const {
    showPassword,
    togglePasswordVisibility,
    showConfirmPassword,
    toggleConfirmPasswordVisibility,
  } = usePasswordVisibility();

  // Modal states
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const password = watch('password', '');
  const { passwordRequirements } = usePasswordRequirements(password);

  useEffect(() => {
    const refCode = searchParams.get('ref');
    if (refCode) {
      setValue('referrerCode', refCode);
    }
  }, [searchParams, setValue]);

  const onSubmit = async (data: RegisterFormData) => {
    await handleRegister({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phone,
      password: data.password,
      confirmPassword: data.confirmPassword,
      referrerCode: data.referrerCode,
      acceptTerms: data.acceptTerms,
    });
  };

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold text-foreground'>Create account</h2>
        <p className='mt-2 text-foreground-secondary'>
          Join Xashli and start your digital marketplace journey
        </p>
      </div>

      <Card>
        <CardContent className='pt-6'>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
            <Input
              label='First Name'
              placeholder='John'
              error={errors.firstName?.message}
              {...register('firstName')}
            />
            <Input
              label='Last Name'
              placeholder='Doe'
              error={errors.lastName?.message}
              {...register('lastName')}
            />{' '}
            <Input
              label='Email'
              type='email'
              placeholder='<EMAIL>'
              error={errors.email?.message}
              {...register('email')}
            />
            <Input
              label='Phone Number'
              type='tel'
              placeholder='+234 80 1234 5678'
              error={errors.phone?.message}
              {...register('phone')}
            />
            <Input
              label='Referrer Code (Optional)'
              placeholder='Enter referrer code if you have one'
              error={errors.referrerCode?.message}
              {...register('referrerCode')}
            />
            <div className='relative'>
              <Input
                label='Password'
                type={showPassword ? 'text' : 'password'}
                placeholder='Create a strong password'
                error={errors.password?.message}
                {...register('password')}
              />
              <button
                type='button'
                className='absolute right-3 bottom-[10px] text-foreground-muted hover:text-foreground'
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            {password && (
              <div className='space-y-2'>
                <p className='text-sm font-medium text-foreground-secondary'>
                  Password requirements:
                </p>
                <div className='grid grid-cols-2 gap-2'>
                  {passwordRequirements.map((req, index) => (
                    <div key={index} className='flex items-center space-x-2'>
                      {req.met ? (
                        <Check size={16} className='text-success' />
                      ) : (
                        <X size={16} className='text-foreground-muted' />
                      )}
                      <span
                        className={`text-xs ${req.met ? 'text-success' : 'text-foreground-muted'}`}
                      >
                        {req.label}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            <div className='relative'>
              <Input
                label='Confirm Password'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm your password'
                error={errors.confirmPassword?.message}
                {...register('confirmPassword')}
              />
              <button
                type='button'
                className='absolute right-3 bottom-[10px] text-foreground-muted hover:text-foreground'
                onClick={toggleConfirmPasswordVisibility}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
            <div className='flex items-start'>
              <input
                id='accept-terms'
                type='checkbox'
                className='h-4 w-4 mt-0.5 text-primary focus:ring-primary border-input rounded'
                {...register('acceptTerms')}
              />
              <label
                htmlFor='accept-terms'
                className='ml-2 block text-sm text-foreground-secondary'
              >
                I agree to the{' '}
                <button
                  type='button'
                  onClick={() => setShowTermsModal(true)}
                  className='text-primary hover:text-primary/90 font-medium'
                >
                  Terms of Service
                </button>{' '}
                and{' '}
                <button
                  type='button'
                  onClick={() => setShowPrivacyModal(true)}
                  className='text-primary hover:text-primary/90 font-medium'
                >
                  Privacy Policy
                </button>
              </label>
            </div>
            {errors.acceptTerms && (
              <p className='text-sm text-destructive'>
                {errors.acceptTerms.message}
              </p>
            )}
            <Button type='submit' className='w-full' isLoading={isLoading}>
              {isLoading ? 'Creating account...' : 'Create account'}
            </Button>
          </form>

          <p className='mt-6 text-center text-sm text-foreground-secondary'>
            Already have an account?{' '}
            <Link
              to='/auth/login'
              className='font-medium text-primary hover:text-primary/90'
            >
              Sign in
            </Link>
          </p>
        </CardContent>
      </Card>

      {/* Terms of Use Modal */}
      <TermsOfUseModal
        isOpen={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />

      {/* Privacy Policy Modal */}
      <PrivacyPolicyModal
        isOpen={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </div>
  );
}
