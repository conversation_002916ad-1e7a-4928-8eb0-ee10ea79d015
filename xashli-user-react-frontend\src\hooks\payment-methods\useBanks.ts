import { useState } from 'react';
import { paymentMethodService } from '../../services';
import type { Bank } from '../../types';
import toast from 'react-hot-toast';

export const useBanks = () => {
  const [banks, setBanks] = useState<Bank[]>([]);
  const [bankSearchTerm, setBankSearchTerm] = useState('');
  const [loadingBanks, setLoadingBanks] = useState(false);

  // Filter banks based on search term
  const filteredBanks = banks.filter(bank =>
    bank.name.toLowerCase().includes(bankSearchTerm.toLowerCase())
  );

  const loadBanks = async () => {
    try {
      setLoadingBanks(true);
      const response = await paymentMethodService.getBanks();
      if (response.status === 'success' && response.data) {
        setBanks(response.data);
      } else {
        toast.error('Failed to load banks');
      }
    } catch (error) {
      console.error('Error loading banks:', error);
      toast.error('Failed to load banks');
    } finally {
      setLoadingBanks(false);
    }
  };

  const getBankByCode = (code: string) => {
    return banks.find(bank => bank.code === code);
  };

  const clearBankSearch = () => {
    setBankSearchTerm('');
  };

  return {
    banks,
    bankSearchTerm,
    setBankSearchTerm,
    loadingBanks,
    filteredBanks,
    loadBanks,
    getBankByCode,
    clearBankSearch,
  };
};
