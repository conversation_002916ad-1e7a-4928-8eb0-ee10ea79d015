export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'bank' | 'crypto';
  bank_name?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  bank_code?: string;
  status: 'active' | 'inactive';
  last_used_at?: string;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    full_name: string;
    email: string;
  };
}

export interface PaymentMethodsParams {
  user_id?: string;
  only_admins_payment_methods?: 'true' | 'false';
  type?: 'bank' | 'crypto';
  status?: 'active' | 'inactive';
  page?: number;
  per_page?: number;
}

export interface PaymentMethodUpdateData {
  type?: 'bank' | 'crypto';
  bank_name?: string;
  account_number?: string;
  account_name?: string;
  wallet_address?: string;
  crypto_network?: string;
  bank_code?: string;
  status?: 'active' | 'inactive';
}
