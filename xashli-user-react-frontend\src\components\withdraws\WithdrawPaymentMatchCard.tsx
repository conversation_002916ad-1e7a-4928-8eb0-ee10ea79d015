import {
  Eye,
  CheckCircle,
  Clock,
  AlertCircle,
  Flag,
  XCircle,
  User,
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Badge } from '../ui';
import { getImageUrl } from '../../utils/images';
import { toTitleCase, formatCurrencyAmount } from '../../utils/format';
import { DisputeModal } from '../disputes/DisputeModal';
import { FunderModal } from './FunderModal';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { PaymentDispute } from '../../types/dispute';
import { useState } from 'react';
import type { Currency } from '@/types';

interface WithdrawPaymentMatchCardProps {
  paymentMatch: PaymentMatch;
  currency: Currency;
  onConfirmPaymentReceived: (match: PaymentMatch) => void;
  onDisputeCreated?: (dispute: PaymentDispute) => void;
  index?: number; // Optional index for display purposes
}

export function WithdrawPaymentMatchCard({
  paymentMatch,
  currency,
  onConfirmPaymentReceived,
  onDisputeCreated,
  index = 1,
}: WithdrawPaymentMatchCardProps) {
  const [showDisputeModal, setShowDisputeModal] = useState(false);
  const [showFunderModal, setShowFunderModal] = useState(false);

  const getPaymentMatchStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-warning/20 text-warning border-warning/30',
      paid: 'bg-primary/20 text-primary border-primary/30',
      confirmed: 'bg-success/20 text-success border-success/30',
      disputed: 'bg-destructive/20 text-destructive border-destructive/30',
      cancelled: 'bg-muted/20 text-muted-foreground border-muted/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className='h-3 w-3' />;
      case 'paid':
        return <AlertCircle className='h-3 w-3' />;
      case 'confirmed':
        return <CheckCircle className='h-3 w-3' />;
      case 'disputed':
        return <XCircle className='h-3 w-3' />;
      case 'cancelled':
        return <XCircle className='h-3 w-3' />;
      default:
        return null;
    }
  };

  const canConfirmPayment =
    paymentMatch.status === 'paid' &&
    !paymentMatch.is_payment_received_confirmed;

  const hasDisputes = paymentMatch.disputes && paymentMatch.disputes.length > 0;
  const canCreateDispute = paymentMatch.status === 'paid' && !hasDisputes;

  const handleDisputeCreated = (dispute: PaymentDispute) => {
    onDisputeCreated?.(dispute);
    setShowDisputeModal(false);
  };

  const getDisputeStatusBadge = (status: string) => {
    const variants = {
      under_review: 'bg-warning/20 text-warning border-warning/30',
      resolved: 'bg-success/20 text-success border-success/30',
      rejected: 'bg-destructive/20 text-destructive border-destructive/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  return (
    <div className='bg-background-secondary rounded-lg border border-border p-4 hover:border-primary/20 transition-all'>
      {/* Status badge at the top */}
      <div className='mb-3 flex justify-start items-center gap-2'>
        <Badge
          variant='outline'
          className={`${getPaymentMatchStatusBadge(paymentMatch.status)} flex items-center gap-1 px-2 py-1 text-xs font-medium border`}
        >
          {getStatusIcon(paymentMatch.status)}
          {toTitleCase(paymentMatch.status)}
        </Badge>
        {hasDisputes && (
          <Badge
            variant='outline'
            className={`${getDisputeStatusBadge(paymentMatch.disputes![0].status)} flex items-center gap-1 px-2 py-1 text-xs font-medium border`}
          >
            <Flag className='h-3 w-3' />
            {paymentMatch.disputes!.length > 1
              ? `${paymentMatch.disputes!.length} Disputes`
              : toTitleCase(paymentMatch.disputes![0].status)}
          </Badge>
        )}
      </div>

      {/* Match info header */}
      <div className='flex items-center justify-between mb-3'>
        <span className='text-sm font-medium text-foreground'>
          Match #{index}
        </span>
        <div className='text-sm font-semibold text-foreground'>
          {formatCurrencyAmount(paymentMatch.amount || 0, currency)}
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
        <div>
          <p className='text-xs text-foreground-secondary mb-1'>Match ID</p>
          <p className='text-sm font-mono text-foreground'>
            {paymentMatch.id.slice(0, 8)}...
          </p>
        </div>
        <div>
          <p className='text-xs text-foreground-secondary mb-1'>Created</p>
          <p className='text-sm text-foreground'>
            {new Date(paymentMatch.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* Status messages */}
      {paymentMatch.status === 'pending' && (
        <div className='bg-warning/10 border border-warning/30 rounded-md p-3 mb-4'>
          <p className='text-xs text-warning'>
            Waiting for funder to send payment. You will be notified when
            payment is sent.
          </p>
        </div>
      )}

      {paymentMatch.status === 'paid' &&
        !paymentMatch.is_payment_received_confirmed && (
          <div className='bg-primary/10 border border-primary/30 rounded-md p-3 mb-4'>
            <p className='text-xs text-primary'>
              Payment has been sent by the funder. Please confirm once you
              receive the payment.
            </p>
          </div>
        )}

      {paymentMatch.status === 'confirmed' && (
        <div className='bg-success/10 border border-success/30 rounded-md p-3 mb-4'>
          <p className='text-xs text-success'>
            ✅ Payment received and confirmed! This match is complete.
          </p>
        </div>
      )}

      {/* Action buttons */}
      <div className='flex gap-2 flex-wrap'>
        {/* Funder info button */}
        {paymentMatch.fund_user && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => setShowFunderModal(true)}
            className='flex items-center gap-2'
          >
            <User className='h-4 w-4' />
            View Funder
          </Button>
        )}

        {/* View payment proof if available */}
        {paymentMatch.payment_proof_image && (
          <Button
            size='sm'
            variant='outline'
            onClick={() =>
              window.open(
                getImageUrl(paymentMatch.payment_proof_image!)!,
                '_blank'
              )
            }
            className='flex items-center gap-2'
          >
            <Eye className='h-4 w-4' />
            View Payment Proof
          </Button>
        )}

        {/* Confirm payment received button */}
        {canConfirmPayment && (
          <Button
            size='sm'
            onClick={() => onConfirmPaymentReceived(paymentMatch)}
            className='flex items-center gap-2 bg-success hover:bg-success/90 text-success-foreground'
          >
            <CheckCircle className='h-4 w-4' />
            Confirm Payment Received
          </Button>
        )}

        {/* Dispute button */}
        {canCreateDispute && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => setShowDisputeModal(true)}
            className='flex items-center gap-2 text-destructive border-destructive/30 hover:bg-destructive hover:text-white'
          >
            <Flag className='h-4 w-4' />
            Dispute Payment
          </Button>
        )}

        {/* View disputes button */}
        {hasDisputes && !canCreateDispute && (
          <Button
            size='sm'
            variant='outline'
            onClick={() => setShowDisputeModal(true)}
            className='flex items-center gap-2 text-destructive border-destructive/30 hover:bg-destructive hover:text-white'
          >
            <Flag className='h-4 w-4' />
            {paymentMatch.disputes!.length > 1
              ? `View Disputes (${paymentMatch.disputes!.length})`
              : 'View Dispute'}
          </Button>
        )}
      </div>

      {/* Dispute Modal */}
      <DisputeModal
        isOpen={showDisputeModal}
        onClose={() => setShowDisputeModal(false)}
        paymentMatch={paymentMatch}
        onDisputeCreated={handleDisputeCreated}
      />

      {/* Funder Modal */}
      <FunderModal
        isOpen={showFunderModal}
        onClose={() => setShowFunderModal(false)}
        paymentMatch={paymentMatch}
        currency={currency}
      />
    </div>
  );
}
