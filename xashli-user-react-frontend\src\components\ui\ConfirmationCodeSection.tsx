import { Shield, Clock, AlertCircle } from 'lucide-react';
import { Button } from './Button';
import { Input } from './Input';
import { Label } from './Label';
import { LoadingSpinner } from './LoadingSpinner';

interface ConfirmationCodeSectionProps {
  code: string;
  onCodeChange: (code: string) => void;
  onRequestCode: () => void;
  isRequesting: boolean;
  isRequested: boolean;
  canRequest: boolean;
  cooldownRemaining: number;
  error?: string | null;
  title?: string;
  description?: string;
  className?: string;
}

export function ConfirmationCodeSection({
  code,
  onCodeChange,
  onRequestCode,
  isRequesting,
  isRequested,
  canRequest,
  cooldownRemaining,
  error,
  title = 'Security Confirmation',
  description = 'For your security, please request and enter a confirmation code to proceed with this action.',
  className = '',
}: ConfirmationCodeSectionProps) {
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className='flex items-start gap-3'>
        <div className='p-2 bg-primary/10 rounded-lg'>
          <Shield className='h-5 w-5 text-primary' />
        </div>
        <div className='flex-1'>
          <h3 className='font-medium text-foreground'>{title}</h3>
          <p className='text-sm text-foreground-secondary mt-1'>
            {description}
          </p>
        </div>
      </div>

      {/* Request Code Section */}
      <div className='bg-background-secondary rounded-lg p-4 space-y-3'>
        <div className='flex items-center justify-between'>
          <Label className='text-sm font-medium'>Confirmation Code</Label>
          <Button
            type='button'
            variant='outline'
            size='sm'
            onClick={onRequestCode}
            disabled={!canRequest}
            className='min-w-[120px]'
          >
            {isRequesting ? (
              <>
                <LoadingSpinner size='sm' className='mr-2' />
                Sending...
              </>
            ) : cooldownRemaining > 0 ? (
              <>
                <Clock className='h-4 w-4 mr-2' />
                {formatTime(cooldownRemaining)}
              </>
            ) : isRequested ? (
              'Resend Code'
            ) : (
              'Request Code'
            )}
          </Button>
        </div>

        {/* Code Input */}
        <div className='space-y-2'>
          <Input
            type='text'
            placeholder='Enter 6-digit code'
            value={code}
            onChange={e => {
              // Handle both typing and pasting
              const value = e.target.value;
              const cleanedValue = value.replace(/\D/g, '').slice(0, 6);
              onCodeChange(cleanedValue);
            }}
            onInput={e => {
              // Handle input events (more reliable for some browsers)
              const target = e.target as HTMLInputElement;
              const value = target.value;
              const cleanedValue = value.replace(/\D/g, '').slice(0, 6);
              onCodeChange(cleanedValue);
            }}
            onPaste={e => {
              // Handle paste events specifically
              e.preventDefault();
              const pastedText = e.clipboardData.getData('text');
              const cleanedValue = pastedText.replace(/\D/g, '').slice(0, 6);
              onCodeChange(cleanedValue);
            }}
            maxLength={6}
            className='text-center text-lg tracking-widest font-mono'
            disabled={!isRequested}
          />

          {!isRequested && (
            <p className='text-xs text-foreground-secondary'>
              Click "Request Code" to receive a confirmation code
            </p>
          )}

          {isRequested && !code && (
            <p className='text-xs text-foreground-secondary'>
              Check your email for the confirmation code
            </p>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className='flex items-center gap-2 text-sm text-destructive'>
            <AlertCircle className='h-4 w-4' />
            <span>{error}</span>
          </div>
        )}

        {/* Status Messages */}
        {cooldownRemaining > 0 && (
          <div className='text-xs text-foreground-secondary'>
            You can request a new code in {formatTime(cooldownRemaining)}
          </div>
        )}
      </div>
    </div>
  );
}
