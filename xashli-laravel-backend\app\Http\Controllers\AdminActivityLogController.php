<?php

namespace App\Http\Controllers;

use App\Models\AdminActivityLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminActivityLogController extends Controller
{
    /**
     * Display a listing of admin activity logs.
     */
    public function index(Request $request): JsonResponse
    {
        // Check if action_type is valid if provided
        if ($request->has('action_type') && ! empty($request->action_type)) {
            if (! in_array($request->action_type, AdminActivityLog::getValidActionTypes())) {
                return $this->badRequest('Invalid action type provided');
            }
        }

        $query = AdminActivityLog::with(['admin:id,full_name,email', 'targetUser:id,full_name,email']);

        // Filter by action type if provided
        if ($request->has('action_type') && ! empty($request->action_type)) {
            $query->where('action_type', $request->action_type);
        }

        // Filter by admin ID if provided
        if ($request->has('admin_id') && ! empty($request->admin_id)) {
            $query->where('admin_id', $request->admin_id);
        }

        // Filter by target user ID if provided
        if ($request->has('target_user_id') && ! empty($request->target_user_id)) {
            $query->where('target_user_id', $request->target_user_id);
        }

        // Filter by date range if provided (optimized for index usage)
        if ($request->has('start_date') && ! empty($request->start_date)) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && ! empty($request->end_date)) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Sort by created_at in descending order (newest first)
        $perPage = $request->per_page ?? 15;
        $paginatedLogs = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Structure response with separate adminActivityLogs and pagination
        $response = [
            'adminActivityLogs' => $paginatedLogs->items(),
            'pagination' => [
                'current_page' => $paginatedLogs->currentPage(),
                'last_page' => $paginatedLogs->lastPage(),
                'per_page' => $paginatedLogs->perPage(),
                'total' => $paginatedLogs->total(),
                'from' => $paginatedLogs->firstItem(),
                'to' => $paginatedLogs->lastItem(),
            ],
        ];

        return $this->success($response, 'Admin activity logs retrieved successfully');
    }

    /**
     * Display the specified admin activity log.
     */
    public function show(string $id): JsonResponse
    {
        // Validate UUID format
        if (! preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $id)) {
            return $this->badRequest('Invalid ID format. Must be a valid UUID.');
        }

        $log = AdminActivityLog::with(['admin:id,full_name,email', 'targetUser:id,full_name,email'])->find($id);

        if (! $log) {
            return $this->notFound('Admin activity log not found');
        }

        return $this->success($log, 'Admin activity log retrieved successfully');
    }

    /**
     * Get admin activity statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        // Get date range for statistics
        $startDate = $request->start_date ? date($request->start_date) : now()->subDays(30);
        $endDate = $request->end_date ? date($request->end_date) : now();

        // Base query for statistics with date range
        $baseQuery = AdminActivityLog::whereBetween('created_at', [$startDate, $endDate]);

        // Get counts by action type (optimized with single query)
        $actionTypeCounts = (clone $baseQuery)
            ->selectRaw('action_type, count(*) as count')
            ->groupBy('action_type')
            ->pluck('count', 'action_type')
            ->toArray();

        // Get counts by admin with JOIN to avoid N+1 queries
        $adminCounts = (clone $baseQuery)
            ->join('users', 'admin_activity_logs.admin_id', '=', 'users.id')
            ->selectRaw('admin_activity_logs.admin_id, users.full_name, users.email, count(*) as count')
            ->groupBy('admin_activity_logs.admin_id', 'users.full_name', 'users.email')
            ->get()
            ->map(function ($item) {
                return [
                    'admin_id' => $item->admin_id,
                    'admin_name' => $item->full_name,
                    'admin_email' => $item->email,
                    'count' => $item->count,
                ];
            });

        // Get daily activity counts (optimized)
        $dailyCounts = (clone $baseQuery)
            ->selectRaw('DATE(created_at) as date, count(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();

        // Get total count efficiently (reuse base query instead of separate count query)
        $totalLogs = (clone $baseQuery)->count();

        $summary = [
            'total_logs_count' => $totalLogs,
            'action_type_counts' => $actionTypeCounts,
            'admin_counts' => $adminCounts,
            'daily_counts' => $dailyCounts,
            'date_range' => [
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
            ],
        ];

        return $this->success($summary, 'Admin activity statistics retrieved successfully');
    }

    /**
     * Get available action types for frontend validation.
     */
    public function actionTypes(): JsonResponse
    {
        $actionTypes = AdminActivityLog::getValidActionTypes();

        // Format for frontend display
        $formattedActionTypes = array_map(function ($actionType) {
            return [
                'value' => $actionType,
                'label' => ucwords(str_replace('_', ' ', $actionType)),
            ];
        }, $actionTypes);

        return $this->success($formattedActionTypes, 'Action types retrieved successfully');
    }
}
