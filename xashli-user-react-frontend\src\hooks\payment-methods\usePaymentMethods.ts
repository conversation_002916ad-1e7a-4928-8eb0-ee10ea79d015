import { useState, useEffect } from 'react';
import { paymentMethodService } from '../../services';
import type { PaymentMethod } from '../../types';
import toast from 'react-hot-toast';

export const usePaymentMethods = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    paymentMethod: PaymentMethod | null;
  }>({ open: false, paymentMethod: null });

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await paymentMethodService.getPaymentMethods();
      if (response.status === 'success' && response.data) {
        setPaymentMethods(response.data);
      } else {
        toast.error('Failed to load payment methods');
      }
    } catch (error) {
      console.error('Error loading payment methods:', error);
      toast.error('Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePaymentMethod = async (
    paymentMethod: PaymentMethod,
    confirmationCode: string
  ) => {
    try {
      const response = await paymentMethodService.deletePaymentMethod(
        paymentMethod.id,
        { confirmation_code: confirmationCode }
      );

      if (response.status === 'success') {
        toast.success('Payment method deleted successfully');
        setPaymentMethods(prev =>
          prev.filter(pm => pm.id !== paymentMethod.id)
        );
        setDeleteDialog({ open: false, paymentMethod: null });
      } else {
        toast.error(response.message || 'Failed to delete payment method');
      }
    } catch (error) {
      console.error('Error deleting payment method:', error);
      toast.error('Failed to delete payment method');
    }
  };

  const openDeleteDialog = (paymentMethod: PaymentMethod) => {
    setDeleteDialog({ open: true, paymentMethod });
  };

  const closeDeleteDialog = () => {
    setDeleteDialog({ open: false, paymentMethod: null });
  };

  useEffect(() => {
    loadPaymentMethods();
  }, []);

  return {
    paymentMethods,
    loading,
    deleteDialog,
    loadPaymentMethods,
    handleDeletePaymentMethod,
    openDeleteDialog,
    closeDeleteDialog,
  };
};
