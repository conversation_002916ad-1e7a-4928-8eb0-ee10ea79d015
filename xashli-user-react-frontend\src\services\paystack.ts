import PaystackPop from '@paystack/inline-js';
import type { PaystackConfig } from '../types/paystack';

export const paystackService = {
  /**
   * Get Paystack public key from environment
   */
  getPublicKey: (): string => {
    const key = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY;
    if (!key) {
      throw new Error('Paystack public key not configured');
    }
    return key;
  },

  /**
   * Convert NGN amount to kobo (multiply by 100)
   */
  convertToKobo: (amount: number): number => {
    return Math.round(amount * 100);
  },

  /**
   * Generate payment reference
   */
  generateReference: (paymentMatchId: string): string => {
    const timestamp = Date.now();
    return `pm_${paymentMatchId}_${timestamp}`;
  },

  /**
   * Initialize Paystack payment popup
   */
  initializePayment: (config: PaystackConfig): void => {
    try {
      const paystackInstance = new PaystackPop();

      const paystackOptions = {
        key: config.publicKey,
        email: config.email,
        amount: config.amount,
        currency: config.currency,
        ref: paystackService.generateReference(
          config.metadata.payment_match_id
        ),
        channels: config.channels,
        metadata: {
          payment_match_id: config.metadata.payment_match_id,
          fund_id: config.metadata.fund_id,
          custom_fields: [
            {
              display_name: 'Payment Match ID',
              variable_name: 'payment_match_id',
              value: config.metadata.payment_match_id,
            },
            {
              display_name: 'Fund ID',
              variable_name: 'fund_id',
              value: config.metadata.fund_id,
            },
          ],
        },
        onSuccess: config.onSuccess,
        onCancel: config.onClose, // Handle cancel same as close
        onClose: config.onClose,
      };

      paystackInstance.newTransaction(paystackOptions);
    } catch (error) {
      throw new Error(
        `Failed to initialize Paystack payment: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },
};
