import {
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import type { WithdrawStatistics as WithdrawStatisticsType } from '../../types';

interface WithdrawStatisticsProps {
  statistics: WithdrawStatisticsType | null;
  loading: boolean;
}

export function WithdrawsStatistics({
  statistics,
  loading,
}: WithdrawStatisticsProps) {
  if (loading) {
    return (
      <div className='bg-background-secondary rounded-lg border border-background-tertiary p-6'>
        <div className='flex items-center justify-center h-32'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!statistics) {
    return (
      <div className='bg-background-secondary rounded-lg border border-background-tertiary p-6'>
        <p className='text-center text-foreground-secondary'>
          No statistics available
        </p>
      </div>
    );
  }

  const formatAmount = (fiatAmount?: number, cryptoAmount?: number) => {
    const fiat = fiatAmount || 0;
    const crypto = cryptoAmount || 0;

    if (fiat > 0 && crypto > 0) {
      return `₦${fiat.toLocaleString()} + ${crypto.toFixed(4)} SOL`;
    } else if (fiat > 0) {
      return `₦${fiat.toLocaleString()}`;
    } else if (crypto > 0) {
      return `${crypto.toFixed(4)} SOL`;
    }
    return '₦0';
  };

  const statCards = [
    {
      title: 'Total Withdrawals',
      value: (statistics.overview?.total_count || 0).toString(),
      icon: TrendingUp,
      color: 'text-primary',
      bgColor: 'bg-background-secondary',
    },
    {
      title: 'Total Amount',
      value: formatAmount(
        statistics.overview?.amount?.fiat,
        statistics.overview?.amount?.crypto
      ),
      icon: DollarSign,
      color: 'text-primary',
      bgColor: 'bg-background-secondary',
    },
    {
      title: 'Pending Withdrawals',
      value: (statistics.statuses?.pending?.total_count || 0).toString(),
      icon: Clock,
      color: 'text-primary-dark',
      bgColor: 'bg-background-secondary',
    },
    {
      title: 'Matched Withdrawals',
      value: (statistics.statuses?.matched?.total_count || 0).toString(),
      icon: AlertCircle,
      color: 'text-brand-gold-400',
      bgColor: 'bg-background-secondary',
    },
    {
      title: 'Completed Withdrawals',
      value: (statistics.statuses?.completed?.total_count || 0).toString(),
      icon: CheckCircle,
      color: 'text-primary',
      bgColor: 'bg-background-secondary',
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Main Statistics Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {statCards.map((stat, index) => (
          <div
            key={index}
            className='bg-background-secondary rounded-lg border border-background-tertiary p-6'
          >
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-foreground-secondary'>
                  {stat.title}
                </p>
                <p className='text-2xl font-bold text-foreground mt-1'>
                  {stat.value}
                </p>
              </div>
              <div
                className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}
              >
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Amount Breakdown by Status */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <div className='bg-background-secondary rounded-lg border border-background-tertiary p-6'>
          <h3 className='font-semibold text-primary mb-4'>
            Fiat Currency Breakdown (NGN)
          </h3>
          <div className='space-y-3'>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Total Amount</span>
              <span className='font-medium text-primary'>
                ₦{(statistics.overview?.amount?.fiat || 0).toLocaleString()}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Pending Amount</span>
              <span className='font-medium text-primary-dark'>
                ₦
                {(
                  statistics.statuses?.pending?.amount?.fiat || 0
                ).toLocaleString()}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Matched Amount</span>
              <span className='font-medium text-brand-gold-400'>
                ₦
                {(
                  statistics.statuses?.matched?.amount?.fiat || 0
                ).toLocaleString()}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>
                Completed Amount
              </span>
              <span className='font-medium text-primary'>
                ₦
                {(
                  statistics.statuses?.completed?.amount?.fiat || 0
                ).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        <div className='bg-background-secondary rounded-lg border border-background-tertiary p-6'>
          <h3 className='font-semibold text-primary mb-4'>
            Crypto Currency Breakdown (SOL)
          </h3>
          <div className='space-y-3'>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Total Amount</span>
              <span className='font-medium text-primary'>
                {(statistics.overview?.amount?.crypto || 0).toFixed(4)} SOL
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Pending Amount</span>
              <span className='font-medium text-primary-dark'>
                {(statistics.statuses?.pending?.amount?.crypto || 0).toFixed(4)}{' '}
                SOL
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>Matched Amount</span>
              <span className='font-medium text-brand-gold-400'>
                {(statistics.statuses?.matched?.amount?.crypto || 0).toFixed(4)}{' '}
                SOL
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-foreground-secondary'>
                Completed Amount
              </span>
              <span className='font-medium text-primary'>
                {(statistics.statuses?.completed?.amount?.crypto || 0).toFixed(
                  4
                )}{' '}
                SOL
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
