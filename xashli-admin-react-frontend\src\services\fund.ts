import { apiClient } from './api';
import type {
  Fund,
  FundListParams,
  FundListResponse,
  FundStats,
  CancelFundRequest,
  ConfirmPaymentRequest,
} from '../types/fund';
import type { ApiResponse } from '../types';

class FundService {
  /**
   * Get all funds with optional filters (admin only)
   */
  async getFunds(
    params?: FundListParams
  ): Promise<ApiResponse<FundListResponse>> {
    return apiClient.get<FundListResponse>('/funds', { params });
  }

  /**
   * Get a specific fund by ID
   */
  async getFund(id: string): Promise<ApiResponse<Fund>> {
    return apiClient.get<Fund>(`/funds/${id}`);
  }

  /**
   * Cancel a fund (admin only)
   */
  async cancelFund(
    id: string,
    data?: CancelFundRequest
  ): Promise<ApiResponse<Fund>> {
    return apiClient.post<Fund>(`/funds/${id}/cancel`, data);
  }

  /**
   * Confirm payment sent for a fund
   */
  async confirmPaymentSent(
    id: string,
    data: ConfirmPaymentRequest
  ): Promise<ApiResponse<Fund>> {
    return apiClient.post<Fund>(`/funds/${id}/confirm-payment-sent`, data);
  }
  /**
   * Get fund statistics for dashboard
   */
  async getFundStats(params?: {
    user_id?: string;
    email?: string;
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<FundStats>> {
    return apiClient.get<FundStats>('/funds/statistics', { params });
  }
}

export const fundService = new FundService();
