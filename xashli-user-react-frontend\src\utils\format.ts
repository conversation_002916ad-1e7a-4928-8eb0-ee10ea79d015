import moment from 'moment';
import type { Currency } from '../types/common';

/**
 * Format amounts for fiat and crypto currencies
 */
export function formatCurrencyAmount(
  amount: number | string | null | undefined,
  currency: Currency
): string {
  const num = Number(amount) || 0;

  return currency === 'fiat'
    ? `₦${Math.round(num).toLocaleString()}`
    : `${num.toFixed(4)} SOL`;
}

/**
 * Format large numbers with K, M, B suffixes
 */
export function formatCompactNumber(num: number | null | undefined): string {
  // Handle null, undefined, or invalid number values
  const numericValue = typeof num === 'number' && !isNaN(num) ? num : 0;

  return new Intl.NumberFormat('en-US', {
    notation: 'compact',
    maximumFractionDigits: 1,
  }).format(numericValue);
}

/**
 * Format date to readable string (converts UTC to local time)
 */
export function formatDate(date: string | Date): string {
  return moment(date).format('MMM D, YYYY');
}

/**
 * Format date and time to readable string (converts UTC to local time)
 */
export function formatDateTime(date: string | Date): string {
  return moment(date).format('MMM D, YYYY [at] h:mm A');
}

/**
 * Get relative time string (e.g., "2 hours ago") - handles UTC to local conversion
 */
export function getRelativeTime(date: string | Date): string {
  return moment(date).fromNow();
}

/**
 * Convert a string to title case (capitalize first letter of each word)
 * Handles underscores by replacing them with spaces
 */
export function toTitleCase(str: string): string {
  return str
    .replace('_', ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Convert 24-hour time format to 12-hour format with AM/PM
 * @param time24 - Time in 24-hour format (e.g., "14:30")
 * @returns Time in 12-hour format (e.g., "2:30 PM")
 */
export function formatTo12Hour(time24: string): string {
  if (!time24) return time24;

  const [hours, minutes] = time24.split(':').map(Number);

  if (isNaN(hours) || isNaN(minutes)) return time24;

  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12;

  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
}

/**
 * Format time range from 24-hour to 12-hour format with AM/PM
 * @param startTime - Start time in 24-hour format
 * @param endTime - End time in 24-hour format
 * @returns Formatted time range (e.g., "9:00 AM - 5:00 PM")
 */
export function formatTimeRange(startTime: string, endTime: string): string {
  return `${formatTo12Hour(startTime)} - ${formatTo12Hour(endTime)}`;
}

/**
 * Format time left until a target date in a compact, human-readable format
 * @param targetDate - Target date string or moment object, or null
 * @returns Formatted countdown string (e.g., "2d 14h 30m", "5h 30m 15s", "0s" when elapsed), or null if no date provided
 */
export function formatTimeLeft(
  targetDate: string | moment.Moment | null
): string | null {
  if (!targetDate) {
    return null;
  }

  const now = moment();
  const target = moment(targetDate);
  const duration = moment.duration(target.diff(now));

  if (duration.asMilliseconds() <= 0) {
    return '0s';
  }

  const days = Math.floor(duration.asDays());
  const hours = duration.hours();
  const minutes = duration.minutes();
  const seconds = duration.seconds();

  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 && days === 0) parts.push(`${seconds}s`);

  return parts.slice(0, 3).join(' ') || '0s';
}
