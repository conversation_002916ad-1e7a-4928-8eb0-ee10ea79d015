import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { LoadingSpinner } from '../ui/loading';
import { fundService } from '../../services/fund';
import type { Fund, FundFilters } from '../../types/fund';
import { format } from 'date-fns';

interface FundListProps {
  filters?: FundFilters;
  onFundSelect?: (fund: Fund) => void;
}

// Naira icon component
const NairaIcon = ({ className = 'h-4 w-4' }: { className?: string }) => (
  <span
    className={`inline-flex items-center justify-center font-bold ${className}`}
    style={{ fontSize: '0.875rem' }}
  >
    ₦
  </span>
);

export const FundList: React.FC<FundListProps> = ({
  filters = {},
  onFundSelect,
}) => {
  // Fetch funds with current filters
  const {
    data: fundsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['funds', filters],
    queryFn: () => fundService.getFunds(filters),
  });

  const funds = fundsResponse?.data?.funds || [];

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'matched':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Format currency display
  const formatCurrencyAmount = (amount: string | number, currency: string) => {
    const numericAmount =
      typeof amount === 'string' ? parseFloat(amount) : amount;
    if (currency === 'fiat') {
      return (
        <span className="flex items-center gap-1">
          <NairaIcon className="h-3 w-3" />
          {numericAmount.toLocaleString()}
        </span>
      );
    }
    return `${numericAmount.toFixed(3)} SOL`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="flex items-center justify-center">
            <LoadingSpinner size="lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            Failed to load funds. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Funds ({funds.length})</CardTitle>
      </CardHeader>
      <CardContent>
        {funds.length === 0 ? (
          <div className="text-center py-8 text-gray-500">No funds found.</div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>SN</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Currency</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Maturity</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {funds.map((fund, index) => (
                  <TableRow
                    key={fund.id}
                    className={
                      onFundSelect ? 'cursor-pointer hover:bg-gray-50' : ''
                    }
                    onClick={() => onFundSelect?.(fund)}
                  >
                    <TableCell>
                      <span className="text-sm font-medium text-gray-900">
                        {index + 1}
                      </span>
                    </TableCell>
                    <TableCell>
                      {fund.user ? (
                        <div>
                          <div className="font-medium">
                            {fund.user.full_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {fund.user.email}
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrencyAmount(fund.amount, fund.currency)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {fund.currency}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeVariant(fund.status)}>
                        {fund.status.charAt(0).toUpperCase() +
                          fund.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {format(new Date(fund.created_at), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>
                      {fund.maturity_date
                        ? format(new Date(fund.maturity_date), 'MMM dd, yyyy')
                        : 'TBD'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FundList;
