import { apiClient } from './api';
import type { ApiResponse } from '../types/common';
import type {
  VerificationRequest,
  VerificationStatus,
} from '../types/verification';

export const verificationService = {
  /**
   * Send email verification code
   */
  sendEmailCode: async (): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/verification/email/send');
  },

  /**
   * Verify email code
   */
  verifyEmailCode: async (
    data: VerificationRequest
  ): Promise<ApiResponse<{ message: string }>> => {
    return apiClient.post<{ message: string }>('/verification/email/verify', {
      code: data.email_code,
    });
  },

  /**
   * Get verification status
   */
  getStatus: async (): Promise<ApiResponse<VerificationStatus>> => {
    return apiClient.get<VerificationStatus>('/verification/status');
  },
};
