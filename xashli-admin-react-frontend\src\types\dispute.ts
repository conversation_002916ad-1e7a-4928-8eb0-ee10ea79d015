// PaymentDispute types based on the database schema and API
import type { PaginationMeta, Currency } from './common';

export interface PaymentDispute {
  id: string;
  payment_match_id: string;
  dispute_user_id: string;
  fund_user_id: string;
  withdraw_user_id: string;
  resolve_user_id?: string;
  reason: string;
  status: 'under_review' | 'resolved' | 'rejected';
  resolution?: 'confirmed' | 'cancelled';
  resolution_notes?: string;
  disputed_at: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;

  // Relations
  payment_match?: {
    id: string;
    fund_id: string;
    withdraw_id: string;
    amount: string;
    status: string;
    fund?: {
      id: string;
      currency: Currency;
      amount: string;
      user?: {
        id: string;
        full_name: string;
        email: string;
      };
    };
    withdraw?: {
      id: string;
      user?: {
        id: string;
        full_name: string;
        email: string;
      };
    };
  };
  dispute_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  fund_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  withdraw_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  resolve_user?: {
    id: string;
    full_name: string;
    email: string;
  };
}

// Filter and pagination types
export interface DisputeFilters {
  status?: 'under_review' | 'resolved' | 'rejected';
  resolution?: 'confirmed' | 'cancelled';
  user_id?: string; // Filter by user (admin only) - changed to string for UUID
  admin_id?: string; // Filter by admin (admin only) - changed to string for UUID
  start_date?: string; // Date range filter
  end_date?: string; // Date range filter
  search?: string; // Search in reason, resolution_notes
  page?: number;
  per_page?: number;
  sort_field?: 'disputed_at' | 'resolved_at' | 'amount' | 'status';
  sort_direction?: 'asc' | 'desc';
}

export interface PaginatedDisputes {
  disputes: PaymentDispute[];
  pagination: PaginationMeta;
}

// Request types for dispute actions
export interface ResolveDisputeRequest {
  resolution: 'confirmed' | 'cancelled';
  resolution_notes?: string;
}

export interface RejectDisputeRequest {
  resolution_notes?: string;
}

// Statistics types
export interface DisputeStats {
  counts: {
    total_count: number;
    statuses: {
      under_review: number;
      resolved: number;
      rejected: number;
    };
    resolutions: {
      confirmed: number;
      cancelled: number;
    };
  };
}

// Form types for dispute resolution modals
export interface DisputeResolveFormData {
  resolution: 'confirmed' | 'cancelled';
  resolution_notes?: string;
}

export interface DisputeRejectFormData {
  resolution_notes?: string;
}

// Export all dispute-related types
export type DisputeStatus = 'under_review' | 'resolved' | 'rejected';
export type DisputeResolution = 'confirmed' | 'cancelled';
