import React from 'react';
import { Button } from '@/components/ui/Button';
import { User, LogOut, Menu } from 'lucide-react';
import { useAuth, useSidebar } from '@/contexts';

export const TopBar: React.FC = () => {
  const { user, logout } = useAuth();
  const { toggle, isMobile } = useSidebar();
  const handleLogout = async () => {
    await logout();
  };

  return (
    <header className='h-16 bg-background border-b border-border flex items-center px-6'>
      {/* Left Section - Mobile Menu */}
      <div className='flex items-center'>
        {isMobile && (
          <Button
            variant='ghost'
            size='sm'
            onClick={toggle}
            className='lg:hidden'
          >
            <Menu className='h-5 w-5' />
          </Button>
        )}
      </div>

      {/* Center Section - Can be used for title or breadcrumb if needed */}
      <div className='flex-1'></div>

      {/* Right Section */}
      <div className='flex items-center space-x-4'>
        {/* User Profile */}
        <div className='flex items-center space-x-3'>
          <div className='text-right'>
            <p className='text-sm font-medium text-foreground'>
              {((user?.full_name || user?.email) ?? '').length > 10
                ? `${(user?.full_name || user?.email)?.slice(0, 10)}...`
                : user?.full_name || user?.email}
            </p>
          </div>

          <div className='h-8 w-8 bg-primary rounded-full flex items-center justify-center'>
            <User className='h-4 w-4 text-primary-foreground' />
          </div>
        </div>{' '}
        {/* Logout - Aligned to the end */}
        <Button
          variant='secondary'
          size='sm'
          onClick={handleLogout}
          className='ml-2'
        >
          <LogOut className='h-4 w-4 mr-2' />
          Logout
        </Button>
      </div>
    </header>
  );
};
