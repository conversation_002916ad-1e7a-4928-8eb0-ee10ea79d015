import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { platformSettingsService } from '../services/platformSettings';
import type {
  CreatePlatformSettingRequest,
  UpdatePlatformSettingRequest,
} from '../types/platformSettings';

// Query Keys
export const platformSettingsKeys = {
  all: ['platform-settings'] as const,
  detail: (id: string) => ['platform-settings', id] as const,
  byKey: (key: string) => ['platform-settings', 'by-key', key] as const,
} as const;

// Hooks for Platform Settings
export const usePlatformSettings = () => {
  return useQuery({
    queryKey: platformSettingsKeys.all,
    queryFn: platformSettingsService.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePlatformSetting = (id: string) => {
  return useQuery({
    queryKey: platformSettingsKeys.detail(id),
    queryFn: () => platformSettingsService.getById(id),
    enabled: !!id,
  });
};

export const usePlatformSettingByKey = (key: string) => {
  return useQuery({
    queryKey: platformSettingsKeys.byKey(key),
    queryFn: () => platformSettingsService.getByKey(key),
    enabled: !!key,
  });
};

export const useCreatePlatformSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePlatformSettingRequest) =>
      platformSettingsService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: platformSettingsKeys.all });
      toast.success('Platform setting created successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to create setting');
    },
  });
};

export const useUpdatePlatformSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdatePlatformSettingRequest;
    }) => platformSettingsService.update(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: platformSettingsKeys.all });
      queryClient.invalidateQueries({
        queryKey: platformSettingsKeys.detail(variables.id),
      });
      toast.success('Platform setting updated successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to update setting');
    },
  });
};

export const useDeletePlatformSetting = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => platformSettingsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: platformSettingsKeys.all });
      toast.success('Platform setting deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to delete setting');
    },
  });
};
