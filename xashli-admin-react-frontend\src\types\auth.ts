// User types matching <PERSON><PERSON> backend
// Note: Common types (ApiResponse, ApiError, PaginationMeta, etc.) moved to common.ts
export interface User {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  role: 'user' | 'admin';
  is_active: boolean;
  referral_code: string;
  total_funds_amount: number;
  total_withdraws_amount: number;
  referee_count: number;
  created_at: string;
  updated_at: string;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  authorization: {
    access_token: string;
    refresh_token: string;
    type: string;
    expires_in: number;
  };
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  password_confirmation: string;
}

// Form types
export interface LoginFormData {
  email: string;
  password: string;
  remember?: boolean;
}

export interface ForgotPasswordFormData {
  email: string;
}

export interface ResetPasswordFormData {
  password: string;
  password_confirmation: string;
}
