import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils';
import { useSidebar } from '../../contexts';
import {
  LayoutDashboard,
  Users,
  CreditCard,
  ArrowLeftRight,
  ShieldAlert,
  Settings,
  FileText,
  ChevronDown,
  Wallet,
  X,
  TrendingUp,
  Trophy,
  Clock,
} from 'lucide-react';

interface SidebarProps {
  className?: string;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Users',
    href: '/users',
    icon: Users,
  },
  {
    title: 'Transactions',
    href: '/transactions',
    icon: CreditCard,
    children: [
      {
        title: 'All Funds',
        href: '/transactions/funds',
        icon: Wallet,
      },
      {
        title: 'All Withdraws',
        href: '/transactions/withdraws',
        icon: CreditCard,
      },
      {
        title: 'Payment Matches',
        href: '/transactions/payment-matches',
        icon: ArrowLeftRight,
      },
      {
        title: 'Manual Matching',
        href: '/transactions/manual-matching',
        icon: ArrowLeftRight,
      },
    ],
  },
  {
    title: 'Disputes',
    href: '/disputes',
    icon: ShieldAlert,
  },
  {
    title: 'Platform Fees',
    href: '/platform-fees',
    icon: TrendingUp,
  },
  {
    title: 'Platform Settings',
    href: '/settings',
    icon: Settings,
    children: [
      {
        title: 'Payment Methods',
        href: '/settings/payment-methods',
        icon: Wallet,
      },
      {
        title: 'Funding Schedule',
        href: '/settings/funding-schedule',
        icon: Clock,
      },
      {
        title: 'Payment Matching',
        href: '/settings/payment_matching',
        icon: ArrowLeftRight,
      },
      {
        title: 'Fee Management',
        href: '/settings/fee_management',
        icon: CreditCard,
      },
      {
        title: 'Elite Privileges',
        href: '/settings/elite_privileges',
        icon: Trophy,
      },
      {
        title: 'System Config',
        href: '/settings/system_config',
        icon: Settings,
      },
    ],
  },
  {
    title: 'Activity Logs',
    href: '/activity-logs',
    icon: FileText,
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);
  const { isOpen, isMobile, close } = useSidebar();

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href) ? prev.filter(item => item !== href) : [...prev, href]
    );
  };

  const isActive = (href: string) => {
    // For exact match always return true first
    if (location.pathname === href) {
      return true;
    }

    // Special handling for dashboard - only exact match
    if (href === '/dashboard') {
      return false;
    }

    // Special handling for /users - exact match and child routes
    if (href === '/users') {
      return (
        location.pathname === '/users' ||
        location.pathname.startsWith('/users/')
      );
    }

    // For other paths, check if current path starts with href followed by a slash
    return location.pathname.startsWith(href + '/');
  };

  const isExpanded = (href: string) => expandedItems.includes(href);

  React.useEffect(() => {
    // Auto-expand parent items for active routes
    navigationItems.forEach(item => {
      if (item.children) {
        const hasActiveChild = item.children.some(child =>
          isActive(child.href)
        );
        if (hasActiveChild && !expandedItems.includes(item.href)) {
          setExpandedItems(prev => [...prev, item.href]);
        }
      }
    });
  }, [location.pathname]);

  const renderNavItem = (item: NavItem, level = 0) => {
    const Icon = item.icon;
    const hasChildren = item.children && item.children.length > 0;
    const active = isActive(item.href);
    const expanded = isExpanded(item.href);

    return (
      <div key={item.href}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.href)}
            className={cn(
              'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors',
              level > 0 && 'ml-6',
              active
                ? 'bg-brand-gold-100 text-brand-gold-800 border-r-2 border-brand-gold-500'
                : 'text-brand-grey-300 hover:text-white hover:bg-brand-grey-800'
            )}
          >
            <div className="flex items-center">
              <Icon className="mr-3 h-5 w-5" />
              <span>{item.title}</span>
              {item.badge && (
                <span className="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-gold-100 text-brand-gold-800">
                  {item.badge}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform',
                expanded && 'transform rotate-180'
              )}
            />
          </button>
        ) : (
          <Link
            to={item.href}
            onClick={() => isMobile && close()}
            className={cn(
              'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              level > 0 && 'ml-6',
              active
                ? 'bg-brand-gold-100 text-brand-gold-800 border-r-2 border-brand-gold-500'
                : 'text-brand-grey-300 hover:text-white hover:bg-brand-grey-800'
            )}
          >
            <Icon className="mr-3 h-5 w-5" />
            <span>{item.title}</span>
            {item.badge && (
              <span className="ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-brand-gold-100 text-brand-gold-800">
                {item.badge}
              </span>
            )}
          </Link>
        )}

        {hasChildren && expanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };
  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={close}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'flex flex-col h-screen bg-brand-black transition-transform duration-300 ease-in-out z-50',
          isMobile ? 'fixed inset-y-0 left-0 w-64' : 'relative',
          isMobile && !isOpen && '-translate-x-full',
          className
        )}
      >
        {/* Mobile Close Button */}
        {isMobile && (
          <div className="flex items-center justify-end p-4 lg:hidden">
            <button
              onClick={close}
              className="text-brand-grey-300 hover:text-white"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        )}

        {/* Logo */}
        <div
          className={cn(
            'flex items-center justify-center px-4 border-b border-brand-grey-800',
            isMobile ? 'h-12' : 'h-16'
          )}
        >
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-lg">
              <img
                src="/assets/xashli_logo.png"
                alt="Xashli Logo"
                className="h-6 w-6 object-contain"
              />
            </div>
            <div className="ml-3">
              <h1 className="text-lg font-bold text-white">Xashli</h1>
              <p className="text-xs text-brand-grey-400">Admin Panel</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {navigationItems.map(item => renderNavItem(item))}
        </nav>

        {/* Footer */}
        <div className="px-4 py-4 border-t border-brand-grey-800">
          <div className="text-xs text-brand-grey-400 text-center">
            Xashli Admin v1.0
          </div>
        </div>
      </div>
    </>
  );
};
