import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { ForgotPasswordPage } from './pages/auth/ForgotPasswordPage';
import { ResetPasswordPage } from './pages/auth/ResetPasswordPage';
import { VerificationPage } from './pages/auth/VerificationPage';
import { DashboardPage } from './pages/DashboardPage';
import { HomePage } from './pages/HomePage';
import { PaymentMethodsPage } from './pages/payment-methods/PaymentMethodsPage';
import { CreatePaymentMethodPage } from './pages/payment-methods/CreatePaymentMethodPage';
import { EditPaymentMethodPage } from './pages/payment-methods/EditPaymentMethodPage';
import { PaymentMethodDetailsPage } from './pages/payment-methods/PaymentMethodDetailsPage';
import { FundsPage } from './pages/funds/FundsPage';
import { CreateFundPage } from './pages/funds/CreateFundPage';
import { FundDetailsPage } from './pages/funds/FundDetailsPage';
import { FundStatisticsPage } from './pages/funds/FundStatisticsPage';
import { WithdrawsPage } from './pages/withdraws/WithdrawsPage';
import { WithdrawDetailsPage } from './pages/withdraws/WithdrawDetailsPage';
import { WithdrawStatisticsPage } from './pages/withdraws/WithdrawStatisticsPage';
import { ProfilePage } from './pages/profile/ProfilePage';
import { EditProfilePage } from './pages/profile/EditProfilePage';
import { ReferralsPage } from './pages/referrals/ReferralsPage';
import { ReferralBonusesPage } from './pages/referrals/ReferralBonusesPage';
import { AuthLayout } from './components/layouts/AuthLayout';
import { AppLayout } from './components/layouts/AppLayout';
import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <div className='min-h-screen bg-background text-foreground'>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public Home Page */}
              <Route path='/' element={<HomePage />} />

              {/* Authentication Routes */}
              <Route path='/auth' element={<AuthLayout />}>
                <Route path='login' element={<LoginPage />} />
                <Route path='register' element={<RegisterPage />} />
                <Route path='verification' element={<VerificationPage />} />
                <Route
                  path='forgot-password'
                  element={<ForgotPasswordPage />}
                />
                <Route path='reset-password' element={<ResetPasswordPage />} />
              </Route>

              {/* Protected Routes */}
              <Route
                path='/dashboard'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <DashboardPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Payment Methods Routes */}
              <Route
                path='/payment-methods'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <PaymentMethodsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/payment-methods/create'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <CreatePaymentMethodPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/payment-methods/:id/edit'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <EditPaymentMethodPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/payment-methods/:id'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <PaymentMethodDetailsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Funds Routes */}
              <Route
                path='/funds'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <FundsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/funds/create'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <CreateFundPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/funds/statistics'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <FundStatisticsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/funds/:id'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <FundDetailsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Withdraws Routes */}
              <Route
                path='/withdraws'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <WithdrawsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/withdraws/statistics'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <WithdrawStatisticsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/withdraws/:id'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <WithdrawDetailsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Profile Routes */}
              <Route
                path='/profile'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <ProfilePage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/profile/edit'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <EditProfilePage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />

              {/* Referral Routes */}
              <Route
                path='/referrals'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <ReferralsPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path='/referrals/bonuses'
                element={
                  <ProtectedRoute>
                    <AppLayout>
                      <ReferralBonusesPage />
                    </AppLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </Router>
        </AuthProvider>
        <Toaster />
      </div>
    </QueryClientProvider>
  );
}

export default App;
