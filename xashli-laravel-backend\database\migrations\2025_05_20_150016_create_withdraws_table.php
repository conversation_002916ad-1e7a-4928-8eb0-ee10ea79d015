<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('withdraws', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('fund_id');
            $table->decimal('base_withdrawable_amount', 20, 8);
            $table->decimal('available_referral_bonus', 20, 8);
            $table->decimal('withdrawable_referral_bonus', 20, 8);
            $table->decimal('total_withdrawable_amount', 20, 8);
            $table->decimal('amount_matched', 20, 8)->default(0);
            $table->enum('status', ['pending', 'matched', 'completed'])->default('pending');
            $table->uuid('payment_method_id');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('fund_id')->references('id')->on('funds')->onDelete('restrict');
            $table->foreign('payment_method_id')->references('id')->on('payment_methods')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdraws');
    }
};
