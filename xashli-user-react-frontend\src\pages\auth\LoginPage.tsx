import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Eye, EyeOff } from 'lucide-react';
import { useLogin, usePasswordVisibility } from '@/hooks/auth';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginPage() {
  const { isLoading, handleLogin } = useLogin();
  const { showPassword, togglePasswordVisibility } = usePasswordVisibility();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold text-foreground'>Welcome back</h2>
        <p className='mt-2 text-foreground-secondary'>
          Sign in to your account to continue
        </p>
      </div>

      <Card>
        <CardContent className='pt-6'>
          <form onSubmit={handleSubmit(handleLogin)} className='space-y-4'>
            <Input
              label='Email'
              type='email'
              placeholder='Enter your email'
              error={errors.email?.message}
              {...register('email')}
            />

            <div className='relative'>
              <Input
                label='Password'
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter your password'
                error={errors.password?.message}
                {...register('password')}
              />
              <button
                type='button'
                className='absolute right-3 bottom-[10px] text-foreground-muted hover:text-foreground'
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <div className='flex justify-end'>
              <Link
                to='/auth/forgot-password'
                className='text-sm text-primary hover:text-primary/90 font-medium'
              >
                Forgot password?
              </Link>
            </div>

            <Button type='submit' className='w-full' isLoading={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          <p className='mt-6 text-center text-sm text-foreground-secondary'>
            Don't have an account?{' '}
            <Link
              to='/auth/register'
              className='font-medium text-primary hover:text-primary/90'
            >
              Sign up
            </Link>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
