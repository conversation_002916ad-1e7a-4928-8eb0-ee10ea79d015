import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { ArrowLeft, Mail, CheckCircle } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Card, CardContent } from '../../components/ui/card';
import { authService, handleApiError } from '../../services';
import type { ForgotPasswordFormData } from '../../types';
import { isValidEmail, showToast } from '../../utils';

export const ForgotPasswordPage: React.FC = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ForgotPasswordFormData>();

  const email = watch('email');
  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsLoading(true);

      const response = await authService.forgotPassword(data);

      if (response.status === 'success') {
        setIsSubmitted(true);
        showToast.success('Password reset email sent successfully!');
      } else {
        showToast.error(response.message);
      }
    } catch (err) {
      const apiError = handleApiError(err);
      showToast.error(apiError.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-grey-50 to-brand-grey-100 flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-brand-black">
              Check your email
            </h1>
            <p className="text-brand-grey-600 mt-2">
              We've sent a password reset link to your email
            </p>
          </div>

          {/* Success Card */}
          <Card className="shadow-xl border-0">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <p className="text-brand-grey-600">
                  We've sent a password reset link to{' '}
                  <span className="font-semibold text-brand-black">
                    {email}
                  </span>
                </p>
                <p className="text-sm text-brand-grey-500">
                  Please check your email and click the link to reset your
                  password. The link will expire in 1 hour.
                </p>
                <div className="pt-4">
                  <Link to="/auth/login">
                    <Button variant="outline" className="w-full">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Sign In
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Resend */}
          <div className="text-center">
            <p className="text-sm text-brand-grey-500">
              Didn't receive the email?{' '}
              <button
                onClick={() => setIsSubmitted(false)}
                className="text-brand-gold-600 hover:text-brand-gold-700 font-medium"
              >
                Try again
              </button>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-grey-50 to-brand-grey-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 bg-brand-black rounded-full flex items-center justify-center mb-4">
            <Mail className="h-8 w-8 text-brand-gold-500" />
          </div>
          <h1 className="text-3xl font-bold text-brand-black">
            Forgot Password?
          </h1>
          <p className="text-brand-grey-600 mt-2">
            No worries, we'll send you reset instructions
          </p>
        </div>

        {/* Form */}
        <Card className="shadow-xl border-0">
          <CardContent className="pt-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email Field */}
              <div className="space-y-2">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-brand-black"
                >
                  Email Address
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className={`${errors.email ? 'border-red-500 focus-visible:ring-red-500' : ''}`}
                  {...register('email', {
                    required: 'Email is required',
                    validate: value =>
                      isValidEmail(value) ||
                      'Please enter a valid email address',
                  })}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full bg-brand-gold-500 hover:bg-brand-gold-600 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    <span>Sending...</span>
                  </div>
                ) : (
                  'Send Reset Link'
                )}
              </Button>
              {/* Back to Login */}
              <div className="text-center pt-2">
                <Link
                  to="/auth/login"
                  className="inline-flex items-center text-sm text-brand-grey-600 hover:text-brand-black"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Sign In
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center">
          <p className="text-sm text-brand-grey-500">
            © 2025 Xashli. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};
