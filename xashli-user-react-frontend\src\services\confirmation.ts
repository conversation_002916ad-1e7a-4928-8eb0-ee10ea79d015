import type { ApiResponse } from '@/types';
import type {
  ConfirmationCodeGenerateRequest,
  ConfirmationGenerateResponse,
  ConfirmationVerifyRequest,
  ConfirmationVerifyResponse,
} from '../types/confirmation';
import { apiClient } from './api';

export const confirmationService = {
  async generate(
    data: ConfirmationCodeGenerateRequest
  ): Promise<ApiResponse<ConfirmationGenerateResponse>> {
    return await apiClient.post<ConfirmationGenerateResponse>(
      '/confirmation/generate',
      data
    );
  },

  async verify(
    data: ConfirmationVerifyRequest
  ): Promise<ApiResponse<ConfirmationVerifyResponse>> {
    return await apiClient.post<ConfirmationVerifyResponse>(
      '/confirmation/verify',
      data
    );
  },
};
