<?php

namespace App\Models;

use App\Enums\ElitePrivilegeLevel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject, MustVerifyEmail
{
    use HasFactory, HasUuids, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'full_name',
        'email',
        'phone',
        'profile_image',
        'password',
        'referral_code',
        'referrer_code',
        'referrer_id',
        'referee_count',
        'is_active',
        'deactivated_at',
        'deactivation_duration',
        'role',
        'has_premium_privilege',
        'has_elite_privilege',
        'elite_privilege_level',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'deactivated_at' => 'datetime',
            'deactivation_duration' => 'integer',
            'referee_count' => 'integer',
            'has_premium_privilege' => 'boolean',
            'has_elite_privilege' => 'boolean',
            'elite_privilege_level' => 'integer',
        ];
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [
            'role' => $this->role,
            'is_active' => $this->is_active,
        ];
    }

    /**
     * Get the payment methods for the user.
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class);
    }

    /**
     * Get the funds for the user.
     */
    public function funds(): HasMany
    {
        return $this->hasMany(Fund::class);
    }

    /**
     * Get the withdraws for the user.
     */
    public function withdraws(): HasMany
    {
        return $this->hasMany(Withdraw::class);
    }

    /**
     * Get the user stats for the user.
     */
    public function stats(): HasOne
    {
        return $this->hasOne(UserStat::class);
    }

    /**
     * Get the referees (direct referrals) for the user.
     */
    public function referees(): HasMany
    {
        return $this->hasMany(User::class, 'referrer_id');
    }

    /**
     * Get the referrer user.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the verification codes for the user.
     */
    public function verificationCodes(): HasMany
    {
        return $this->hasMany(VerificationCode::class);
    }

    /**
     * Get the referral bonuses for the user.
     */
    public function referralBonuses(): HasMany
    {
        return $this->hasMany(ReferralBonus::class, 'referrer_user_id');
    }

    /**
     * Get the referral withdraws for the user.
     */
    public function referralWithdraws(): HasMany
    {
        return $this->hasMany(ReferralWithdraw::class);
    }

    /**
     * Get the payment disputes initiated by the user.
     */
    public function paymentDisputes(): HasMany
    {
        return $this->hasMany(PaymentDispute::class, 'dispute_user_id');
    }

    /**
     * Get the payment disputes resolved by the admin.
     */
    public function resolvedDisputes(): HasMany
    {
        return $this->hasMany(PaymentDispute::class, 'resolve_user_id');
    }

    /**
     * Check if the user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if the user has verified their email.
     */
    public function hasVerifiedEmail(): bool
    {
        return ! is_null($this->email_verified_at);
    }

    /**
     * Mark the user's email as verified.
     */
    public function markEmailAsVerified(): void
    {
        $this->email_verified_at = now();
        $this->save();
    }

    /**
     * Calculate total level 1 referee funding by currency.
     *
     * @param  string  $currency  'fiat' or 'crypto'
     */
    public function totalLevel1RefereeFunding(string $currency): float
    {
        return (float) $this->referees()
            ->join('funds', 'users.id', '=', 'funds.user_id')
            ->where('funds.currency', $currency)
            ->where('funds.status', 'completed')
            ->sum('funds.amount');
    }

    /**
     * Get the admin account for payment matching.
     *
     * @param  string  $currency  'fiat' or 'crypto'
     */
    public static function getAdminAccount(string $currency): ?self
    {
        $paymentType = $currency === 'fiat' ? 'bank' : 'crypto';

        return self::where('role', 'admin')
            ->where('is_active', true)
            ->whereHas('paymentMethods', function ($query) use ($paymentType) {
                $query->where('type', $paymentType)
                    ->where('status', 'active');
            })
            ->with(['stats', 'paymentMethods' => function ($query) use ($paymentType) {
                $query->where('type', $paymentType)
                    ->where('status', 'active');
            }])
            ->first();
    }

    protected static function booted()
    {
        // Update referral counts when a new user is created
        static::created(function ($user) {
            if ($user->referrer_id) {
                // Update counts for direct referrer
                $referrerStat = UserStat::firstOrCreate(['user_id' => $user->referrer_id], [
                    'updated_at' => now(),
                ]);
                $referrerStat->updateRefereeCounts();

                // Get referrer's referrer (level 2)
                $referrer = User::find($user->referrer_id);
                if ($referrer && $referrer->referrer_id) {
                    $level2Stat = UserStat::firstOrCreate(['user_id' => $referrer->referrer_id], [
                        'updated_at' => now(),
                    ]);
                    $level2Stat->updateRefereeCounts();

                    // Get level 2's referrer (level 3)
                    $level2User = User::find($referrer->referrer_id);
                    if ($level2User && $level2User->referrer_id) {
                        $level3Stat = UserStat::firstOrCreate(['user_id' => $level2User->referrer_id], [
                            'updated_at' => now(),
                        ]);
                        $level3Stat->updateRefereeCounts();
                    }
                }
            }
        });
    }

    /**
     * Check if user has premium privilege (fast maturity).
     */
    public function hasPremiumPrivilege(): bool
    {
        return $this->has_premium_privilege ?? false;
    }

    /**
     * Check if user has elite privilege (enhanced fund limits).
     */
    public function hasElitePrivilege(): bool
    {
        return $this->has_elite_privilege ?? false;
    }

    /**
     * Get the user's elite privilege level.
     */
    public function getElitePrivilegeLevel(): ElitePrivilegeLevel
    {
        return ElitePrivilegeLevel::from($this->elite_privilege_level ?? 0);
    }

    /**
     * Check if user has at least the specified elite level.
     */
    public function hasEliteLevel(ElitePrivilegeLevel $level): bool
    {
        return $this->elite_privilege_level >= $level->value;
    }

    /**
     * Check if user has any elite privilege level.
     */
    public function hasAnyElitePrivilege(): bool
    {
        return $this->elite_privilege_level > 0;
    }

    /**
     * Check if the user's deactivation has expired and should be reactivated.
     */
    public function shouldBeReactivated(): bool
    {
        if ($this->is_active || ! $this->deactivated_at || ! $this->deactivation_duration) {
            return false;
        }

        $reactivationTime = $this->deactivated_at->addMinutes($this->deactivation_duration);

        return now()->greaterThanOrEqualTo($reactivationTime);
    }

    /**
     * Get the remaining deactivation time in minutes.
     */
    public function getRemainingDeactivationMinutes(): int
    {
        if ($this->is_active || ! $this->deactivated_at || ! $this->deactivation_duration) {
            return 0;
        }

        $reactivationTime = $this->deactivated_at->addMinutes($this->deactivation_duration);
        $remainingMinutes = now()->diffInMinutes($reactivationTime, false);

        return max(0, $remainingMinutes);
    }

    /**
     * Get the remaining deactivation time formatted as an array with days, hours, and minutes.
     */
    public function getRemainingDeactivationTime(): array
    {
        $totalMinutes = $this->getRemainingDeactivationMinutes();

        if ($totalMinutes <= 0) {
            return ['days' => 0, 'hours' => 0, 'minutes' => 0];
        }

        $days = intval($totalMinutes / (24 * 60));
        $hours = intval(($totalMinutes % (24 * 60)) / 60);
        $minutes = $totalMinutes % 60;

        return [
            'days' => $days,
            'hours' => $hours,
            'minutes' => $minutes,
        ];
    }

    /**
     * Automatically reactivate the user if deactivation period has expired.
     */
    public function autoReactivateIfExpired(): bool
    {
        if ($this->shouldBeReactivated()) {
            $this->is_active = true;
            $this->deactivated_at = null;
            $this->deactivation_duration = null;
            $this->save();

            return true;
        }

        return false;
    }

    /**
     * Deactivate the user for a specific duration.
     */
    public function deactivateFor(int $durationMinutes): void
    {
        $this->is_active = false;
        $this->deactivated_at = now();
        $this->deactivation_duration = $durationMinutes;
        $this->save();
    }

    /**
     * Permanently deactivate the user (no auto-reactivation).
     */
    public function deactivatePermanently(): void
    {
        $this->is_active = false;
        $this->deactivated_at = now();
        $this->deactivation_duration = null;
        $this->save();
    }

    /**
     * Manually reactivate the user.
     */
    public function reactivate(): void
    {
        $this->is_active = true;
        $this->deactivated_at = null;
        $this->deactivation_duration = null;
        $this->save();
    }
}
