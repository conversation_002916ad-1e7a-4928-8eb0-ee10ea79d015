<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserStat extends Model
{
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'total_fiat_funded',
        'total_crypto_funded',
        'total_fiat_withdrawn',
        'total_crypto_withdrawn',
        'total_fiat_referral_bonus_earned',
        'total_crypto_referral_bonus_earned',
        'available_fiat_referral_bonus',
        'available_crypto_referral_bonus',
        'consumed_fiat_referral_bonus',
        'consumed_crypto_referral_bonus',
        'referee_count_level1',
        'referee_count_level2',
        'referee_count_level3',
        'next_withdraw_eligibility',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'total_fiat_funded' => 'decimal:8',
        'total_crypto_funded' => 'decimal:8',
        'total_fiat_withdrawn' => 'decimal:8',
        'total_crypto_withdrawn' => 'decimal:8',
        'pending_fiat_withdrawal' => 'decimal:8',
        'pending_crypto_withdrawal' => 'decimal:8',
        'total_fiat_referral_bonus_earned' => 'decimal:8',
        'total_crypto_referral_bonus_earned' => 'decimal:8',
        'available_fiat_referral_bonus' => 'decimal:8',
        'available_crypto_referral_bonus' => 'decimal:8',
        'consumed_fiat_referral_bonus' => 'decimal:8',
        'consumed_crypto_referral_bonus' => 'decimal:8',
        'referee_count_level1' => 'integer',
        'referee_count_level2' => 'integer',
        'referee_count_level3' => 'integer',
        'next_withdraw_eligibility' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the user that owns the stats.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Increment the total fiat funded amount.
     */
    public function incrementTotalFiatFunded(float $amount): bool
    {
        $this->total_fiat_funded = bcadd($this->total_fiat_funded, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Increment the total crypto funded amount.
     */
    public function incrementTotalCryptoFunded(float $amount): bool
    {
        $this->total_crypto_funded = bcadd($this->total_crypto_funded, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the total fiat funded amount.
     */
    public function decrementTotalFiatFunded(float $amount): bool
    {
        $this->total_fiat_funded = max(0, bcsub($this->total_fiat_funded, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the total crypto funded amount.
     */
    public function decrementTotalCryptoFunded(float $amount): bool
    {
        $this->total_crypto_funded = max(0, bcsub($this->total_crypto_funded, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Update the next withdraw eligibility date.
     */
    public function updateNextWithdrawEligibility(\DateTime $date): bool
    {
        $this->next_withdraw_eligibility = $date;
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Get the available referral bonus amount for a specific currency.
     *
     * @param  string  $currency  'fiat' or 'crypto'
     */
    public function getAvailableReferralBonus(string $currency): float
    {
        return $currency === 'fiat'
            ? (float) $this->available_fiat_referral_bonus
            : (float) $this->available_crypto_referral_bonus;
    }

    /**
     * Get available fiat referral bonus.
     */
    public function getAvailableFiatReferralBonus(): float
    {
        return (float) $this->available_fiat_referral_bonus;
    }

    /**
     * Get available crypto referral bonus.
     */
    public function getAvailableCryptoReferralBonus(): float
    {
        return (float) $this->available_crypto_referral_bonus;
    }

    /**
     * Increment the available fiat referral bonus amount.
     */
    public function incrementAvailableFiatReferralBonus(float $amount): bool
    {
        $this->total_fiat_referral_bonus_earned = bcadd($this->total_fiat_referral_bonus_earned, $amount, 8);
        $this->available_fiat_referral_bonus = bcadd($this->available_fiat_referral_bonus, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Increment the available crypto referral bonus amount.
     */
    public function incrementAvailableCryptoReferralBonus(float $amount): bool
    {
        $this->total_crypto_referral_bonus_earned = bcadd($this->total_crypto_referral_bonus_earned, $amount, 8);
        $this->available_crypto_referral_bonus = bcadd($this->available_crypto_referral_bonus, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Consume fiat referral bonus for a withdrawal.
     */
    public function consumeFiatReferralBonus(float $amount): bool
    {
        $amountToConsume = min($amount, $this->available_fiat_referral_bonus);
        $this->available_fiat_referral_bonus = max(0, bcsub($this->available_fiat_referral_bonus, $amountToConsume, 8));
        $this->consumed_fiat_referral_bonus = bcadd($this->consumed_fiat_referral_bonus, $amountToConsume, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Consume crypto referral bonus for a withdrawal.
     */
    public function consumeCryptoReferralBonus(float $amount): bool
    {
        $amountToConsume = min($amount, $this->available_crypto_referral_bonus);
        $this->available_crypto_referral_bonus = max(0, bcsub($this->available_crypto_referral_bonus, $amountToConsume, 8));
        $this->consumed_crypto_referral_bonus = bcadd($this->consumed_crypto_referral_bonus, $amountToConsume, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Get the total number of referees across all levels.
     */
    public function getTotalRefereeCount(): int
    {
        return $this->referee_count_level1 +
               $this->referee_count_level2 +
               $this->referee_count_level3;
    }

    /**
     * Increment the total fiat withdrawn amount.
     */
    public function incrementTotalFiatWithdrawn(float $amount): bool
    {
        $this->total_fiat_withdrawn = bcadd($this->total_fiat_withdrawn, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Increment the total crypto withdrawn amount.
     */
    public function incrementTotalCryptoWithdrawn(float $amount): bool
    {
        $this->total_crypto_withdrawn = bcadd($this->total_crypto_withdrawn, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Increment the pending fiat withdrawal amount.
     */
    public function incrementPendingFiatWithdrawal(float $amount): bool
    {
        $this->pending_fiat_withdrawal = bcadd($this->pending_fiat_withdrawal, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Increment the pending crypto withdrawal amount.
     */
    public function incrementPendingCryptoWithdrawal(float $amount): bool
    {
        $this->pending_crypto_withdrawal = bcadd($this->pending_crypto_withdrawal, $amount, 8);
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the pending fiat withdrawal amount.
     */
    public function decrementPendingFiatWithdrawal(float $amount): bool
    {
        $this->pending_fiat_withdrawal = max(0, bcsub($this->pending_fiat_withdrawal, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the pending crypto withdrawal amount.
     */
    public function decrementPendingCryptoWithdrawal(float $amount): bool
    {
        $this->pending_crypto_withdrawal = max(0, bcsub($this->pending_crypto_withdrawal, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the total fiat withdrawn amount.
     */
    public function decrementTotalFiatWithdrawn(float $amount): bool
    {
        $this->total_fiat_withdrawn = max(0, bcsub($this->total_fiat_withdrawn, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Decrement the total crypto withdrawn amount.
     */
    public function decrementTotalCryptoWithdrawn(float $amount): bool
    {
        $this->total_crypto_withdrawn = max(0, bcsub($this->total_crypto_withdrawn, $amount, 8));
        $this->updated_at = now();

        return $this->save();
    }

    /**
     * Update referee counts for all levels.
     */
    public function updateRefereeCounts(): bool
    {
        $user = $this->user;
        if (! $user) {
            return false;
        }

        // Get direct referees (level 1)
        $this->referee_count_level1 = User::where('referrer_id', $user->id)->count();

        // Get level 2 referees (referrals of referrals)
        $this->referee_count_level2 = User::whereIn('referrer_id',
            User::where('referrer_id', $user->id)->pluck('id')
        )->count();

        // Get level 3 referees
        $this->referee_count_level3 = User::whereIn('referrer_id',
            User::whereIn('referrer_id',
                User::where('referrer_id', $user->id)->pluck('id')
            )->pluck('id')
        )->count();

        $this->updated_at = now();

        return $this->save();
    }
}
