import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Save, User, Mail, Phone, Camera } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card } from '../../components/ui/Card';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { useProfile, useProfileImageUpload } from '../../hooks/profile';
import { useAuth } from '../../contexts/AuthContext';
import { profileFormSchema, type ProfileFormData } from '../../schemas/profile';
import type { UpdateProfileRequest } from '../../types/profile';
import { getImageUrl } from '../../utils/images';
import toast from 'react-hot-toast';

export function EditProfilePage() {
  const navigate = useNavigate();
  const { user, isLoading } = useAuth();
  const { updateProfile } = useProfile();
  const { uploading, uploadImage } = useProfileImageUpload();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [saving, setSaving] = useState(false);

  // Split full_name into firstName and lastName for form display
  const getInitialFormData = (): ProfileFormData => {
    const fullName = user?.full_name || '';
    const nameParts = fullName.trim().split(/\s+/);
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    return {
      firstName,
      lastName,
      phone: user?.phone || '',
    };
  };
  // Set up form with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: getInitialFormData(),
  });

  // Update form when user data changes
  useEffect(() => {
    if (user) {
      reset(getInitialFormData());
    }
  }, [user, reset]);
  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    try {
      await uploadImage(file);
    } catch (error) {
      console.error('Image upload failed:', error);
      toast.error('Failed to upload image. Please try again.');
    }
  };

  const onSubmit = async (formData: ProfileFormData) => {
    setSaving(true);

    try {
      // Combine firstName and lastName into full_name for API
      const full_name = `${formData.firstName.trim()} ${formData.lastName.trim()}`;

      // Prepare update data for API
      const updateData: UpdateProfileRequest = {};

      if (full_name.trim()) {
        updateData.full_name = full_name;
      }

      if (formData.phone && formData.phone.trim()) {
        updateData.phone = formData.phone.trim();
      }

      const success = await updateProfile(updateData);
      if (success) {
        toast.success('Profile updated successfully!');
        navigate('/profile');
      }
    } catch (error) {
      console.error('Profile update failed:', error);
      toast.error('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center py-12'>
          <p className='text-foreground-muted'>No profile data available</p>
          <Link to='/profile'>
            <Button className='mt-4'>Go Back</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link to='/profile'>
          <Button
            variant='outline'
            size='sm'
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>Edit Profile</h1>
          <p className='text-foreground-secondary mt-2'>
            Update your personal information and profile photo
          </p>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        {/* Profile Picture Section */}
        <div className='lg:col-span-1'>
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-foreground mb-4'>
              Profile Photo
            </h3>{' '}
            <div className='text-center'>
              <div className='relative inline-block mb-4 mx-auto'>
                {getImageUrl(user.profile_image) ? (
                  <img
                    src={getImageUrl(user.profile_image)!}
                    alt={`${user.full_name || 'User'}'s avatar`}
                    className='w-32 h-32 rounded-full object-cover border-4 border-border block'
                    onError={e => {
                      // Fallback to default avatar if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove(
                        'hidden'
                      );
                    }}
                  />
                ) : null}
                <div
                  className={`w-32 h-32 rounded-full bg-background-secondary border-4 border-border flex items-center justify-center ${getImageUrl(user.profile_image) ? 'hidden' : 'block'}`}
                >
                  <User className='h-16 w-16 text-foreground-muted' />
                </div>
                <button
                  type='button'
                  onClick={() => fileInputRef.current?.click()}
                  disabled={uploading}
                  title='Change profile photo'
                  className='absolute -bottom-2 -right-2 bg-primary hover:bg-primary/90 text-primary-foreground p-2 rounded-full border-2 border-background shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 hover:scale-105'
                >
                  {uploading ? (
                    <LoadingSpinner size='sm' />
                  ) : (
                    <Camera className='h-4 w-4' />
                  )}
                </button>
              </div>
              <input
                ref={fileInputRef}
                type='file'
                accept='image/*'
                onChange={handleImageUpload}
                className='hidden'
              />
              <p className='text-xs text-foreground-muted mt-2'>
                JPG, PNG or GIF. Max 5MB.
              </p>
              <p className='text-xs text-foreground-muted'>
                Click the camera icon to change photo
              </p>
            </div>
          </Card>
        </div>

        {/* Profile Form Section */}
        <div className='lg:col-span-2'>
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-foreground mb-6'>
              Personal Information{' '}
            </h3>{' '}
            <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground mb-2'>
                    <User className='h-4 w-4' />
                    First Name
                  </label>
                  <Input
                    type='text'
                    {...register('firstName')}
                    placeholder='Enter your first name'
                    error={errors.firstName?.message}
                  />
                </div>
                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground mb-2'>
                    <User className='h-4 w-4' />
                    Last Name
                  </label>
                  <Input
                    type='text'
                    {...register('lastName')}
                    placeholder='Enter your last name'
                    error={errors.lastName?.message}
                  />
                </div>
              </div>
              <div>
                <label className='flex items-center gap-2 text-sm font-medium text-foreground mb-2'>
                  <Mail className='h-4 w-4' />
                  Email Address
                </label>
                <Input
                  type='email'
                  value={user.email}
                  disabled
                  className='bg-background-secondary opacity-60'
                />
                <p className='text-xs text-foreground-muted mt-1'>
                  Email cannot be changed. Contact support if needed.
                </p>
              </div>{' '}
              <div>
                <label className='flex items-center gap-2 text-sm font-medium text-foreground mb-2'>
                  <Phone className='h-4 w-4' />
                  Phone Number
                </label>
                <Input
                  type='tel'
                  {...register('phone')}
                  placeholder='Enter your phone number'
                  error={errors.phone?.message}
                />
              </div>
              <div className='flex gap-4 pt-4'>
                <Button
                  type='submit'
                  disabled={saving}
                  className='flex items-center gap-2'
                >
                  {saving ? (
                    <LoadingSpinner size='sm' />
                  ) : (
                    <Save className='h-4 w-4' />
                  )}
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
                <Link to='/profile'>
                  <Button type='button' variant='outline'>
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
}
