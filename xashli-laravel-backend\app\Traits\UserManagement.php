<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

trait UserManagement
{
    /**
     * Get validation rules for updating user profile.
     */
    protected function getUserValidationRules(?string $userId = null, bool $isAdmin = false): array
    {
        $rules = [
            'full_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255',
            'phone' => 'nullable|string|max:20',
            'password' => 'sometimes|string|min:8',
        ];

        if ($userId) {
            $rules['email'] .= '|unique:users,email,' . $userId;
        }

        if ($isAdmin) {
            $rules['is_active'] = 'sometimes|boolean';
            $rules['role'] = 'sometimes|in:user,admin';
        }

        return $rules;
    }

    /**
     * Update user fields from request.
     *
     * @param  \App\Models\User  $user
     */
    protected function updateUserFields($user, Request $request, bool $isAdmin = false): void
    {
        if ($request->has('full_name')) {
            $user->full_name = $request->full_name;
        }

        if ($request->has('email')) {
            $user->email = $request->email;
        }

        if ($request->has('password')) {
            $user->password = Hash::make($request->password);
        }

        if ($request->has('phone')) {
            $user->phone = $request->phone;
        }

        // Admin-only fields
        if ($isAdmin) {
            if ($request->has('is_active')) {
                $wasActive = $user->is_active;
                $newActiveStatus = $request->is_active;

                $user->is_active = $newActiveStatus;

                // Handle deactivation logic
                if ($wasActive && ! $newActiveStatus) {
                    // User is being deactivated
                    $user->deactivated_at = now();
                    $user->deactivation_duration = 2 * 24 * 60;
                } elseif (! $wasActive && $newActiveStatus) {
                    // User is being reactivated
                    $user->deactivated_at = null;
                    $user->deactivation_duration = null;
                }
            }

            if ($request->has('role')) {
                $user->role = $request->role;
            }
        }
    }
}
