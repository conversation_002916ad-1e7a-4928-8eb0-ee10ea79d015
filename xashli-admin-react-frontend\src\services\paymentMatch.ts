import { apiClient } from './api';
import type {
  PaymentMatchDetails,
  PaymentMatchFilters,
  PaymentMatchResponse,
  PaymentMatchStatistics,
  PaymentMatchStatisticsFilters,
  ManualMatchRequest,
  ApiResponse,
} from '../types';

export const paymentMatchService = {
  // Get all payment matches with filtering and pagination
  getPaymentMatches: async (
    filters: PaymentMatchFilters = {}
  ): Promise<ApiResponse<PaymentMatchResponse>> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const url = queryString
      ? `/payment-matches?${queryString}`
      : '/payment-matches';

    return apiClient.get<PaymentMatchResponse>(url);
  },

  // Get a specific payment match by ID
  getPaymentMatch: async (
    id: string
  ): Promise<ApiResponse<PaymentMatchDetails>> => {
    return apiClient.get<PaymentMatchDetails>(`/payment-matches/${id}`);
  },

  // Get payment match statistics
  getStatistics: async (
    filters: PaymentMatchStatisticsFilters = {}
  ): Promise<ApiResponse<PaymentMatchStatistics>> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const queryString = params.toString();
    const url = queryString
      ? `/payment-matches/statistics?${queryString}`
      : '/payment-matches/statistics';

    return apiClient.get<PaymentMatchStatistics>(url);
  },

  // Create a manual payment match (admin only)
  createManualMatch: async (
    data: ManualMatchRequest
  ): Promise<ApiResponse<PaymentMatchDetails>> => {
    return apiClient.post<PaymentMatchDetails>(
      '/payment-matches/manual-match',
      data
    );
  },

  // Trigger automatic payment matching (admin only)
  triggerAutoMatch: async (): Promise<
    ApiResponse<{ matches_created: number }>
  > => {
    return apiClient.post<{ matches_created: number }>(
      '/payment-matches/auto-match'
    );
  },

  // Confirm payment sent by funder
  confirmPaymentSent: async (
    matchId: string,
    data: { transaction_hash: string }
  ): Promise<ApiResponse<PaymentMatchDetails>> => {
    return apiClient.post<PaymentMatchDetails>(
      `/payment-matches/${matchId}/confirm-payment-sent`,
      data
    );
  },

  // Confirm payment received by withdrawer
  confirmPaymentReceived: async (
    matchId: string
  ): Promise<ApiResponse<PaymentMatchDetails>> => {
    return apiClient.post<PaymentMatchDetails>(
      `/payment-matches/${matchId}/confirm-payment-received`
    );
  },

  // Upload payment proof
  uploadPaymentProof: async (
    matchId: string,
    file: File
  ): Promise<ApiResponse<PaymentMatchDetails>> => {
    const formData = new FormData();
    formData.append('payment_proof', file);

    return apiClient.post<PaymentMatchDetails>(
      `/payment-matches/${matchId}/upload-payment-proof`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },

  // Get next auto matching time information
  getNextMatchingTime: async (): Promise<
    ApiResponse<{
      is_auto_matching_enabled: boolean;
      mode?: string;
      frequency_hours?: number;
      next_run?: string;
      next_run_formatted?: string;
      is_due?: boolean;
      message?: string;
    }>
  > => {
    return apiClient.get('/payment-matches/next-matching-time');
  },
};

export default paymentMatchService;
