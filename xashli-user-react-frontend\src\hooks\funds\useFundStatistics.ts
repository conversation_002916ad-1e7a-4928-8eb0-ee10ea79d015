import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { fundService } from '../../services';
import type { FundStatistics } from '../../types';

export function useFundStatistics() {
  const [statistics, setStatistics] = useState<FundStatistics | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fundService.getFundStatistics();

      if (response.status === 'success' && response.data) {
        setStatistics(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch statistics');
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      toast.error('Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshStatistics = useCallback(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    fetchStatistics,
    refreshStatistics,
  };
}
