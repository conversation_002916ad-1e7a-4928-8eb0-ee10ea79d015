import { apiClient } from './api';
import type {
  User,
  UpdateUserRequest,
  UserFilters,
  PaginatedUsers,
  UserStatistics,
  ReferralBonus,
  ApiResponse,
} from '../types';

export const userService = {
  /**
   * Get all users with filtering and pagination
   */
  getUsers: async (
    filters?: UserFilters
  ): Promise<ApiResponse<PaginatedUsers>> => {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const queryString = params.toString();
    const url = queryString ? `/users?${queryString}` : '/users';

    return apiClient.get<PaginatedUsers>(url);
  },

  /**
   * Get a specific user by ID
   */
  getUserById: async (id: string): Promise<ApiResponse<User>> => {
    return apiClient.get<User>(`/users/${id}`);
  },

  /**
   * Update an existing user
   */
  updateUser: async (
    id: string,
    data: UpdateUserRequest
  ): Promise<ApiResponse<User>> => {
    return apiClient.put<User>(`/users/${id}`, data);
  },

  /**
   * Delete a user
   */
  deleteUser: async (id: string): Promise<ApiResponse<null>> => {
    return apiClient.delete<null>(`/users/${id}`);
  },

  /**
   * Get user statistics (independent of pagination and filters)
   */
  getUserStatistics: async (): Promise<ApiResponse<UserStatistics>> => {
    return apiClient.get<UserStatistics>('/users/statistics');
  },

  /**
   * Get referees (users referred by a specific user)
   * For admins: Can specify userId or referralCode to get any user's referees
   */
  getReferees: async (params?: {
    userId?: string;
    referralCode?: string;
  }): Promise<ApiResponse<any>> => {
    const queryParams = new URLSearchParams();
    if (params?.userId) queryParams.append('user_id', params.userId);
    if (params?.referralCode)
      queryParams.append('referral_code', params.referralCode);

    const url = `/referrals/referees${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<any>(url);
  },

  /**
   * Get referral bonuses
   * For admins: Can specify userId or referralCode to get any user's bonuses
   */
  getReferralBonuses: async (params?: {
    userId?: string;
    referralCode?: string;
    withdrawable?: boolean;
    consumed?: boolean;
    level?: 1 | 2 | 3;
  }): Promise<ApiResponse<ReferralBonus[]>> => {
    const queryParams = new URLSearchParams();
    if (params?.userId) queryParams.append('user_id', params.userId);
    if (params?.referralCode)
      queryParams.append('referral_code', params.referralCode);
    if (params?.withdrawable !== undefined)
      queryParams.append('withdrawable', params.withdrawable.toString());
    if (params?.consumed !== undefined)
      queryParams.append('consumed', params.consumed.toString());
    if (params?.level) queryParams.append('level', params.level.toString());

    const url = `/referrals/bonuses${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<ReferralBonus[]>(url);
  },

  /**
   * Get referral info/stats
   * For admins: Can specify userId or referralCode to get any user's referral info
   */
  getReferralInfo: async (params?: {
    userId?: string;
    referralCode?: string;
  }): Promise<ApiResponse<any>> => {
    const queryParams = new URLSearchParams();
    if (params?.userId) queryParams.append('user_id', params.userId);
    if (params?.referralCode)
      queryParams.append('referral_code', params.referralCode);

    const url = `/referrals/info${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiClient.get<any>(url);
  },
};
