{"version": "2.0.0", "tasks": [{"label": "Start Admin Dev Server", "type": "shell", "command": "npm run dev", "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Build Admin Frontend", "type": "shell", "command": "npm run build", "group": "build", "problemMatcher": []}, {"label": "Preview Admin Build", "type": "shell", "command": "npm run preview", "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Format Admin Code", "type": "shell", "command": "npm run format", "group": "build", "problemMatcher": []}, {"label": "Lint Admin Code", "type": "shell", "command": "npm run lint", "group": "build", "problemMatcher": []}]}