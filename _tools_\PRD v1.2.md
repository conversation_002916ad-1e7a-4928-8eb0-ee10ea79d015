**Product Requirements Document (PRD)**

**Product Title:**

**Peer-to-Peer Financial Assistance Platform**

**Version:**

v1.2

**Author:**

\[Your Name / Product Owner\]

**Last Updated:**

May 2025

**1. 📌 Introduction**

**1.1 Purpose**

This PRD defines the functional and technical requirements for a web-based financial assistance system modelled on the peer-to-peer "Fund" and "Withdraw" mechanism. The platform facilitates user-to-user financial exchanges using both **fiat (via payment processor)** and **crypto (via Solana smart contracts)**.

**1.2 Goals**

- Enable **conditional maturity periods** (7 or 10 days) for Fund → Withdraw
- Support **multilevel referral rewards**
- Enforce strict **crypto-fiat segregation**
- Allow for **automated or manual matching**
- Ensure **fee deduction logic** on both fiat and crypto contributions
- Maintain transparency, trust, and administrative oversight

**1.3 Scope**

- Core Fund and Withdraw system
- Admin management tools
- Fiat/crypto infrastructure
- Referral + bonus + trust engine
- Matching system with variable timers and manual override

**2. 👤 User Roles**

| **Role**      | **Description**                                                             |
|---------------|-----------------------------------------------------------------------------|
| **Participant** | Can Fund/Withdraw, refer others, earn bonuses                              |
| **Admin**     | Controls system configs, fee settings, user bans, matching, dispute resolution |

**3. 🔧 Functional Requirements**

**3.1 Fund**

**Description:** User commits to assist others financially.

**Requirements:**

- User selects **amount, currency**, and **payment method** (Fiat Bank or Solana Wallet)
  - **Fiat limits:** NGN 10,000 (min) to NGN 500,000 (max)
  - **Crypto limits:** 0.1 SOL (min) to 100 SOL (max)
- User can only Fund using **one method** per transaction
- User must complete KYC or add payment details after registration
- Upon Fund transaction:
  - User completes payment on the frontend
  - **Transaction hash/reference** is saved alongside Funding data
  - **5% platform fee** is deducted from the Funder's amount
- The Fund matures based on referral status:
  - **7 days** for users with **5+ referees**
  - **10 days** for users with **<5 referees**
- Fund accrues a growth rate based on currency type:
  - **Fiat: fixed 30% return**
  - **Crypto: fixed 35% return**
- Users can cancel Fund before matching begins

**3.2 Withdraw**

**Description:** User requests payout after Fund maturity.

**Requirements:**

- Withdraw is available after maturity period (7 or 10 days) based on referral count
- Withdraw is paid in **same method as Fund** (Fiat or Crypto)
- Withdraw amount depends on Fund method:
  - **Fiat:** Fund amount + 30% growth + referral bonuses
  - **Crypto:** Fund amount + 35% growth + referral bonuses
- **Prerequisite for Withdrawal:**
  - User must create a new Fund that is equal to or greater than previous Fund amount
  - New Fund must be created before withdrawing matured funds
- Withdraw can be matched by multiple Funders
- Confirmation required upon receipt
- Timeout/dispute protocol (auto flag if no confirmation in 48h)

**🔒 Bonus Withdrawal Limitation**

**Purpose:** Prevent abuse by capping total bonus withdrawals to 100% of original Fund amount.

**Logic:**

- When Withdrawing, user is entitled to:
  - Original Fund amount
  - Growth (30% for fiat, 35% for crypto)
  - Referral bonus
- However, bonus withdrawals (growth + referral combined) **must not exceed 100% of Fund**

**3.3 Referral System**

**Description:** Multilevel reward structure for referrals.

**Requirements:**

- 3-level structure:
  - Level 1: 5% of Fund amount
  - Level 2: 2%
  - Level 3: 1%
- Referral bonuses are **automatically added to Withdraw value** at maturity
- Bonuses are only credited after referred Fund is completed and matched
- Admin can view and manage full referral trees
- Users with sufficient total Level 1 referee funding enjoy faster maturity periods (7 days vs 10 days):
  - For fiat: when total L1 referee funding reaches 1M
  - For crypto: when total L1 referee funding reaches 2 SOL

**3.4 Matching Engine**

**Description:** Pairs Funders with Withdrawers.

**Requirements:**

- **Automatic matching** every X hours (configurable variable, e.g., 4h, 1 day)
- Admin can **toggle auto-matching ON/OFF**
- **Manual matching** always available to admin
- Matching is **FIFO-based**, allows **split matching**
- Matching respects **payment method type (Fiat ↔ Fiat, Crypto ↔ Crypto)**
- **Important matching compensation:**
  - Withdrawers receive full 130% (fiat) or 135% (crypto) of their Fund amount
  - System matches Withdrawers with Funders whose combined contribution (after 5% fee deduction) equals the Withdrawer's full amount
  - The 5% platform fee is borne entirely by Funders, ensuring Withdrawers get their complete earnings
- **Fallback for Incomplete Matches:**
  - If there are no complete matches available, the system will use default admin accounts
  - Remaining Funders or Withdrawers are matched with appropriate admin accounts
  - Each admin account must have both bank and Solana wallet details configured
  - Admin can manage (add/modify/deactivate) these default accounts

**3.5 User Management**

**Description:** Registration, authentication, and profile control.

**Requirements:**

- Secure registration with phone/email
- 2FA and KYC capability
- Profile page includes:
  - Bank account info (for Fiat Withdraw)
  - Solana wallet address (for Crypto Withdraw)
- Users can't switch method mid-cycle
- Admin can **deactivate/suspend** users violating rules
- Deactivated users cannot Withdraw, refer, or Fund

**3.6 Dashboard**

**Description:** Provides real-time insight into user activity.

**Requirements:**

- Fund/Withdraw status timeline
- Referral tree and bonuses earned
- Maturity dates based on referral count
- Notifications (match, bonus, Fund matured, Withdraw eligible)
- Admin view shows platform-wide metrics and flags

**3.7 Trust & Confirmation**

**Description:** Confirmation protocol for fund receipt.

**Requirements:**

- Withdrawer confirms once payment is received
- If unconfirmed after 48h, Funder can upload proof
- Admin is notified for unresolved transactions
- Confirmation locks transaction in system

**3.8 Fees & Transaction Processing**

**Description:** Fee processing and transaction management.

**Requirements:**

- **5% platform fee** applied on all Fund transactions
- Admin can adjust fee dynamically via config panel
- **Transaction tracking:**
  - Frontend handles payment completion
  - Backend stores transaction hash/reference with Fund details
  - System tracks transaction status

**3.9 Admin Panel**

**Requirements:**

- Toggle auto-matching ON/OFF
- Set matching frequency (e.g., every 6h)
- Update platform fee (0--10%)
- View system-wide transaction queue
- Ban/deactivate users
- Handle disputes (view payment proofs)
- Match Fund ↔ Withdraw manually
- See referral performance per user
- **Manage Default Matching Accounts:**
  - Create/edit/deactivate default accounts used for incomplete matches
  - Configure bank details and Solana wallet addresses for these accounts
  - Monitor activity and balances of default accounts

**4. 🧱 Non-Functional Requirements**

| **Category**      | **Requirement**                                     |
|-------------------|-----------------------------------------------------|
| **Security**      | AES-256 encryption, smart contract audits, rate limiting |
| **Scalability**   | Horizontal scaling for transaction volumes          |
| **Performance**   | < 2s average API response under 5k concurrent users |
| **Resilience**    | Auto-recovery jobs for interrupted matches          |
| **Integration**   | Solana RPC API, failover nodes, payment gateways    |
| **UX**            | Clear separation of crypto/fiat actions and progress timelines |

**5. 🧱 Technical Architecture**

**5.1 Backend**

- **Language:** Node.js or Python (Django)
- **Database:** PostgreSQL (relational mapping), Redis (queue engine)
- **Payment Tracking:**
  - Store transaction hashes/references after frontend payment completion
- **Job Queue:** BullMQ (for matching scheduler)

**5.2 Frontend**

- **Framework:** React
- **Design:** Tailwind CSS
- **Routing:** React Router
- **Wallet Connect:** Solana Wallet Adapter
- **Payment Processing:** Handles payments and sends transaction data to backend

**6. 🧮 Key User Flow Examples**

**A. Fund via Bank → Withdraw in Bank**

1. User Funds $100 (frontend handles payment)
2. System registers $95 ($100 - 5% fee) for matching
3. After maturity period (7 or 10 days based on referral count), Withdraw eligible
4. User creates new Fund ≥ $100 to enable withdrawal
5. Withdraw includes:
   - $100 Fund amount
   - $30 growth (30%)
   - $5 referral bonus
   - → Total: $135
6. Matched with one or more Funders totaling $135
7. If insufficient Funders are available, the system uses default admin accounts to complete the match
8. Receives full amount via bank

**B. Fund via Solana → Withdraw in Solana**

1. User Funds 2 SOL (frontend handles payment)
2. System registers 1.9 SOL (2 SOL - 5% fee) for matching
3. After maturity, Withdraw includes:
   - 2 SOL Fund amount
   - 0.7 SOL growth (35%)
   - → Total: 2.7 SOL plus any referral bonuses
4. User creates new Fund ≥ 2 SOL to enable withdrawal
5. Matched with Funders and receives full amount in SOL
6. If no complete matches available, default admin accounts are used

**C. Default Account Matching Flow**

1. System runs matching cycle but finds incomplete matches
2. System selects appropriate default accounts based on:
   - Transaction type (Fund or Withdraw)
   - Payment method (Fiat or Crypto)
   - Account availability and rotation rules
3. Match is created using default account
4. Admin is notified of default account usage
5. Transaction proceeds normally from user perspective

**Entity Relationship Diagram (ERD)**

**⚙️ 1. User**

```ts
User {
  id: UUID (PK)
  full_name: String
  email: String (unique)
  phone: String
  password_hash: String
  referral_code: String (unique)
  referred_by_code: String (nullable) // connects to another User.referral_code
  parent_id: UUID (FK to User.id) (nullable)
  referee_count: Int (default: 0) // Used to determine Fund maturity period
  is_active: Boolean (default: true)
  user_type: Enum('user', 'admin') // Removed default_account type
  created_at: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 2. PaymentMethod**

```ts
PaymentMethod {
  id: UUID (PK)
  user_id: UUID (FK to User.id)
  type: Enum('bank', 'crypto')
  bank_name: String (nullable)
  account_number: String (nullable)
  account_name: String (nullable)
  crypto_address: String (nullable)
  crypto_network: String (nullable, e.g., 'Solana')
  label: String // e.g. "Binance Wallet", "My Bank"
  status: Enum('active', 'inactive')
  created_at: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 3. Fund**

```ts
Fund {
  id: UUID (PK)
  user_id: UUID (FK to User.id)
  amount: Decimal
  currency: Enum('fiat', 'crypto')
  payment_method_id: UUID (FK to PaymentMethod.id)
  transaction_hash: String // Payment reference/hash from frontend
  platform_fee_percentage: Decimal
  start_date: DateTime
  maturity_date: DateTime // 7 or 10 days from start_date based on referee_count
  growth_percentage: Decimal // 30% for fiat, 35% for crypto
  growth_amount: Decimal // calculated from growth_percentage
  referral_bonus_limit: Decimal // Max referral bonus allowed in Withdraw
  is_eligible_for_withdrawal: Boolean (computed based on maturity_date)
  status: Enum('pending', 'matched', 'completed', 'cancelled')
  created_at: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 4. Withdraw**

```ts
Withdraw {
  id: UUID (PK)
  user_id: UUID (FK to User.id)
  fund_id: UUID (FK to Fund.id)
  new_fund_id: UUID (FK to Fund.id) // Reference to the new Fund created as prerequisite
  base_withdrawable_amount: Decimal // 130% (fiat) or 135% (crypto) of Fund.amount
  available_referral_bonus: Decimal // available referral bonus that can be used
  withdrawable_referral_bonus: Decimal // portion of referral bonus actually used
  amount_matched: Decimal // total amount matched with funds
  status: Enum('pending', 'matched', 'completed')
  payment_method_id: UUID (FK to PaymentMethod.id)
  created_at: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 5. Match**

```ts
Match {
  id: UUID (PK)
  fund_id: UUID (FK to Fund.id)
  withdraw_id: UUID (FK to Withdraw.id)
  amount: Decimal // partial or full
  payment_method_id: UUID (FK to PaymentMethod.id)
  initiator: Enum('auto', 'manual')
  status: Enum('pending', 'paid', 'confirmed', 'disputed')
  created_at: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 6. ReferralBonus**

```ts
ReferralBonus {
  id: UUID (PK)
  referred_user_id: UUID (FK to User.id) // the Funder
  referrer_user_id: UUID (FK to User.id)
  fund_id: UUID (FK to Fund.id)
  level: Int // 1, 2, or 3
  percentage: Decimal (e.g., 0.05, 0.02, 0.01)
  amount: Decimal
  created_at: Timestamp
}
```

**⚙️ 7. PlatformSetting**

```ts
PlatformSetting {
  id: UUID (PK)
  key: String (unique) // e.g., "platform_fee", "fiat_growth_rate", "crypto_growth_rate"
  value: String
  type: Enum('string', 'int', 'decimal', 'boolean')
  description: String (optional)
  updated_at: Timestamp
}
```

Example rows:

```ts
{ key: "platform_fee", value: "0.05", type: "decimal" }
{ key: "fiat_growth_rate", value: "0.3", type: "decimal" }
{ key: "crypto_growth_rate", value: "0.35", type: "decimal" }
{ key: "auto_match_frequency", value: "4", type: "int" }
{ key: "auto_matching_enabled", value: "true", type: "boolean" }
{ key: "min_referees_for_fast_maturity", value: "5", type: "int" }
{ key: "fast_maturity_days", value: "7", type: "int" }
{ key: "standard_maturity_days", value: "10", type: "int" }
```

**⚙️ 8. AdminActivityLog**

```ts
AdminActivityLog {
  id: UUID (PK)
  admin_id: UUID (FK to User.id)
  action_type: String // e.g., "deactivate_account", "manual_match"
  target_user_id: UUID (nullable)
  metadata: JSON
  created_at: Timestamp
}
```

**⚙️ 9. MatchingCycle (Optional for scheduled matching control)**

```ts
MatchingCycle {
  id: UUID (PK)
  mode: Enum('auto', 'manual')
  frequency_hours: Int
  next_run: Timestamp
  is_active: Boolean
  updated_at: Timestamp
}
```

**⚙️ 10. UserStats (Optional but great for performance)**

```ts
UserStats {
  user_id: UUID (PK, FK to User.id)
  total_funded: Decimal
  total_withdrawn: Decimal
  total_bonus_earned: Decimal
  referral_count_level1: Int
  referral_count_level2: Int
  referral_count_level3: Int
  pending_withdrawal_amount: Decimal
  next_withdraw_eligibility: Timestamp
  updated_at: Timestamp
}
```

**⚙️ 11. DefaultAccount**

```ts
DefaultAccount {
  id: UUID (PK)
  user_id: UUID (FK to User.id where user_type = 'default_account')
  account_name: String
  description: String
  is_active: Boolean (default: true)
  bank_payment_method_id: UUID (FK to PaymentMethod.id) // For fiat transactions
  crypto_payment_method_id: UUID (FK to PaymentMethod.id) // For crypto transactions
  purpose: Enum('fund_default', 'withdraw_default', 'both')
  last_used: Timestamp
  total_funds_matched: Decimal
  total_withdraws_matched: Decimal
  created_at: Timestamp
  updated_at: Timestamp
}
```
