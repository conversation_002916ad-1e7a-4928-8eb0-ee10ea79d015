import { useQuery } from '@tanstack/react-query';
import { adminActivityLogService } from '../services';
import type { AdminActivityLogFilters } from '../types';

// Hook for fetching admin activity logs with filters
export const useAdminActivityLogs = (filters?: AdminActivityLogFilters) => {
  return useQuery({
    queryKey: ['admin-activity-logs', filters],
    queryFn: () => adminActivityLogService.getActivityLogs(filters),
    staleTime: 30000, // 30 seconds
  });
};

// Hook for fetching a single admin activity log
export const useAdminActivityLog = (id: string) => {
  return useQuery({
    queryKey: ['admin-activity-log', id],
    queryFn: () => adminActivityLogService.getActivityLog(id),
    enabled: !!id,
  });
};

// Hook for fetching action types
export const useActionTypes = () => {
  return useQuery({
    queryKey: ['admin-activity-action-types'],
    queryFn: () => adminActivityLogService.getActionTypes(),
    staleTime: 300000, // 5 minutes - action types don't change often
  });
};
