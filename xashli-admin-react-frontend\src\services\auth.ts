import { apiClient } from './api';
import type {
  LoginCredentials,
  AuthResponse,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  User,
  ApiResponse,
} from '../types';

export const authService = {
  /**
   * Login admin user
   */
  login: async (
    credentials: LoginCredentials
  ): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post<AuthResponse>('/auth/login', credentials);
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/auth/logout');
  },

  /**
   * Get current user profile
   */
  me: async (): Promise<ApiResponse<User>> => {
    return apiClient.get<User>('/auth/me');
  },

  /**
   * Refresh access token
   */
  refreshToken: async (
    refreshToken: string
  ): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post<AuthResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });
  },

  /**
   * Request password reset
   */
  forgotPassword: async (
    data: ForgotPasswordRequest
  ): Promise<ApiResponse<{ token?: string }>> => {
    return apiClient.post<{ token?: string }>('/auth/forgot-password', data);
  },

  /**
   * Reset password with token
   */
  resetPassword: async (
    data: ResetPasswordRequest
  ): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/auth/reset-password', data);
  },
};
