import toast from 'react-hot-toast';

// Toast configuration
const toastConfig = {
  duration: 4000,
  position: 'top-right' as const,
  style: {
    borderRadius: '8px',
    background: '#fff',
    color: '#333',
    fontSize: '14px',
    fontWeight: '500',
    padding: '12px 16px',
    boxShadow:
      '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    border: '1px solid #e5e7eb',
  },
};

// Custom toast utilities
export const showToast = {
  success: (message: string) => {
    toast.success(message, {
      ...toastConfig,
      style: {
        ...toastConfig.style,
        borderLeft: '4px solid #10b981',
      },
      iconTheme: {
        primary: '#10b981',
        secondary: '#fff',
      },
    });
  },

  error: (message: string) => {
    toast.error(message, {
      ...toastConfig,
      duration: 5000, // Show errors a bit longer
      style: {
        ...toastConfig.style,
        borderLeft: '4px solid #ef4444',
      },
      iconTheme: {
        primary: '#ef4444',
        secondary: '#fff',
      },
    });
  },

  warning: (message: string) => {
    toast(message, {
      ...toastConfig,
      icon: '⚠️',
      style: {
        ...toastConfig.style,
        borderLeft: '4px solid #f59e0b',
      },
    });
  },

  info: (message: string) => {
    toast(message, {
      ...toastConfig,
      icon: 'ℹ️',
      style: {
        ...toastConfig.style,
        borderLeft: '4px solid #3b82f6',
      },
    });
  },

  loading: (message: string) => {
    return toast.loading(message, {
      ...toastConfig,
      style: {
        ...toastConfig.style,
        borderLeft: '4px solid #6b7280',
      },
    });
  },

  dismiss: (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  },

  // Custom toast for authentication
  auth: {
    loginSuccess: (userName: string) => {
      showToast.success(`Welcome back, ${userName}! 🎉`);
    },
    loginError: (error: string) => {
      showToast.error(`Login failed: ${error}`);
    },
    logoutSuccess: () => {
      showToast.info('You have been logged out successfully');
    },
    sessionExpired: () => {
      showToast.warning('Your session has expired. Please log in again.');
    },
    verificationSuccess: () => {
      showToast.success('Verification successful! Welcome to Xashli! 🎉');
    },
    verificationError: (error: string) => {
      showToast.error(`Verification failed: ${error}`);
    },
    passwordResetSent: () => {
      showToast.info('Password reset link sent to your email');
    },
    passwordResetSuccess: () => {
      showToast.success('Password reset successfully! Please log in.');
    },
    registrationSuccess: () => {
      showToast.success(
        'Account created successfully! Please verify your email address.'
      );
    },
  },

  // Custom toast for API operations
  api: {
    requestSuccess: (action: string) => {
      showToast.success(`${action} completed successfully`);
    },
    requestError: (action: string, error: string) => {
      showToast.error(`${action} failed: ${error}`);
    },
    networkError: () => {
      showToast.error(
        'Network error. Please check your connection and try again.'
      );
    },
  },

  // Custom toast for financial operations
  finance: {
    requestSent: (amount: string, recipient: string) => {
      showToast.success(`Request for ${amount} sent to ${recipient}! 💰`);
    },
    requestReceived: (amount: string, sender: string) => {
      showToast.info(`New request for ${amount} from ${sender}`);
    },
    paymentSuccess: (amount: string, recipient: string) => {
      showToast.success(`Payment of ${amount} sent to ${recipient}! ✅`);
    },
    paymentReceived: (amount: string, sender: string) => {
      showToast.success(`Received ${amount} from ${sender}! 🎉`);
    },
    insufficientFunds: () => {
      showToast.warning('Insufficient funds for this transaction');
    },
    transactionPending: (transactionId: string) => {
      showToast.info(`Transaction ${transactionId} is being processed...`);
    },
  },

  // Custom toast for profile operations
  profile: {
    updateSuccess: () => {
      showToast.success('Profile updated successfully!');
    },
    updateError: (error: string) => {
      showToast.error(`Profile update failed: ${error}`);
    },
    avatarUploadSuccess: () => {
      showToast.success('Profile picture updated successfully!');
    },
    avatarUploadError: () => {
      showToast.error('Failed to upload profile picture. Please try again.');
    },
  },
};

export default showToast;
