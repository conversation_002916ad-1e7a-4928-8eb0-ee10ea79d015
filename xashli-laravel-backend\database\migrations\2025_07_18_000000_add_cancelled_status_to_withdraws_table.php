<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE withdraws MODIFY COLUMN status ENUM('pending', 'matched', 'completed', 'cancelled') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First update any cancelled status to completed before reverting
        DB::table('withdraws')->where('status', 'cancelled')->update(['status' => 'completed']);

        DB::statement("ALTER TABLE withdraws MODIFY COLUMN status ENUM('pending', 'matched', 'completed') DEFAULT 'pending'");
    }
};
