import { useState, useEffect, useCallback } from 'react';
import { platformFeeService } from '../services/platformFeeService';
import type {
  PlatformFee,
  PlatformFeeStatistics,
  PlatformFeeFilters,
} from '../types/platformFee';

interface UsePlatformFeesState {
  fees: PlatformFee[];
  loading: boolean;
  error: string | null;
  pagination: {
    currentPage: number;
    lastPage: number;
    perPage: number;
    total: number;
  };
}

interface UsePlatformFeesReturn extends UsePlatformFeesState {
  fetchFees: (filters?: PlatformFeeFilters) => Promise<void>;
  refetch: () => Promise<void>;
  setFilters: (filters: PlatformFeeFilters) => void;
}

export function usePlatformFees(
  initialFilters?: PlatformFeeFilters
): UsePlatformFeesReturn {
  const [state, setState] = useState<UsePlatformFeesState>({
    fees: [],
    loading: false,
    error: null,
    pagination: {
      currentPage: 1,
      lastPage: 1,
      perPage: 20,
      total: 0,
    },
  });

  const [filters, setFilters] = useState<PlatformFeeFilters>(
    initialFilters || {}
  );

  const fetchFees = useCallback(
    async (newFilters?: PlatformFeeFilters) => {
      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        const filtersToUse = newFilters || filters;
        const response = await platformFeeService.getFees(filtersToUse);

        if (response.data) {
          setState(prev => ({
            ...prev,
            fees: response.data!.platformFees,
            pagination: {
              currentPage: response.data!.pagination.current_page,
              lastPage: response.data!.pagination.last_page,
              perPage: response.data!.pagination.per_page,
              total: response.data!.pagination.total,
            },
            loading: false,
          }));
        }
      } catch (error) {
        setState(prev => ({
          ...prev,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to fetch platform fees',
          loading: false,
        }));
      }
    },
    [filters]
  );
  const refetch = useCallback(() => {
    return fetchFees(filters);
  }, [fetchFees, filters]);

  const updateFilters = useCallback(
    (newFilters: PlatformFeeFilters) => {
      setFilters(newFilters);
      fetchFees(newFilters);
    },
    [fetchFees]
  );

  useEffect(() => {
    fetchFees();
  }, []);

  return {
    ...state,
    fetchFees,
    refetch,
    setFilters: updateFilters,
  };
}

interface UsePlatformFeeStatisticsReturn {
  statistics: PlatformFeeStatistics | null;
  loading: boolean;
  error: string | null;
  fetchStatistics: (startDate?: string, endDate?: string) => Promise<void>;
}

export function usePlatformFeeStatistics(): UsePlatformFeeStatisticsReturn {
  const [statistics, setStatistics] = useState<PlatformFeeStatistics | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fetchStatistics = useCallback(
    async (startDate?: string, endDate?: string) => {
      setLoading(true);
      setError(null);

      try {
        const response = await platformFeeService.getStatistics(
          startDate,
          endDate
        );
        // The response contains the simplified statistics data
        setStatistics(response.data);
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to fetch statistics'
        );
      } finally {
        setLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    error,
    fetchStatistics,
  };
}

interface UsePlatformFeeReturn {
  fee: PlatformFee | null;
  loading: boolean;
  error: string | null;
  fetchFee: (id: string) => Promise<void>;
}

export function usePlatformFee(): UsePlatformFeeReturn {
  const [fee, setFee] = useState<PlatformFee | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFee = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await platformFeeService.getFee(id);
      setFee(response.data);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'Failed to fetch platform fee'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    fee,
    loading,
    error,
    fetchFee,
  };
}
