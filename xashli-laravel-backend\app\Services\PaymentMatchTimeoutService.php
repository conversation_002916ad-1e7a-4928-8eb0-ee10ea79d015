<?php

namespace App\Services;

use App\Models\Fund;
use App\Models\PaymentMatch;
use App\Models\PlatformSetting;
use App\Models\User;
use App\Models\Withdraw;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentMatchTimeoutService
{
    /**
     * Process all payment matches that have exceeded the fund confirmation timeout.
     * Deactivates funders who failed to fund and cancels their payment matches.
     *
     * @return array Statistics about the processing
     */
    public function processTimeouts(): array
    {
        $stats = [
            'processed_matches' => 0,
            'deactivated_users' => 0,
            'cancelled_matches' => 0,
            'updated_funds' => 0,
            'updated_withdraws' => 0,
        ];

        try {
            DB::beginTransaction();

            // Get the fund confirmation timeout from platform settings (in hours)
            $timeoutHours = PlatformSetting::getSetting('fund_confirmation_timeout_hours') ?? 48;
            $timeoutDateTime = now()->subHours($timeoutHours);

            Log::info("Processing payment match timeouts. Timeout threshold: {$timeoutHours} hours ago ({$timeoutDateTime})");

            // Get all pending payment matches that have exceeded the timeout
            $timedOutMatches = PaymentMatch::where('status', 'pending')
                ->where('created_at', '<=', $timeoutDateTime)
                ->whereHas('fund', function ($query) {
                    // Only process matches where the fund user is not an admin
                    $query->whereHas('user', function ($userQuery) {
                        $userQuery->where('role', '!=', 'admin');
                    });
                })
                ->with(['fund.user', 'withdraw'])
                ->get();

            Log::info("Found {$timedOutMatches->count()} timed out payment matches to process");

            $processedUserIds = [];

            foreach ($timedOutMatches as $match) {
                $stats['processed_matches']++;

                // Deactivate the funder (only once per user)
                if (! in_array($match->fund_user_id, $processedUserIds)) {
                    $this->deactivateUser($match->fund->user);
                    $processedUserIds[] = $match->fund_user_id;
                    $stats['deactivated_users']++;

                    Log::info("Deactivated user {$match->fund_user_id} for failing to fund payment match {$match->id}");
                }

                // Cancel the payment match
                $match->status = 'cancelled';
                $match->save();
                $stats['cancelled_matches']++;

                // Update the fund's matched amount and status
                $this->updateFundAfterCancellation($match->fund, $match->amount);
                $stats['updated_funds']++;

                // Update the withdraw's matched amount and status
                $this->updateWithdrawAfterCancellation($match->withdraw, $match->amount);
                $stats['updated_withdraws']++;

                Log::info("Cancelled payment match {$match->id} and updated related fund and withdraw");
            }

            DB::commit();

            Log::info('Payment match timeout processing completed', $stats);

            return $stats;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error processing payment match timeouts: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Deactivate a user for 2 days (2880 minutes).
     */
    protected function deactivateUser(User $user): void
    {
        $deactivationDurationMinutes = 2 * 24 * 60; // 2 days in minutes
        $user->deactivateFor($deactivationDurationMinutes);

        Log::info("User {$user->id} deactivated for {$deactivationDurationMinutes} minutes");
    }

    /**
     * Update fund after a payment match cancellation.
     */
    protected function updateFundAfterCancellation(Fund $fund, float $cancelledAmount): void
    {
        // Reduce the matched amount
        $fund->amount_matched = max(0, $fund->amount_matched - $cancelledAmount);

        // Update status based on remaining matched amount
        if ($fund->amount_matched == 0) {
            $fund->status = 'pending';
        } elseif ($fund->amount_matched < $fund->amount) {
            $fund->status = 'pending'; // Partially matched funds go back to pending
        }

        $fund->save();

        Log::info("Updated fund {$fund->id}: matched amount reduced by {$cancelledAmount}, new status: {$fund->status}");
    }

    /**
     * Update withdraw after a payment match cancellation.
     */
    protected function updateWithdrawAfterCancellation(Withdraw $withdraw, float $cancelledAmount): void
    {
        // Check if this is an admin withdraw created for user matching (not fee collection)
        if ($withdraw->user->role === 'admin' && ! $withdraw->platformFee) {
            $newAmountMatched = max(0, $withdraw->amount_matched - $cancelledAmount);

            if ($newAmountMatched == 0) {
                // Mark as cancelled to prevent future matching
                $withdraw->status = 'cancelled';
                $withdraw->amount_matched = 0;
                $withdraw->save();

                // Reverse admin stats tracking since this withdraw is now obsolete
                if ($withdraw->user->stats) {
                    if ($withdraw->fund->currency === 'fiat') {
                        $withdraw->user->stats->decrementTotalFiatWithdrawn($withdraw->total_withdrawable_amount);
                    } else {
                        $withdraw->user->stats->decrementTotalCryptoWithdrawn($withdraw->total_withdrawable_amount);
                    }
                    $withdraw->user->stats->save();
                }

                Log::info("Cancelled obsolete admin withdraw {$withdraw->id} created for user matching and reversed admin account tracking");

                return;
            }
        }

        // For regular user withdraws or admin fee withdraws, follow existing logic
        $withdraw->amount_matched = max(0, $withdraw->amount_matched - $cancelledAmount);

        // Update status based on remaining matched amount
        if ($withdraw->amount_matched == 0) {
            $withdraw->status = 'pending';
        } elseif ($withdraw->amount_matched < $withdraw->total_withdrawable_amount) {
            $withdraw->status = 'pending'; // Partially matched withdraws go back to pending
        }

        $withdraw->save();

        Log::info("Updated withdraw {$withdraw->id}: matched amount reduced by {$cancelledAmount}, new status: {$withdraw->status}");
    }

    /**
     * Get statistics about pending payment matches that are approaching timeout.
     */
    public function getTimeoutStatistics(): array
    {
        $timeoutHours = PlatformSetting::getSetting('fund_confirmation_timeout_hours') ?? 48;
        $timeoutDateTime = now()->subHours($timeoutHours);
        $warningDateTime = now()->subHours($timeoutHours * 0.75); // 75% of timeout period

        return [
            'timeout_hours' => $timeoutHours,
            'timed_out_matches' => PaymentMatch::where('status', 'pending')
                ->where('created_at', '<=', $timeoutDateTime)
                ->count(),
            'approaching_timeout_matches' => PaymentMatch::where('status', 'pending')
                ->where('created_at', '<=', $warningDateTime)
                ->where('created_at', '>', $timeoutDateTime)
                ->count(),
            'total_pending_matches' => PaymentMatch::where('status', 'pending')->count(),
        ];
    }

    /**
     * Get timeout information for a specific fund's payment matches.
     * Returns timeout data for pending payment matches of the fund.
     */
    public function getFundTimeoutInfo(string $fundId): array
    {
        $timeoutHours = PlatformSetting::getSetting('fund_confirmation_timeout_hours') ?? 48;

        // Get pending payment matches for this fund
        $pendingMatches = PaymentMatch::where('fund_id', $fundId)
            ->where('status', 'pending')
            ->get();

        if ($pendingMatches->isEmpty()) {
            return [
                'has_pending_matches' => false,
                'timeout_hours' => $timeoutHours,
                'message' => 'No pending payment matches found',
            ];
        }

        // Find the earliest created match (most urgent)
        $earliestMatch = $pendingMatches->sortBy('created_at')->first();
        $timeoutDateTime = $earliestMatch->created_at->addHours($timeoutHours);
        $now = now();

        // Check if timeout has passed
        $isExpired = $now->greaterThan($timeoutDateTime);

        if ($isExpired) {
            return [
                'has_pending_matches' => true,
                'timeout_hours' => $timeoutHours,
                'is_expired' => true,
                'message' => 'Payment matches have expired',
            ];
        }

        return [
            'has_pending_matches' => true,
            'timeout_hours' => $timeoutHours,
            'is_expired' => false,
            'timeout_at' => $timeoutDateTime->toISOString(),
            'timeout_at_formatted' => $timeoutDateTime->format('M j, Y \a\t g:i A'),
            'earliest_match_created_at' => $earliestMatch->created_at->toISOString(),
            'pending_matches_count' => $pendingMatches->count(),
        ];
    }
}
