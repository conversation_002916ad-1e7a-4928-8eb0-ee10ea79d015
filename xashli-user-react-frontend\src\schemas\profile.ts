import { z } from 'zod';

// Profile form validation schema
export const profileFormSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name cannot exceed 50 characters')
    .regex(
      /^[a-zA-Z\s'-]+$/,
      'First name can only contain letters, spaces, hyphens, and apostrophes'
    ),

  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name cannot exceed 50 characters')
    .regex(
      /^[a-zA-Z\s'-]+$/,
      'Last name can only contain letters, spaces, hyphens, and apostrophes'
    ),

  phone: z
    .string()
    .optional()
    .or(z.literal(''))
    .refine(value => {
      if (!value || value === '') return true;
      return /^\+?[\d\s\-\(\)]+$/.test(value);
    }, 'Please enter a valid phone number'),
});

// Type inference from schema
export type ProfileFormData = z.infer<typeof profileFormSchema>;
