import { Co<PERSON>, <PERSON>, Users, TrendingUp, Share2 } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import type { ReferralInfoApiResponse } from '../../types/referral';

interface ReferralOverviewProps {
  referralInfo: ReferralInfoApiResponse;
  onCopyLink: () => void;
  onCopyCode: () => void;
}

export function ReferralOverview({
  referralInfo,
  onCopyLink,
  onCopyCode,
}: ReferralOverviewProps) {
  return (
    <div className='space-y-6'>
      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-foreground-secondary'>
                Your Referral Code
              </p>
              <p className='text-2xl font-bold text-primary mt-1'>
                {referralInfo.referral_code}
              </p>
            </div>
            <div className='h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center'>
              <Share2 className='h-6 w-6 text-primary' />
            </div>
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={onCopyCode}
            className='mt-4 w-full'
          >
            <Copy className='h-4 w-4 mr-2' />
            Copy Code
          </Button>
        </Card>
        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-foreground-secondary'>
                Total Referees
              </p>
              <p className='text-2xl font-bold text-primary mt-1'>
                {referralInfo.referee_count}
              </p>
            </div>
            <div className='h-12 w-12 bg-blue-500/10 rounded-lg flex items-center justify-center'>
              <Users className='h-6 w-6 text-blue-500' />
            </div>
          </div>
          <p className='text-xs text-foreground-muted mt-4'>
            Users you've referred
          </p>
        </Card>{' '}
        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div className='flex-1 min-w-0 mr-4'>
              <p className='text-sm font-medium text-foreground-secondary'>
                Referral Link
              </p>
              <p className='text-sm text-foreground-muted mt-1 truncate max-w-full'>
                {referralInfo.referral_link}
              </p>
            </div>
            <div className='h-12 w-12 bg-green-500/10 rounded-lg flex items-center justify-center flex-shrink-0'>
              <Link className='h-6 w-6 text-green-500' />
            </div>
          </div>
          <Button
            variant='outline'
            size='sm'
            onClick={onCopyLink}
            className='mt-4 w-full'
          >
            <Copy className='h-4 w-4 mr-2' />
            Copy Link
          </Button>
        </Card>
      </div>

      {/* How It Works */}
      <Card className='p-6'>
        <h3 className='text-lg font-semibold text-foreground mb-4'>
          How Referrals Work
        </h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          <div className='text-center'>
            <div className='h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3'>
              <Share2 className='h-6 w-6 text-primary' />
            </div>
            <h4 className='font-medium text-foreground mb-2'>1. Share</h4>
            <p className='text-sm text-foreground-secondary'>
              Share your referral code or link with friends and family
            </p>
          </div>
          <div className='text-center'>
            <div className='h-12 w-12 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-3'>
              <Users className='h-6 w-6 text-blue-500' />
            </div>
            <h4 className='font-medium text-foreground mb-2'>2. They Join</h4>
            <p className='text-sm text-foreground-secondary'>
              When they sign up using your code, they become your referee
            </p>
          </div>
          <div className='text-center'>
            <div className='h-12 w-12 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-3'>
              <TrendingUp className='h-6 w-6 text-green-500' />
            </div>
            <h4 className='font-medium text-foreground mb-2'>
              3. Earn Rewards
            </h4>
            <p className='text-sm text-foreground-secondary'>
              Get bonuses when your referees make successful transactions
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}
