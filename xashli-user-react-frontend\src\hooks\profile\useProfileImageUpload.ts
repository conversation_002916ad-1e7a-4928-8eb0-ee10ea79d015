import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { profileService } from '../../services/profile';
import { useAuth } from '../../contexts/AuthContext';

export const useProfileImageUpload = () => {
  const { refresh } = useAuth();
  const [uploading, setUploading] = useState(false);

  const uploadImage = useCallback(
    async (file: File) => {
      try {
        setUploading(true);
        const response = await profileService.uploadProfileImage(file);

        if (response.status === 'success' && response.data) {
          toast.success(
            response.message || 'Profile image uploaded successfully'
          );

          // Refresh user data to get updated image
          await refresh();

          return response.data.profile_image_url;
        } else {
          toast.error(response.message || 'Failed to upload profile image');
          return null;
        }
      } catch (error) {
        console.error('Error uploading profile image:', error);
        toast.error('Failed to upload profile image');
        return null;
      } finally {
        setUploading(false);
      }
    },
    [refresh]
  );

  const removeImage = useCallback(async () => {
    try {
      setUploading(true);
      const response = await profileService.removeProfileImage();

      if (response.status === 'success') {
        toast.success('Profile image removed successfully');

        // Refresh user data to reflect removal
        await refresh();

        return true;
      } else {
        toast.error(response.message || 'Failed to remove profile image');
        return false;
      }
    } catch (error) {
      console.error('Error removing profile image:', error);
      toast.error('Failed to remove profile image');
      return false;
    } finally {
      setUploading(false);
    }
  }, [refresh]);

  return {
    uploading,
    uploadImage,
    removeImage,
  };
};
