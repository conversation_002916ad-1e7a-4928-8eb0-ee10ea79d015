<?php

namespace App\Console\Commands;

use App\Models\UserStat;
use App\Models\Withdraw;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CompleteWithdrawCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'withdraw:complete {withdraw_id : The ID of the withdraw to mark as completed}';

    /**
     * The console command description.
     */
    protected $description = 'Mark a withdraw as completed and trigger user stats updates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $withdrawId = $this->argument('withdraw_id');

        // Find the withdraw
        $withdraw = Withdraw::with(['fund'])->find($withdrawId);

        if (! $withdraw) {
            $this->error("Withdraw with ID {$withdrawId} not found.");

            return 1;
        }

        $this->info("Found withdraw: {$withdraw->id} (Amount: {$withdraw->total_withdrawable_amount} {$withdraw->fund->currency}, Current Status: {$withdraw->status})");

        try {
            DB::beginTransaction();

            // Check if withdraw is already completed
            if ($withdraw->status === 'completed') {
                $this->warn("Withdraw {$withdraw->id} is already marked as completed.");
                $this->info('User stats and bonuses have already been processed. Skipping to avoid duplicates.');

                DB::commit();

                return 0;
            }

            // Check if withdraw is fully matched
            if (! $withdraw->isFullyMatched()) {
                $this->error("Withdraw {$withdraw->id} is not fully matched. Matched: {$withdraw->amount_matched}, Total: {$withdraw->total_withdrawable_amount}");
                DB::rollBack();

                return 1;
            }

            // Check if all non-cancelled payment matches are confirmed before marking as completed
            $paymentMatches = $withdraw->paymentMatches()->where('status', '!=', 'cancelled')->get();

            if ($paymentMatches->isEmpty()) {
                $this->error("Withdraw {$withdraw->id} has no payment matches. Cannot mark as completed.");
                DB::rollBack();

                return 1;
            }

            $unconfirmedMatches = $paymentMatches->filter(function ($match) {
                return $match->status !== 'confirmed';
            });

            if ($unconfirmedMatches->isNotEmpty()) {
                $this->error("Withdraw {$withdraw->id} has unconfirmed payment matches:");
                foreach ($unconfirmedMatches as $match) {
                    $this->line("  - Match {$match->id}: status = {$match->status}");
                }

                $this->error('Cannot complete withdraw until all payment matches are confirmed.');
                DB::rollBack();

                return 1;
            } else {
                $this->info('All payment matches are confirmed. Proceeding with completion.');
            }

            // Update withdraw status to completed
            $withdraw->status = 'completed';
            $withdraw->save();

            $this->info('Updated withdraw status to completed.');

            // Update user stats
            $this->updateUserStats($withdraw);
            $this->info("Updated user stats for withdraw {$withdraw->id}");

            DB::commit();

            $this->info("✅ Withdraw {$withdraw->id} has been successfully marked as completed!");
            $this->info('User stats have been updated.');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Failed to complete withdraw: ' . $e->getMessage());

            return 1;
        }

        return 0;
    }

    /**
     * Update user stats for the completed withdraw
     */
    private function updateUserStats(Withdraw $withdraw): void
    {
        $userStat = UserStat::where('user_id', $withdraw->user_id)->first();
        if ($userStat) {
            if ($withdraw->fund->currency === 'fiat') {
                $userStat->incrementTotalFiatWithdrawn($withdraw->amount_matched);
                $userStat->decrementPendingFiatWithdrawal($withdraw->total_withdrawable_amount);
            } else {
                $userStat->incrementTotalCryptoWithdrawn($withdraw->amount_matched);
                $userStat->decrementPendingCryptoWithdrawal($withdraw->total_withdrawable_amount);
            }
            $userStat->save();
            $this->info("  - Updated user stats for user {$withdraw->user_id}");
        } else {
            $this->warn("  - No user stats found for user {$withdraw->user_id}");
        }
    }
}
