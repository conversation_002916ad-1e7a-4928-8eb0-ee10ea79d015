<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\UserStat;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class InitialAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'id' => Str::uuid(),
            'full_name' => 'System Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin12345'),
            'role' => 'admin',
            'is_active' => true,
            'referral_code' => strtoupper(Str::random(8)),
            'referee_count' => 0,
            'email_verified_at' => now(),
        ]);

        // Create admin stats for tracking payment matching activities
        UserStat::create([
            'user_id' => $admin->id,
        ]);

        // Create multiple bank payment methods for admin (for fiat platform fees)
        $bankPaymentMethods = [
            [
                'bank_name' => 'GTBank',
                'account_number' => '**********',
                'account_name' => 'Izuchukwu Amadi',
            ],
            [
                'bank_name' => 'Opay',
                'account_number' => '**********',
                'account_name' => 'Izuchukwu Amadi',
            ],
            [
                'bank_name' => 'Kuda MFB',
                'account_number' => '**********',
                'account_name' => 'Izuchukwu Amadi',
            ],
            [
                'bank_name' => 'Kuda MFB',
                'account_number' => '**********',
                'account_name' => 'Victor Amadi',
            ],
            [
                'bank_name' => 'FCMB',
                'account_number' => '**********',
                'account_name' => 'Victor Amadi',
            ],
        ];

        foreach ($bankPaymentMethods as $bankMethod) {
            PaymentMethod::create([
                'id' => Str::uuid(),
                'user_id' => $admin->id,
                'type' => 'bank',
                'bank_name' => $bankMethod['bank_name'],
                'account_number' => $bankMethod['account_number'],
                'account_name' => $bankMethod['account_name'],
                'status' => 'active',
                'last_used_at' => null,
            ]);
        }

        // Create multiple crypto payment methods for admin (for crypto platform fees)
        $cryptoPaymentMethods = [
            [
                'wallet_address' => '2KFCH6QpLNsPVEZ5ftusVjNdDzRUc1GfMb4s7JyFmSvA',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '8tFQk73KBGQTum5mbBKHnPwpJcvc5qosFhtBEY2LAg8p',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '9r69D6HzcXxW6DVUJMV53mUQznEiyn9XgHn6f3TQoikd',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '5AJg3zNAqpoFLAiTmFkdF73zp7YkU5ouG73MRY1xYbaU',
                'crypto_network' => 'Solana',
            ],
            [
                'wallet_address' => '9PBbxXzysevPFf9UyLgyGigRjCZz8Aad1VRf8ZNty1W5',
                'crypto_network' => 'Solana',
            ],
        ];

        foreach ($cryptoPaymentMethods as $cryptoMethod) {
            PaymentMethod::create([
                'id' => Str::uuid(),
                'user_id' => $admin->id,
                'type' => 'crypto',
                'wallet_address' => $cryptoMethod['wallet_address'],
                'crypto_network' => $cryptoMethod['crypto_network'],
                'status' => 'active',
                'last_used_at' => null,
            ]);
        }
    }
}
