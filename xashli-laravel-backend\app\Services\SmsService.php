<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/*
 * SMS Service - Using Termii SMS API
 *
 * This service handles SMS sending using the Termii API.
 *
*/
class SmsService
{
    private ?string $apiKey;

    private string $senderId;

    private string $baseUrl;

    private string $channel;

    public function __construct()
    {
        $this->apiKey = config('services.termii.api_key');
        $this->senderId = config('services.termii.sender_id', 'Xashli');
        $this->baseUrl = 'https://v3.api.termii.com/api';
        $this->channel = config('services.termii.channel', 'generic');
    }

    public function sendVerificationCode(User $user, string $verificationCode): bool
    {
        if (! $user->phone) {
            throw new \Exception('User does not have a phone number');
        }

        try {
            $this->sendSms($user->phone, $this->buildVerificationMessage($verificationCode));
            Log::info("SMS verification code sent to user: {$user->phone}", [
                'user_id' => $user->id,
                'phone' => $user->phone,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send SMS verification code to user: {$user->phone}", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    private function buildVerificationMessage(string $code): string
    {
        return "Your Xashli SMS verification code is: {$code}. This code expires in 15 minutes. Do not share this code with anyone.";
    }

    private function formatPhoneNumber(string $phoneNumber): string
    {
        $phone = preg_replace('/[^0-9]/', '', $phoneNumber);
        if (strlen($phone) === 10 && substr($phone, 0, 1) === '0') {
            $phone = '234' . substr($phone, 1);
        } elseif (strlen($phone) === 11 && substr($phone, 0, 1) !== '2') {
            $phone = '234' . $phone;
        }

        return $phone;
    }

    private function sendSms(string $phoneNumber, string $message): void
    {
        $formattedPhone = $this->formatPhoneNumber($phoneNumber);

        if (app()->environment(['local', 'testing'])) {
            Log::info('SMS would be sent via Termii', [
                'to' => $formattedPhone,
                'message' => $message,
                'channel' => $this->channel,
            ]);

            return;
        }

        if (! $this->apiKey) {
            throw new \Exception('Termii API key not configured');
        }

        $payload = [
            'to' => $formattedPhone,
            'from' => $this->senderId,
            'sms' => $message,
            'type' => 'plain',
            'channel' => $this->channel,
            'api_key' => $this->apiKey,
        ];

        $response = Http::withHeaders(['Content-Type' => 'application/json'])
            ->post($this->baseUrl . '/sms/send', $payload);

        if (! $response->successful()) {
            $errorBody = $response->json();
            $errorMessage = $errorBody['message'] ?? $response->body();
            Log::error('Termii SMS sending failed', [
                'phone' => $formattedPhone,
                'status' => $response->status(),
                'error' => $errorMessage,
                'response' => $response->body(),
            ]);
            throw new \Exception('Failed to send SMS via Termii: ' . $errorMessage);
        }

        $responseData = $response->json();
        Log::info('SMS sent successfully via Termii', [
            'to' => $formattedPhone,
            'message_id' => $responseData['message_id'] ?? null,
            'balance' => $responseData['balance'] ?? null,
        ]);
    }

    public function sendBulkSms(array $phoneNumbers, string $message): bool
    {
        // Termii bulk SMS implementation would go here
        return true;
    }

    public function getBalance(): array
    {
        // Termii balance check implementation would go here
        return ['balance' => 'Unknown', 'provider' => 'Termii'];
    }
}
