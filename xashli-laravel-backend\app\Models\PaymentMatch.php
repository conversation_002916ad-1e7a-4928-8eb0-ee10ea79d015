<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentMatch extends Model
{
    use HasUuids;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'payment_matches';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'fund_id',
        'withdraw_id',
        'fund_user_id',
        'withdraw_user_id',
        'amount',
        'payment_method_id',
        'payment_proof_image',
        'transaction_hash',
        'initiator',
        'status',
        'is_payment_sent_confirmed',
        'payment_sent_confirmed_at',
        'is_payment_received_confirmed',
        'payment_received_confirmed_at',
        'is_admin_withdraw',
        'admin_fiat_withdraw_medium',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:8',
        'initiator' => 'string',
        'status' => 'string',
        'is_payment_sent_confirmed' => 'boolean',
        'payment_sent_confirmed_at' => 'datetime',
        'is_payment_received_confirmed' => 'boolean',
        'payment_received_confirmed_at' => 'datetime',
        'is_admin_withdraw' => 'boolean',
        'admin_fiat_withdraw_medium' => 'string',
    ];

    /**
     * Get the fund for the match.
     */
    public function fund(): BelongsTo
    {
        return $this->belongsTo(Fund::class);
    }

    /**
     * Get the withdraw for the match.
     */
    public function withdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class);
    }

    /**
     * Get the fund user directly.
     */
    public function fundUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'fund_user_id');
    }

    /**
     * Get the withdraw user directly.
     */
    public function withdrawUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'withdraw_user_id');
    }

    /**
     * Get the payment method for the match.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get the disputes for this payment match.
     */
    public function disputes(): HasMany
    {
        return $this->hasMany(PaymentDispute::class);
    }

    /**
     * Check if the match has any disputes.
     */
    public function hasDispute(): bool
    {
        return $this->disputes()->exists();
    }

    /**
     * Check if the match has any active disputes (under review).
     */
    public function hasActiveDispute(): bool
    {
        return $this->disputes()->where('status', 'under_review')->exists();
    }

    /**
     * Check if a specific user has an active dispute for this match.
     */
    public function hasActiveDisputeByUser(string $userId): bool
    {
        return $this->disputes()
            ->where('dispute_user_id', $userId)
            ->where('status', 'under_review')
            ->exists();
    }

    /**
     * Check if the match is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the match is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if the match is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if the match is disputed.
     */
    public function isDisputed(): bool
    {
        return $this->status === 'disputed';
    }

    /**
     * Check if the match was initiated automatically.
     */
    public function isAutoInitiated(): bool
    {
        return $this->initiator === 'auto';
    }

    /**
     * Check if the match was initiated manually.
     */
    public function isManualInitiated(): bool
    {
        return $this->initiator === 'manual';
    }

    /**
     * Check if the payment match meets all completion requirements.
     * A match is considered complete when:
     * 1. Payment sent is confirmed
     * 2. Payment received is confirmed
     * 3. Payment proof is uploaded
     */
    public function isComplete(): bool
    {
        return $this->is_payment_sent_confirmed &&
               $this->is_payment_received_confirmed &&
               ! empty($this->payment_proof_image);
    }

    /**
     * Check if the payment match can be marked as confirmed.
     * A match can be confirmed when both payment sent and received confirmations are done
     * AND payment proof has been uploaded.
     */
    public function canMarkAsConfirmed(): bool
    {
        return $this->is_payment_sent_confirmed &&
               $this->is_payment_received_confirmed &&
               ! empty($this->payment_proof_image);
    }
}
