<?php

namespace App\Enums;

enum ElitePrivilegeLevel: int
{
    case NONE = 0;
    case LEVEL_1 = 1;
    case LEVEL_2 = 2;
    case LEVEL_3 = 3;
    case LEVEL_4 = 4;

    /**
     * Get the setting key for the maximum fund multiplier.
     */
    public function getMaximumFundMultiplierSettingKey(): string
    {
        return match ($this) {
            self::LEVEL_1 => 'elite_level_1_priviledge_maximum_fund_multiplier',
            self::LEVEL_2 => 'elite_level_2_priviledge_maximum_fund_multiplier',
            self::LEVEL_3 => 'elite_level_3_priviledge_maximum_fund_multiplier',
            self::LEVEL_4 => 'elite_level_4_priviledge_maximum_fund_multiplier',
            default => '',
        };
    }

    /**
     * Get the setting key for the transaction threshold.
     */
    public function getTransactionThresholdSettingKey(): string
    {
        return match ($this) {
            self::LEVEL_1 => 'elite_level_1_transaction_threshold',
            self::LEVEL_2 => 'elite_level_2_transaction_threshold',
            self::LEVEL_3 => 'elite_level_3_transaction_threshold',
            self::LEVEL_4 => 'elite_level_4_transaction_threshold',
            default => '',
        };
    }

    /**
     * Get the setting key for the referee threshold.
     */
    public function getRefereeThresholdSettingKey(): string
    {
        return match ($this) {
            self::LEVEL_1 => 'elite_level_1_referee_threshold',
            self::LEVEL_2 => 'elite_level_2_referee_threshold',
            self::LEVEL_3 => 'elite_level_3_referee_threshold',
            self::LEVEL_4 => 'elite_level_4_referee_threshold',
            default => '',
        };
    }

    /**
     * Get a human-readable name for this elite level.
     */
    public function getName(): string
    {
        return match ($this) {
            self::LEVEL_1 => 'Elite Level 1',
            self::LEVEL_2 => 'Elite Level 2',
            self::LEVEL_3 => 'Elite Level 3',
            self::LEVEL_4 => 'Elite Level 4',
            default => 'No Elite Privilege',
        };
    }
}
