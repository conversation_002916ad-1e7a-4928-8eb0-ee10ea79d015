import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { fundService } from '../../services';
import type { Fund, FundFilters, PaginationMeta } from '../../types';

interface DeleteDialog {
  open: boolean;
  fund: Fund | null;
}

export function useFunds() {
  const [funds, setFunds] = useState<Fund[]>([]);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FundFilters>({
    page: 1,
    per_page: 15,
  });
  const [deleteDialog, setDeleteDialog] = useState<DeleteDialog>({
    open: false,
    fund: null,
  });

  const fetchFunds = useCallback(
    async (currentFilters?: FundFilters) => {
      try {
        setLoading(true);
        const response = await fundService.getFunds(currentFilters || filters);
        if (response.status === 'success' && response.data) {
          setFunds(response.data.funds);
          setPagination(response.data.pagination);
        } else {
          toast.error(response.message || 'Failed to fetch funds');
        }
      } catch (error) {
        console.error('Error fetching funds:', error);
        toast.error('Failed to fetch funds');
      } finally {
        setLoading(false);
      }
    },
    [filters]
  );

  const applyFilters = useCallback(
    (newFilters: FundFilters) => {
      const updatedFilters = { ...filters, ...newFilters };
      setFilters(updatedFilters);
      fetchFunds(updatedFilters);
    },
    [filters, fetchFunds]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      const updatedFilters = { ...filters, page };
      setFilters(updatedFilters);
      fetchFunds(updatedFilters);
    },
    [filters, fetchFunds]
  );

  const handlePageSizeChange = useCallback(
    (per_page: number) => {
      const updatedFilters = { ...filters, per_page, page: 1 };
      setFilters(updatedFilters);
      fetchFunds(updatedFilters);
    },
    [filters, fetchFunds]
  );

  const refreshFunds = useCallback(() => {
    fetchFunds();
  }, [fetchFunds]);

  // Cancel fund
  const handleCancelFund = useCallback(async () => {
    if (!deleteDialog.fund) return;

    try {
      const response = await fundService.cancelFund(deleteDialog.fund.id);

      if (response.status === 'success') {
        toast.success('Fund cancelled successfully');
        setFunds(prevFunds =>
          prevFunds.map(fund =>
            fund.id === deleteDialog.fund!.id
              ? { ...fund, status: 'cancelled' as const }
              : fund
          )
        );
        closeDeleteDialog();
      } else {
        toast.error(response.message || 'Failed to cancel fund');
      }
    } catch (error) {
      console.error('Error cancelling fund:', error);
      toast.error('Failed to cancel fund');
    }
  }, [deleteDialog.fund]);

  const openDeleteDialog = useCallback((fund: Fund) => {
    setDeleteDialog({ open: true, fund });
  }, []);

  const closeDeleteDialog = useCallback(() => {
    setDeleteDialog({ open: false, fund: null });
  }, []);

  useEffect(() => {
    fetchFunds();
  }, []);

  return {
    funds,
    pagination,
    loading,
    filters,
    deleteDialog,
    fetchFunds,
    applyFilters,
    refreshFunds,
    handleCancelFund,
    handlePageChange,
    handlePageSizeChange,
    openDeleteDialog,
    closeDeleteDialog,
  };
}
