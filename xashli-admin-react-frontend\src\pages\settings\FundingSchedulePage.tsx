import { useState, useEffect } from 'react';
import { MainLayout } from '../../components/layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Label,
  Switch,
  Alert,
} from '../../components/ui';
import {
  Save,
  RefreshCw,
  Info,
  AlertTriangle,
  Loader2,
  Plus,
  Trash2,
  Clock,
} from 'lucide-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { platformSettingsService } from '../../services/platformSettings';
import type {
  FundingPeriod,
  FundingScheduleSettings,
} from '../../types/platformSettings';
import { formatTo12Hour } from '../../utils/format';
import { toast } from 'sonner';

export const FundingSchedulePage = () => {
  const queryClient = useQueryClient();
  const [scheduleEnabled, setScheduleEnabled] = useState(false);
  const [periods, setPeriods] = useState<FundingPeriod[]>([]);
  const [isFormDirty, setIsFormDirty] = useState(false);

  // Query to fetch current settings
  const {
    data: settings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['funding-schedule'],
    queryFn: platformSettingsService.getFundingSchedule,
  });

  // Mutation to update settings
  const updateMutation = useMutation({
    mutationFn: platformSettingsService.updateFundingSchedule,
    onSuccess: () => {
      toast.success('Funding schedule updated successfully');
      setIsFormDirty(false);
      queryClient.invalidateQueries({ queryKey: ['funding-schedule'] });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to update funding schedule'
      );
    },
  });

  // Initialize form with fetched data
  useEffect(() => {
    if (settings?.data) {
      setScheduleEnabled(settings.data.funding_schedule_enabled);
      setPeriods(settings.data.funding_schedule_periods || []);
      setIsFormDirty(false);
    }
  }, [settings]);

  const addPeriod = () => {
    const newPeriod: FundingPeriod = {
      start_time: '09:00',
      end_time: '17:00',
    };
    setPeriods([...periods, newPeriod]);
    setIsFormDirty(true);
  };

  const removePeriod = (index: number) => {
    setPeriods(periods.filter((_, i) => i !== index));
    setIsFormDirty(true);
  };

  const updatePeriod = (
    index: number,
    field: keyof FundingPeriod,
    value: string
  ) => {
    const updatedPeriods = periods.map((period, i) => {
      if (i === index) {
        return { ...period, [field]: value };
      }
      return period;
    });
    setPeriods(updatedPeriods);
    setIsFormDirty(true);
  };

  const handleSave = () => {
    if (!scheduleEnabled || periods.length === 0) {
      if (scheduleEnabled && periods.length === 0) {
        toast.error(
          'Please add at least one funding period when schedule is enabled'
        );
        return;
      }
    }

    // Validate periods
    for (let i = 0; i < periods.length; i++) {
      const period = periods[i];
      if (period.start_time >= period.end_time) {
        toast.error(`Period ${i + 1}: Start time must be before end time`);
        return;
      }
    }

    const updateData: FundingScheduleSettings = {
      funding_schedule_enabled: scheduleEnabled,
      funding_schedule_periods: periods,
    };

    updateMutation.mutate(updateData);
  };

  const handleScheduleToggle = (enabled: boolean) => {
    setScheduleEnabled(enabled);
    setIsFormDirty(true);

    // If enabling schedule and no periods exist, add a default one
    if (enabled && periods.length === 0) {
      addPeriod();
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="container mx-auto p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <div>
              <h3 className="font-semibold">Error Loading Settings</h3>
              <p className="text-sm mt-1">
                Failed to load funding schedule settings. Please try refreshing
                the page.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                className="mt-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div>
            <h1 className="text-3xl font-bold">Funding Schedule</h1>
            <p className="text-muted-foreground mt-1">
              Configure when users are allowed to create funds
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              onClick={handleSave}
              disabled={!isFormDirty || updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </div>

        {/* Main Settings Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Schedule Configuration
            </CardTitle>
            <CardDescription>
              Control when users can create new funds. When enabled, users can
              only fund during the specified time periods.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Enable/Disable Schedule */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-1">
                <Label className="text-base font-medium">
                  Enable Funding Schedule
                </Label>
                <p className="text-sm text-muted-foreground">
                  When enabled, users can only fund during configured time
                  periods
                </p>
              </div>
              <Switch
                checked={scheduleEnabled}
                onCheckedChange={handleScheduleToggle}
              />
            </div>

            {/* Schedule Periods */}
            {scheduleEnabled && (
              <div className="space-y-4">
                <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                  <div>
                    <Label className="text-base font-medium">
                      Funding Periods
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Define time ranges when users are allowed to create funds
                      (applies to all days)
                    </p>
                  </div>
                  <Button
                    onClick={addPeriod}
                    size="sm"
                    className="self-start sm:self-auto"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Period
                  </Button>
                </div>

                {periods.length === 0 ? (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <div>
                      <h4 className="font-semibold">No periods configured</h4>
                      <p className="text-sm">
                        Add at least one funding period to allow users to create
                        funds.
                      </p>
                    </div>
                  </Alert>
                ) : (
                  <div className="space-y-4">
                    {periods.map((period, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="font-medium">Period {index + 1}</h4>
                            {periods.length > 1 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removePeriod(index)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Start Time */}
                            <div className="space-y-2">
                              <Label>Start Time</Label>
                              <Input
                                type="time"
                                value={period.start_time}
                                onChange={e =>
                                  updatePeriod(
                                    index,
                                    'start_time',
                                    e.target.value
                                  )
                                }
                              />
                            </div>

                            {/* End Time */}
                            <div className="space-y-2">
                              <Label>End Time</Label>
                              <Input
                                type="time"
                                value={period.end_time}
                                onChange={e =>
                                  updatePeriod(
                                    index,
                                    'end_time',
                                    e.target.value
                                  )
                                }
                              />
                            </div>
                          </div>

                          {/* Period Summary */}
                          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                            <p className="text-sm text-gray-600">
                              <strong>Active:</strong> Every day from{' '}
                              {formatTo12Hour(period.start_time)} to{' '}
                              {formatTo12Hour(period.end_time)}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              24-hour format: {period.start_time} -{' '}
                              {period.end_time}
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Info Alert */}
            <Alert>
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 flex-shrink-0" />
                <h4 className="font-semibold">Important Notes</h4>
              </div>
              <ul className="text-sm mt-1 space-y-1">
                <li>• All times are in West Africa Time (WAT, UTC+1)</li>
                <li>• When schedule is disabled, users can fund 24/7</li>
                <li>• Time periods apply to all days of the week</li>
                <li>
                  • Multiple periods can overlap - any matching period allows
                  funding
                </li>
                <li>• Changes take effect immediately after saving</li>
              </ul>
            </Alert>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};
