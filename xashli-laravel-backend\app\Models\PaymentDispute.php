<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentDispute extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'payment_match_id',
        'dispute_user_id',
        'fund_user_id',
        'withdraw_user_id',
        'resolve_user_id',
        'reason',
        'status',
        'resolution',
        'refund_amount',
        'resolution_notes',
        'disputed_at',
        'resolved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'refund_amount' => 'decimal:8',
        'disputed_at' => 'datetime',
        'resolved_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [];

    /**
     * Get the payment match that was disputed.
     */
    public function paymentMatch(): BelongsTo
    {
        return $this->belongsTo(PaymentMatch::class);
    }

    /**
     * Get the user who initiated the dispute.
     */
    public function disputeUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dispute_user_id');
    }

    /**
     * Get the user who owns the fund in the payment match.
     */
    public function fundUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'fund_user_id');
    }

    /**
     * Get the user who owns the withdraw in the payment match.
     */
    public function withdrawUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'withdraw_user_id');
    }

    /**
     * Get the admin who resolved the dispute.
     */
    public function resolveUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolve_user_id');
    }

    /**
     * Check if the dispute is under review.
     */
    public function isUnderReview(): bool
    {
        return $this->status === 'under_review';
    }

    /**
     * Check if the dispute is resolved.
     */
    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    /**
     * Check if the dispute is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the dispute can be resolved.
     */
    public function canBeResolved(): bool
    {
        return $this->status === 'under_review';
    }

    /**
     * Assign an admin to review the dispute.
     */
    public function assignReviewer(?User $admin = null): bool
    {
        if (! $this->isUnderReview()) {
            return false;
        }

        if ($admin) {
            $this->resolve_user_id = $admin->id;
        }
        $this->save();

        return true;
    }

    /**
     * Get the duration of the dispute in hours.
     */
    public function getDurationInHours(): ?float
    {
        if (! $this->resolved_at) {
            return now()->diffInHours($this->disputed_at, false);
        }

        return $this->resolved_at->diffInHours($this->disputed_at, false);
    }

    /**
     * Get human-readable status.
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            'under_review' => 'Under Review',
            'resolved' => 'Resolved',
            'rejected' => 'Rejected',
            default => 'Unknown',
        };
    }

    /**
     * Get human-readable resolution.
     */
    public function getResolutionLabelAttribute(): ?string
    {
        return match ($this->resolution) {
            'confirmed' => 'Payment Confirmed',
            'cancelled' => 'Payment Cancelled',
            'partial_refund' => 'Partial Refund',
            default => null,
        };
    }

    /**
     * Scope to get disputes under review.
     */
    public function scopeUnderReview($query)
    {
        return $query->where('status', 'under_review');
    }

    /**
     * Scope to get resolved disputes.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope to get rejected disputes.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope to get pending disputes (under review).
     */
    public function scopePending($query)
    {
        return $query->where('status', 'under_review');
    }

    /**
     * Scope to get disputes by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('dispute_user_id', $userId);
    }

    /**
     * Scope to get disputes resolved by admin.
     */
    public function scopeResolvedByAdmin($query, $adminId)
    {
        return $query->where('resolve_user_id', $adminId);
    }

    /**
     * Scope to get disputes within date range.
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('disputed_at', [$startDate, $endDate]);
    }
}
