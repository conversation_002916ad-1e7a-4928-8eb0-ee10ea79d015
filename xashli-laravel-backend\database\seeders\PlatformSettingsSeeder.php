<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PlatformSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'platform_fee_percentage',
                'value' => '7',
                'type' => 'decimal',
                'description' => 'Platform fee percentage (7%)',
            ],
            [
                'key' => 'fiat_growth_rate',
                'value' => '50',
                'type' => 'decimal',
                'description' => 'Growth rate for fiat funds (50%)',
            ],
            [
                'key' => 'crypto_growth_rate',
                'value' => '55',
                'type' => 'decimal',
                'description' => 'Growth rate for crypto funds (55%)',
            ],
            [
                'key' => 'auto_payment_match_frequency',
                'value' => '4',
                'type' => 'int',
                'description' => 'Frequency of automatic payment matching in hours',
            ],
            [
                'key' => 'auto_payment_matching_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Whether automatic payment matching is enabled',
            ],
            [
                'key' => 'fast_maturity_days',
                'value' => '7',
                'type' => 'decimal',
                'description' => 'Number of days for fast maturity (7 days)',
            ],
            [
                'key' => 'standard_maturity_days',
                'value' => '10',
                'type' => 'decimal',
                'description' => 'Number of days for standard maturity (10 days)',
            ],
            [
                'key' => 'referral_level1_percentage',
                'value' => '10',
                'type' => 'decimal',
                'description' => 'Percentage for level 1 referrals (10%)',
            ],
            [
                'key' => 'referral_level2_percentage',
                'value' => '0',
                'type' => 'decimal',
                'description' => 'Percentage for level 2 referrals (0%)',
            ],
            [
                'key' => 'referral_level3_percentage',
                'value' => '0',
                'type' => 'decimal',
                'description' => 'Percentage for level 3 referrals (0%)',
            ],
            [
                'key' => 'fund_confirmation_timeout_hours',
                'value' => '48',
                'type' => 'int',
                'description' => 'Number of hours before a payment match times out and the funder is deactivated',
            ],
            [
                'key' => 'fiat_fast_maturity_threshold',
                'value' => '5000000',
                'type' => 'decimal',
                'description' => 'Minimum total fiat funding from level 1 referees for fast maturity (5M)',
            ],
            [
                'key' => 'crypto_fast_maturity_threshold',
                'value' => '20',
                'type' => 'decimal',
                'description' => 'Minimum total solana funding from level 1 referees for fast maturity (20 SOL)',
            ],
            [
                'key' => 'minimum_fiat_fund_amount',
                'value' => '10000',
                'type' => 'decimal',
                'description' => 'Minimum amount allowed for fiat funds (10,000 NGN)',
            ],
            [
                'key' => 'maximum_fiat_fund_amount',
                'value' => '100000',
                'type' => 'decimal',
                'description' => 'Maximum amount allowed for fiat funds (100,000 NGN)',
            ],
            [
                'key' => 'minimum_crypto_fund_amount',
                'value' => '0.1',
                'type' => 'decimal',
                'description' => 'Minimum amount allowed for crypto funds (0.1 SOL)',
            ],
            [
                'key' => 'maximum_crypto_fund_amount',
                'value' => '10',
                'type' => 'decimal',
                'description' => 'Maximum amount allowed for crypto funds (10 SOL)',
            ],
            [
                'key' => 'elite_level_1_priviledge_maximum_fund_multiplier',
                'value' => '2',
                'type' => 'decimal',
                'description' => 'Maximum fund multiplier for elite level 1 privilege (10 successful transactions/referees) - 2x',
            ],
            [
                'key' => 'elite_level_2_priviledge_maximum_fund_multiplier',
                'value' => '3',
                'type' => 'decimal',
                'description' => 'Maximum fund multiplier for elite level 2 privilege (15 successful transactions/referees) - 3x',
            ],
            [
                'key' => 'elite_level_3_priviledge_maximum_fund_multiplier',
                'value' => '4',
                'type' => 'decimal',
                'description' => 'Maximum fund multiplier for elite level 3 privilege (20 successful transactions/referees) - 4x',
            ],
            [
                'key' => 'elite_level_4_priviledge_maximum_fund_multiplier',
                'value' => '5',
                'type' => 'decimal',
                'description' => 'Maximum fund multiplier for elite level 4 privilege (25 successful transactions/referees) - 5x',
            ],
            [
                'key' => 'elite_level_1_transaction_threshold',
                'value' => '10',
                'type' => 'int',
                'description' => 'Required successful transactions for elite level 1 privilege',
            ],
            [
                'key' => 'elite_level_2_transaction_threshold',
                'value' => '15',
                'type' => 'int',
                'description' => 'Required successful transactions for elite level 2 privilege',
            ],
            [
                'key' => 'elite_level_3_transaction_threshold',
                'value' => '20',
                'type' => 'int',
                'description' => 'Required successful transactions for elite level 3 privilege',
            ],
            [
                'key' => 'elite_level_4_transaction_threshold',
                'value' => '25',
                'type' => 'int',
                'description' => 'Required successful transactions for elite level 4 privilege',
            ],
            [
                'key' => 'elite_level_1_referee_threshold',
                'value' => '10',
                'type' => 'int',
                'description' => 'Required active referees with completed cycles for elite level 1 privilege',
            ],
            [
                'key' => 'elite_level_2_referee_threshold',
                'value' => '15',
                'type' => 'int',
                'description' => 'Required active referees with completed cycles for elite level 2 privilege',
            ],
            [
                'key' => 'elite_level_3_referee_threshold',
                'value' => '20',
                'type' => 'int',
                'description' => 'Required active referees with completed cycles for elite level 3 privilege',
            ],
            [
                'key' => 'elite_level_4_referee_threshold',
                'value' => '25',
                'type' => 'int',
                'description' => 'Required active referees with completed cycles for elite level 4 privilege',
            ],
            [
                'key' => 'admin_fiat_withdraw_payment_flow',
                'value' => 'both_enabled',
                'type' => 'string',
                'description' => 'Admin fiat withdrawal payment flow mode: both_enabled (random selection), payment_method_only (manual verification only), payment_provider_only (automatic verification only)',
            ],
            [
                'key' => 'allow_admin_account_funding',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Allow admin accounts to fund user withdrawals when no user funds are available. If disabled, unmatched withdrawals remain pending until user funds become available.',
            ],
        ];

        foreach ($settings as $setting) {
            DB::table('platform_settings')->insert([
                'id' => Str::uuid(),
                'key' => $setting['key'],
                'value' => $setting['value'],
                'type' => $setting['type'],
                'description' => $setting['description'],
                'updated_at' => now(),
            ]);
        }
    }
}
