<?php

namespace App\Http\Controllers;

use App\Models\AdminActivityLog;
use App\Models\User;
use App\Traits\UserManagement;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class UserController extends Controller
{
    use UserManagement;

    /**
     * Get all users.
     */
    public function index(Request $request): JsonResponse
    {
        $query = User::query();

        // Filter by user role if provided
        if ($request->has('role') && in_array($request->role, ['user', 'admin'])) {
            $query->where('role', $request->role);
        }

        // Filter by active status if provided
        if ($request->has('is_active') && in_array($request->is_active, ['true', 'false'])) {
            $isActive = $request->is_active === 'true';
            $query->where('is_active', $isActive);
        }

        // Search by name or email if provided
        if ($request->has('search') && ! empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Sort by created_at in descending order (newest first) by default
        $sortField = $request->sort_field ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        // Paginate the results
        $perPage = min($request->get('per_page', 15), 100); // Max 100 for admin
        $paginatedUsers = $query->paginate($perPage);

        // Structure response with separate users and pagination
        $response = [
            'users' => $paginatedUsers->items(),
            'pagination' => [
                'current_page' => $paginatedUsers->currentPage(),
                'last_page' => $paginatedUsers->lastPage(),
                'per_page' => $paginatedUsers->perPage(),
                'total' => $paginatedUsers->total(),
                'from' => $paginatedUsers->firstItem(),
                'to' => $paginatedUsers->lastItem(),
            ],
        ];

        return $this->success($response, 'Users retrieved successfully');
    }

    /**
     * Get user statistics (independent of pagination and filters).
     */
    public function statistics(): JsonResponse
    {
        // Get all user statistics in one query
        $stats = User::selectRaw('
                COUNT(*) as total_users,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as total_active_users,
                SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as total_inactive_users,
                SUM(CASE WHEN role = "admin" THEN 1 ELSE 0 END) as total_admins,
                SUM(CASE WHEN role = "user" THEN 1 ELSE 0 END) as total_regular_users,
                SUM(CASE WHEN role = "admin" AND is_active = 1 THEN 1 ELSE 0 END) as total_active_admins,
                SUM(CASE WHEN role = "user" AND is_active = 1 THEN 1 ELSE 0 END) as total_active_regular_users
            ')
            ->first();

        $response = [
            'total_users' => $stats->total_users ?? 0,
            'total_active_users' => $stats->total_active_users ?? 0,
            'total_inactive_users' => $stats->total_inactive_users ?? 0,
            'total_admins' => $stats->total_admins ?? 0,
            'total_regular_users' => $stats->total_regular_users ?? 0,
            'total_active_admins' => $stats->total_active_admins ?? 0,
            'total_active_regular_users' => $stats->total_active_regular_users ?? 0,
        ];

        return $this->success($response, 'User statistics retrieved successfully');
    }

    /**
     * Get a specific user.
     */
    public function show(string $id): JsonResponse
    {
        $user = User::with(['stats', 'paymentMethods', 'funds', 'withdraws', 'referees'])->find($id);

        if (! $user) {
            return $this->notFound('User not found');
        }

        return $this->success($user, 'User retrieved successfully');
    }

    /**
     * Create a new user.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'phone' => 'nullable|string|max:20',
            'role' => 'required|in:user',
            'is_active' => 'boolean',
            'referrer_code' => 'nullable|string|exists:users,referral_code',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Generate a unique referral code
        $referralCode = strtoupper(Str::random(8));
        while (User::where('referral_code', $referralCode)->exists()) {
            $referralCode = strtoupper(Str::random(8));
        }

        try {
            DB::beginTransaction();

            // Find referrer if referrer_code is provided
            $referrer = null;
            if ($request->referrer_code) {
                $referrer = User::where('referral_code', $request->referrer_code)->first();
            }

            // Prepare user data
            $userData = [
                'full_name' => $request->full_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'phone' => $request->phone,
                'referral_code' => $referralCode,
                'referrer_code' => $request->referrer_code,
                'referrer_id' => $referrer ? $referrer->id : null,
                'is_active' => $request->is_active ?? true,
                'role' => $request->role,
            ];

            // Handle deactivation logic
            if ($request->has('is_active') && ! $request->is_active) {
                $userData['deactivated_at'] = now();
                $userData['deactivation_duration'] = 2 * 24 * 60;
            }

            // Create the user
            $user = User::create($userData);

            // Update referrer's referee count if a referrer exists
            if ($referrer) {
                $referrer->increment('referee_count');
            }

            // Create user stats
            $user->stats()->create([
                'updated_at' => now(),
            ]);

            // Log the activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_USER_CREATED,
                $user,
                ['role' => $request->role]
            );

            DB::commit();

            return $this->success($user, 'User created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create user: ' . $e->getMessage());
        }
    }

    /**
     * Update a user.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = User::find($id);

        if (! $user) {
            return $this->notFound('User not found');
        }

        $validator = Validator::make($request->all(), $this->getUserValidationRules($id, true));

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            DB::beginTransaction();

            $this->updateUserFields($user, $request, true);
            $user->save();

            // Log the activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_USER_UPDATED,
                $user,
                $request->only(['full_name', 'email', 'phone', 'is_active', 'role'])
            );

            DB::commit();

            return $this->success($user, 'User updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to update user: ' . $e->getMessage());
        }
    }

    /**
     * Delete a user.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = User::find($id);

        if (! $user) {
            return $this->notFound('User not found');
        }

        try {
            DB::beginTransaction();

            // Log the activity before deleting
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_USER_DELETED,
                $user,
                ['user_id' => $id]
            );

            // Delete the user
            $user->delete();

            DB::commit();

            return $this->success(null, 'User deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to delete user: ' . $e->getMessage());
        }
    }
}
