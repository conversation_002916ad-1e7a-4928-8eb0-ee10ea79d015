import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  Users,
  AlertCircle,
  Banknote,
  Coins,
  RefreshCw,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { paymentMatchService } from '@/services/paymentMatch';
import {
  type PaymentMatchStatistics,
  type PaymentMatchStatisticsFilters,
} from '@/types/paymentMatch';
import { MainLayout } from '@/components/layout';
import { formatCurrencyAmount } from '@/utils/format';

const PaymentMatchStatisticsPage: React.FC = () => {
  const [statistics, setStatistics] = useState<PaymentMatchStatistics | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<PaymentMatchStatisticsFilters>({});

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await paymentMatchService.getStatistics(filters);
      if (response.status === 'success' && response.data) {
        setStatistics(response.data);
      } else {
        toast.error('Failed to fetch statistics');
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
      toast.error('Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [filters]);

  const handleFilterChange = (
    key: keyof PaymentMatchStatisticsFilters,
    value: string
  ) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };
  const clearFilters = () => {
    setFilters({});
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'paid':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'disputed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!statistics) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-8">
            <p className="text-gray-500">No statistics available</p>
            <Button onClick={fetchStatistics} className="mt-4">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Payment Match Statistics
              </h1>
              <p className="text-gray-600 mt-2">
                Comprehensive analytics and insights for payment matching
                activities
              </p>
            </div>
            <Button
              onClick={fetchStatistics}
              disabled={loading}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                placeholder="User ID"
                value={filters.user_id || ''}
                onChange={e => handleFilterChange('user_id', e.target.value)}
              />
              <Input
                placeholder="Email"
                value={filters.email || ''}
                onChange={e => handleFilterChange('email', e.target.value)}
              />
              <Input
                type="date"
                placeholder="Date From"
                value={filters.date_from || ''}
                onChange={e => handleFilterChange('date_from', e.target.value)}
              />
              <Input
                type="date"
                placeholder="Date To"
                value={filters.date_to || ''}
                onChange={e => handleFilterChange('date_to', e.target.value)}
              />
            </div>
            <div className="mt-4">
              <Button onClick={clearFilters} variant="outline" size="sm">
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Matches
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statistics.overview.total_count.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Banknote className="h-8 w-8 text-green-600" />{' '}
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Fiat Amount
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrencyAmount(
                      statistics.overview.amount.fiat,
                      'fiat'
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Coins className="h-8 w-8 text-purple-600" />{' '}
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Crypto Amount
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrencyAmount(
                      statistics.overview.amount.crypto,
                      'crypto'
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Disputes
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {statistics.disputes.total_count.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Status Breakdown */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(statistics.statuses).map(([status, data]) => (
                <div key={status} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge
                      className={getStatusColor(status)}
                      variant="secondary"
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Badge>
                    <span className="text-sm font-medium">
                      {data.total_count.toLocaleString()} matches
                    </span>
                  </div>

                  <div className="space-y-2">
                    {' '}
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Fiat:</span>
                      <span className="font-medium">
                        {data.count.fiat} (
                        {formatCurrencyAmount(data.amount.fiat, 'fiat')})
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Crypto:</span>
                      <span className="font-medium">
                        {data.count.crypto} (
                        {formatCurrencyAmount(data.amount.crypto, 'crypto')})
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Initiators and Disputes */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Initiators */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Match Initiators
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Automatic Matches</span>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    {statistics.initiators.auto_count.toLocaleString()}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Manual Matches</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    {statistics.initiators.manual_count.toLocaleString()}
                  </Badge>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between font-medium">
                    <span>Total</span>
                    <span>
                      {(
                        statistics.initiators.auto_count +
                        statistics.initiators.manual_count
                      ).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Disputes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Dispute Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Pending</span>
                  <Badge
                    variant="outline"
                    className="bg-yellow-50 text-yellow-700"
                  >
                    {statistics.disputes.pending_count.toLocaleString()}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Under Review</span>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    {statistics.disputes.under_review_count.toLocaleString()}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Resolved</span>
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700"
                  >
                    {statistics.disputes.resolved_count.toLocaleString()}
                  </Badge>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between font-medium">
                    <span>Total Disputes</span>
                    <span>
                      {statistics.disputes.total_count.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};

export default PaymentMatchStatisticsPage;
