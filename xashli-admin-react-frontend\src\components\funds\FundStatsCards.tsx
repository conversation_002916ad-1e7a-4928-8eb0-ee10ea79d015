import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { TrendingUp, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import type { FundStats } from '../../types/fund';
import { formatCurrencyAmount } from '../../utils/format';
import { LoadingSpinner } from '@/components/ui/loading';

interface FundStatsCardsProps {
  stats: FundStats | null;
  isLoading: boolean;
}

export const FundStatsCards: React.FC<FundStatsCardsProps> = ({
  stats,
  isLoading,
}) => {
  const statsCards = [
    {
      title: 'Total Funds',
      count: stats?.overview?.total_count || 0,
      fiatAmount: stats?.overview?.amount?.fiat || 0,
      cryptoAmount: stats?.overview?.amount?.crypto || 0,
      fiatCount: stats?.overview?.count?.fiat || 0,
      cryptoCount: stats?.overview?.count?.crypto || 0,
      icon: TrendingUp,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Pending',
      count: stats?.statuses?.pending?.total_count || 0,
      fiatAmount: stats?.statuses?.pending?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.pending?.amount?.crypto || 0,
      fiatCount: stats?.statuses?.pending?.count?.fiat || 0,
      cryptoCount: stats?.statuses?.pending?.count?.crypto || 0,
      icon: AlertTriangle,
      iconColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Matched',
      count: stats?.statuses?.matched?.total_count || 0,
      fiatAmount: stats?.statuses?.matched?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.matched?.amount?.crypto || 0,
      fiatCount: stats?.statuses?.matched?.count?.fiat || 0,
      cryptoCount: stats?.statuses?.matched?.count?.crypto || 0,
      icon: CheckCircle,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Completed',
      count: stats?.statuses?.completed?.total_count || 0,
      fiatAmount: stats?.statuses?.completed?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.completed?.amount?.crypto || 0,
      fiatCount: stats?.statuses?.completed?.count?.fiat || 0,
      cryptoCount: stats?.statuses?.completed?.count?.crypto || 0,
      icon: CheckCircle,
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Cancelled',
      count: stats?.statuses?.cancelled?.total_count || 0,
      fiatAmount: stats?.statuses?.cancelled?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.cancelled?.amount?.crypto || 0,
      fiatCount: stats?.statuses?.cancelled?.count?.fiat || 0,
      cryptoCount: stats?.statuses?.cancelled?.count?.crypto || 0,
      icon: XCircle,
      iconColor: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      {statsCards.map(card => {
        const Icon = card.icon;

        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.iconColor}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  card.count.toLocaleString()
                )}
              </div>
              <div className="mt-1 space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Fiat:</span>
                  <span>
                    {card.fiatCount} (
                    {formatCurrencyAmount(card.fiatAmount, 'fiat')})
                  </span>
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Crypto:</span>
                  <span>
                    {card.cryptoCount} (
                    {formatCurrencyAmount(card.cryptoAmount, 'crypto')})
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
