<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class PaymentMatchingCycle extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'mode',
        'frequency_hours',
        'next_run',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'frequency_hours' => 'integer',
        'next_run' => 'datetime',
        'is_active' => 'boolean',
        'updated_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Check if the matching cycle is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if the matching cycle is automatic.
     */
    public function isAutomatic(): bool
    {
        return $this->mode === 'auto';
    }

    /**
     * Check if the matching cycle is manual.
     */
    public function isManual(): bool
    {
        return $this->mode === 'manual';
    }

    /**
     * Check if the matching cycle is due to run.
     */
    public function isDue(): bool
    {
        return $this->is_active && now()->gte($this->next_run);
    }

    /**
     * Update the next run time based on the frequency.
     */
    public function updateNextRun(): bool
    {
        $this->next_run = now()->addHours($this->frequency_hours);
        $this->updated_at = now();

        return $this->save();
    }
}
