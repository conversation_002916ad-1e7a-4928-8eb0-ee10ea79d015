<?php

namespace App\Console\Commands;

use App\Models\PaymentMatchingCycle;
use App\Models\PlatformSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class InitPaymentMatchingCycleCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:init-payment-matching-cycle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize the payment matching cycle for automatic payment matching';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing matching cycle...');

        // Check if a payment matching cycle already exists
        $existingCycle = PaymentMatchingCycle::where('mode', 'auto')->first();
        if ($existingCycle) {
            $this->warn('An automatic payment matching cycle already exists.');

            if ($this->confirm('Do you want to reset the existing cycle?', false)) {
                $existingCycle->delete();
                $this->info('Existing cycle deleted.');
            } else {
                return 0;
            }
        }

        // Get the auto payment match frequency from settings
        $frequency = PlatformSetting::getSetting('auto_payment_match_frequency');
        if (! $frequency) {
            $this->error('Auto payment match frequency setting not found.');

            return 1;
        }

        // Create the payment matching cycle
        $matchingCycle = PaymentMatchingCycle::create([
            'id' => Str::uuid(),
            'mode' => 'auto',
            'frequency_hours' => $frequency,
            'next_run' => now()->addHours($frequency),
            'is_active' => true,
            'updated_at' => now(),
        ]);

        $this->info('Payment matching cycle initialized successfully.');
        $this->info("Mode: {$matchingCycle->mode}");
        $this->info("Frequency: {$matchingCycle->frequency_hours} hours");
        $this->info("Next run: {$matchingCycle->next_run}");
        $this->info('Status: ' . ($matchingCycle->is_active ? 'Active' : 'Inactive'));

        return 0;
    }
}
