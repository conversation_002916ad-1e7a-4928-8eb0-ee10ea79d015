import { apiClient } from './api';
import type {
  AdminActivityLog,
  AdminActivityLogFilters,
  PaginatedAdminActivityLogs,
  ActionTypeOption,
  ApiResponse,
} from '../types';

export const adminActivityLogService = {
  /**
   * Get all admin activity logs with filtering and pagination
   */
  getActivityLogs: async (
    filters?: AdminActivityLogFilters
  ): Promise<ApiResponse<PaginatedAdminActivityLogs>> => {
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const queryString = params.toString();
    const url = queryString
      ? `/admin-activity-logs?${queryString}`
      : '/admin-activity-logs';

    return apiClient.get<PaginatedAdminActivityLogs>(url);
  },
  /**
   * Get a specific admin activity log by ID
   */
  getActivityLog: async (
    id: string
  ): Promise<ApiResponse<AdminActivityLog>> => {
    return apiClient.get<AdminActivityLog>(`/admin-activity-logs/${id}`);
  },

  /**
   * Get available action types for filtering
   */
  getActionTypes: async (): Promise<ApiResponse<ActionTypeOption[]>> => {
    return apiClient.get<ActionTypeOption[]>(
      '/admin-activity-logs/action-types'
    );
  },
};
