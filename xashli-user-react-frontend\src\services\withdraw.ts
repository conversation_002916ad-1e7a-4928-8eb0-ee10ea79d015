import { apiClient } from './api';
import type { ApiResponse } from '../types';
import type {
  Withdraw,
  CreateWithdrawData,
  WithdrawFilters,
  WithdrawStatistics,
  PaginatedWithdraws,
} from '../types';

export const withdrawService = {
  /**
   * Get user withdraws with optional filters
   */
  getWithdraws: async (
    filters?: WithdrawFilters
  ): Promise<ApiResponse<PaginatedWithdraws>> => {
    const params = new URLSearchParams();

    if (filters?.status) {
      params.append('status', filters.status);
    }
    if (filters?.currency) {
      params.append('currency', filters.currency);
    }
    if (filters?.sort_field) {
      params.append('sort_field', filters.sort_field);
    }
    if (filters?.sort_direction) {
      params.append('sort_direction', filters.sort_direction);
    }
    if (filters?.page) {
      params.append('page', filters.page.toString());
    }
    if (filters?.per_page) {
      params.append('per_page', filters.per_page.toString());
    }

    const queryString = params.toString();
    const url = queryString ? `/withdraws?${queryString}` : '/withdraws';

    return apiClient.get<PaginatedWithdraws>(url);
  },

  /**
   * Get a specific withdraw by ID
   */
  getWithdraw: async (id: string): Promise<ApiResponse<Withdraw>> => {
    return apiClient.get<Withdraw>(`/withdraws/${id}`);
  },

  /**
   * Create a new withdraw request
   */
  createWithdraw: async (
    data: CreateWithdrawData
  ): Promise<ApiResponse<Withdraw>> => {
    return apiClient.post<Withdraw>('/withdraws', data);
  },

  /**
   * Get withdraw statistics
   */
  getStatistics: async (params?: {
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<WithdrawStatistics>> => {
    return apiClient.get<WithdrawStatistics>('/withdraws/statistics', {
      params,
    });
  },
};
