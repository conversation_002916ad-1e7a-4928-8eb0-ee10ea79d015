/**
 * Cookie utility functions
 * These are simple wrappers around js-cookie for type safety
 */

import Cookies from 'js-cookie';

export interface CookieOptions {
  expires?: number | Date;
  path?: string;
  domain?: string;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  httpOnly?: boolean;
}

export const getCookie = (name: string): string | undefined => {
  return Cookies.get(name);
};

export const setCookie = (
  name: string,
  value: string,
  options?: CookieOptions
): void => {
  Cookies.set(name, value, options);
};

export const removeCookie = (name: string, options?: CookieOptions): void => {
  Cookies.remove(name, options);
};

export const getAllCookies = (): { [key: string]: string } => {
  return Cookies.get();
};
