import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { fundService } from '../../services';
import type {
  Fund,
  ConfirmPaymentSentData,
  UploadPaymentProofData,
} from '../../types';

export function useFund(id: string) {
  const [fund, setFund] = useState<Fund | null>(null);
  const [loading, setLoading] = useState(!!id && id.trim() !== '');
  const [actionLoading, setActionLoading] = useState(false);

  const fetchFund = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fundService.getFund(id);
      if (response.status === 'success' && response.data) {
        setFund(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch fund');
      }
    } catch (error) {
      console.error('Error fetching fund:', error);
      toast.error('Failed to fetch fund');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const cancelFund = useCallback(async () => {
    if (!fund) return;

    try {
      setActionLoading(true);
      const response = await fundService.cancelFund(fund.id);

      if (response.status === 'success' && response.data) {
        toast.success('Fund cancelled successfully');
        setFund(response.data);
        return true;
      } else {
        toast.error(response.message || 'Failed to cancel fund');
        return false;
      }
    } catch (error) {
      console.error('Error cancelling fund:', error);
      toast.error('Failed to cancel fund');
      return false;
    } finally {
      setActionLoading(false);
    }
  }, [fund]);

  const confirmPaymentSent = useCallback(
    async (data: ConfirmPaymentSentData) => {
      if (!fund) return;

      try {
        setActionLoading(true);
        const response = await fundService.confirmPaymentSent(fund.id, data);

        if (response.status === 'success' && response.data) {
          toast.success('Payment confirmation sent successfully');
          setFund(response.data);
          return true;
        } else {
          toast.error(response.message || 'Failed to confirm payment');
          return false;
        }
      } catch (error) {
        console.error('Error confirming payment:', error);
        toast.error('Failed to confirm payment');
        return false;
      } finally {
        setActionLoading(false);
      }
    },
    [fund]
  );

  const uploadPaymentProof = useCallback(
    async (data: UploadPaymentProofData) => {
      if (!fund) return;

      try {
        setActionLoading(true);
        const response = await fundService.uploadPaymentProof(fund.id, data);

        if (response.status === 'success' && response.data) {
          toast.success('Payment proof uploaded successfully');
          setFund(response.data);
          return true;
        } else {
          toast.error(response.message || 'Failed to upload payment proof');
          return false;
        }
      } catch (error) {
        console.error('Error uploading payment proof:', error);
        toast.error('Failed to upload payment proof');
        return false;
      } finally {
        setActionLoading(false);
      }
    },
    [fund]
  );

  const refreshFund = useCallback(() => {
    fetchFund();
  }, [fetchFund]);

  useEffect(() => {
    if (id && id.trim() !== '') {
      fetchFund();
    }
  }, [id, fetchFund]);

  return {
    fund,
    loading,
    actionLoading,
    fetchFund,
    cancelFund,
    confirmPaymentSent,
    uploadPaymentProof,
    refreshFund,
  };
}
