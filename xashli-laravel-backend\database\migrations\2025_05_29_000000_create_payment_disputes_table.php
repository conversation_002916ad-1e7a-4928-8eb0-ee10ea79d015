<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_disputes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('payment_match_id');
            $table->uuid('dispute_user_id');
            $table->uuid('fund_user_id');
            $table->uuid('withdraw_user_id');
            $table->uuid('resolve_user_id')->nullable();
            $table->text('reason');
            $table->enum('status', ['under_review', 'resolved', 'rejected'])->default('under_review');
            $table->enum('resolution', ['confirmed', 'cancelled', 'partial_refund'])->nullable();
            $table->decimal('refund_amount', 20, 8)->nullable();
            $table->text('resolution_notes')->nullable();
            $table->timestamp('disputed_at')->useCurrent();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('payment_match_id')->references('id')->on('payment_matches')->onDelete('cascade');
            $table->foreign('dispute_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('fund_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('withdraw_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('resolve_user_id')->references('id')->on('users')->onDelete('set null');

            // Indexes for better performance
            $table->index(['status', 'disputed_at']);
            $table->index(['dispute_user_id', 'disputed_at']);
            $table->index(['fund_user_id', 'disputed_at']);
            $table->index(['withdraw_user_id', 'disputed_at']);
            $table->index(['resolve_user_id', 'resolved_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_disputes');
    }
};
