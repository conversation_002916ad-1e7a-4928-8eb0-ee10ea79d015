import { useState, useEffect, useCallback } from 'react';
import { confirmationService } from '../services';
import { showToast } from '../utils/toast';
import type { ConfirmationCodeGenerateRequest } from '../types/confirmation';

interface UseConfirmationCodeOptions {
  action: string;
  context: string;
  cooldownDuration?: number; // in seconds, default 45
}

interface UseConfirmationCodeReturn {
  // State
  code: string;
  isRequesting: boolean;
  isRequested: boolean;
  cooldownRemaining: number;
  error: string | null;

  // Actions
  setCode: (code: string) => void;
  requestCode: () => Promise<void>;
  clearError: () => void;
  reset: () => void;

  // Computed
  canRequest: boolean;
  isCodeValid: boolean;
}

export const useConfirmationCode = ({
  action,
  context,
  cooldownDuration = 45,
}: UseConfirmationCodeOptions): UseConfirmationCodeReturn => {
  const [code, setCode] = useState('');
  const [isRequesting, setIsRequesting] = useState(false);
  const [isRequested, setIsRequested] = useState(false);
  const [cooldownRemaining, setCooldownRemaining] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Cooldown timer effect
  useEffect(() => {
    if (cooldownRemaining > 0) {
      const timer = setTimeout(() => {
        setCooldownRemaining(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [cooldownRemaining]);

  const requestCode = useCallback(async () => {
    if (isRequesting || cooldownRemaining > 0) return;

    setIsRequesting(true);
    setError(null);

    try {
      const requestData: ConfirmationCodeGenerateRequest = {
        action,
        context,
      };

      const response = await confirmationService.generate(requestData);

      if (response.status === 'success') {
        setIsRequested(true);
        setCooldownRemaining(cooldownDuration);
        showToast.success(
          response.message || 'Confirmation code sent successfully'
        );
      } else {
        setError(response.message || 'Failed to send confirmation code');
        showToast.error(response.message || 'Failed to send confirmation code');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Network error. Please try again.';
      setError(errorMessage);
      showToast.error(errorMessage);
    } finally {
      setIsRequesting(false);
    }
  }, [action, context, cooldownDuration, isRequesting, cooldownRemaining]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setCode('');
    setIsRequested(false);
    setCooldownRemaining(0);
    setError(null);
  }, []);

  // Computed values
  const canRequest = !isRequesting && cooldownRemaining === 0;
  const isCodeValid = code.trim().length >= 6; // Assuming 6-digit codes

  return {
    // State
    code,
    isRequesting,
    isRequested,
    cooldownRemaining,
    error,

    // Actions
    setCode,
    requestCode,
    clearError,
    reset,

    // Computed
    canRequest,
    isCodeValid,
  };
};
