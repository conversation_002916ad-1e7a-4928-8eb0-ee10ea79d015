import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { ArrowLeft, Mail } from 'lucide-react';
import { useForgotPassword } from '@/hooks/auth';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordPage() {
  const { handleForgotPassword, handleResend, isLoading, isSubmitted } =
    useForgotPassword();

  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    await handleForgotPassword({ email: data.email });
  };

  const handleResendRequest = async () => {
    const email = getValues('email');
    if (email) {
      await handleResend(email);
    }
  };

  if (isSubmitted) {
    return (
      <div className='space-y-6'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-foreground'>
            Check your email
          </h2>
          <p className='mt-2 text-foreground-secondary'>
            We've sent password reset instructions to your email
          </p>
        </div>

        <Card>
          <CardContent className='pt-6'>
            <div className='text-center space-y-4'>
              <div className='mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center'>
                <Mail className='w-8 h-8 text-primary' />
              </div>

              <div className='space-y-2'>
                <h3 className='text-lg font-semibold text-foreground'>
                  Reset link sent!
                </h3>
                <p className='text-foreground-secondary text-sm'>
                  We've sent a password reset link to{' '}
                  <strong>{getValues('email')}</strong>
                </p>
                <p className='text-foreground-muted text-xs'>
                  Didn't receive the email? Check your spam folder or try again.
                </p>
              </div>

              <div className='space-y-3 pt-4'>
                <Button
                  onClick={handleResendRequest}
                  variant='secondary'
                  className='w-full'
                  isLoading={isLoading}
                >
                  {isLoading ? 'Resending...' : 'Resend email'}
                </Button>

                <Link to='/auth/login'>
                  <Button variant='ghost' className='w-full'>
                    <ArrowLeft className='w-4 h-4 mr-2' />
                    Back to sign in
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold text-foreground'>Forgot password?</h2>
        <p className='mt-2 text-foreground-secondary'>
          No worries, we'll send you reset instructions
        </p>
      </div>

      <Card>
        <CardContent className='pt-6'>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
            <Input
              label='Email'
              type='email'
              placeholder='Enter your email address'
              error={errors.email?.message}
              {...register('email')}
            />

            <Button type='submit' className='w-full' isLoading={isLoading}>
              {isLoading ? 'Sending...' : 'Send reset instructions'}
            </Button>
          </form>

          <div className='mt-6'>
            <Link to='/auth/login'>
              <Button variant='ghost' className='w-full'>
                <ArrowLeft className='w-4 h-4 mr-2' />
                Back to sign in
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      <div className='text-center'>
        <p className='text-sm text-foreground-secondary'>
          Don't have an account?{' '}
          <Link
            to='/auth/register'
            className='font-medium text-primary hover:text-primary/90'
          >
            Sign up
          </Link>
        </p>
      </div>
    </div>
  );
}
