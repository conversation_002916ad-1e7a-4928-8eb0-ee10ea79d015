export interface RefereeData {
  id: string;
  full_name: string;
  email: string;
  created_at: string;
  referee_count: number;
  is_active: boolean;
  referrer_id?: string;
  level?: number; // Added level property for merged referees
}

export interface RefereesApiResponse {
  target_user: {
    id: string;
    full_name: string;
    email: string;
    referral_code: string;
  } | null;
  referees: {
    level1: RefereeData[];
    level2: RefereeData[];
    level3: RefereeData[];
    all: RefereeData[]; // Merged and sorted list, consistent with counts naming
  };
  counts: {
    level1: number;
    level2: number;
    level3: number;
    total: number;
  };
}

export interface ReferralInfoApiResponse {
  target_user: {
    id: string;
    full_name: string;
    email: string;
    referral_code: string;
  };
  referral_code: string;
  referee_count: number;
  referral_link: string;
}

export interface BonusesApiResponse {
  bonuses: any[];
  summary: {
    fiat?: {
      total_referral_bonus_amount?: number;
      withdrawable_referral_amount?: number;
      consumed_referral_amount?: number;
      pending_referral_amount?: number;
      count?: number;
    };
    crypto?: {
      total_referral_bonus_amount?: number;
      withdrawable_referral_amount?: number;
      consumed_referral_amount?: number;
      pending_referral_amount?: number;
      count?: number;
    };
    total_count?: number;
  };
  target_user?: {
    id: string;
    full_name: string;
    email: string;
    referral_code: string;
  };
}

export interface ExtendedRefereeData extends RefereeData {
  level: number;
}

export interface ReferralFilters {
  date_from?: string;
  date_to?: string;
}
