import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { fundService } from '../../services';
import type { CreateFundData } from '../../types';

export function useFundForm() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const createFund = useCallback(
    async (data: CreateFundData) => {
      try {
        setLoading(true);
        const response = await fundService.createFund(data);

        if (response.status === 'success' && response.data) {
          toast.success('Fund created successfully');
          navigate('/funds');
          return true;
        } else {
          toast.error(response.message || 'Failed to create fund');
          return false;
        }
      } catch (error: any) {
        console.error('Error creating fund:', error);
        // Extract error message from API response
        const errorMessage =
          error?.response?.data?.message || 'Failed to create fund';
        toast.error(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [navigate]
  );

  return {
    loading,
    createFund,
  };
}
