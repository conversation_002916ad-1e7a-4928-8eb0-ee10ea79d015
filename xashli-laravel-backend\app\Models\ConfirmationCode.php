<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConfirmationCode extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'code',
        'action',
        'context',
        'expires_at',
        'used_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
    ];

    // Contexts
    public const CONTEXT_PAYMENT_METHOD = 'payment_method';

    // Actions
    public const ACTION_CREATE = 'create';

    public const ACTION_UPDATE = 'update';

    public const ACTION_DELETE = 'delete';

    // Allowed actions and contexts
    public const ALLOWED_ACTIONS = [
        self::ACTION_CREATE,
        self::ACTION_UPDATE,
        self::ACTION_DELETE,
    ];

    public const ALLOWED_CONTEXTS = [
        self::CONTEXT_PAYMENT_METHOD,
    ];

    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    public function isUsed(): bool
    {
        return ! is_null($this->used_at);
    }

    /**
     * Generate a new confirmation code for a user and action.
     */
    public static function generateCode($userId, $action, $context, $expiresInMinutes = 10)
    {
        $code = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes($expiresInMinutes);

        return self::create([
            'user_id' => $userId,
            'code' => $code,
            'action' => $action,
            'context' => $context,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Verify a confirmation code for a user and action.
     */
    public static function verifyCode($userId, $action, $code, $context)
    {
        $query = self::where('user_id', $userId)
            ->where('action', $action)
            ->where('code', $code)
            ->where('context', $context)
            ->whereNull('used_at')
            ->where('expires_at', '>', now());

        $confirmationCode = $query->first();

        if (! $confirmationCode) {
            return false;
        }

        $confirmationCode->used_at = now();
        $confirmationCode->save();

        return true;
    }
}
