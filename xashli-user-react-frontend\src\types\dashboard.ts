export interface DashboardStats {
  profile: {
    active_since: string;
    referral_code: string;
    referral_link: string;
    referee_count: number;
  };
  privileges: {
    has_premium_privilege: boolean;
    has_elite_privilege: boolean;
    elite_privilege_level: number;
    maximum_fund_amount: {
      fiat: number;
      crypto: number;
    };
    maturity_days: number;
  };
  funds: {
    counts: {
      total: number;
      statuses: {
        active: number;
        completed: number;
        cancelled: number;
      };
      currencies: {
        fiat: number;
        crypto: number;
      };
    };
    amounts: {
      fiat: {
        total: number;
        growth: number;
      };
      crypto: {
        total: number;
        growth: number;
      };
    };
  };
  withdraws: {
    counts: {
      total: number;
      statuses: {
        pending: number;
        in_progress: number;
        completed: number;
      };
      currencies: {
        fiat: number;
        crypto: number;
      };
    };
    amounts: {
      fiat: { total: number };
      crypto: { total: number };
    };
  };
  payment_methods: {
    counts: {
      total: number;
      types: {
        bank_accounts: number;
        crypto_wallets: number;
      };
    };
  };
  referrals: {
    total_referees: number;
    amounts: {
      fiat: {
        total: number;
        available: number;
        consumed: number;
      };
      crypto: {
        total: number;
        available: number;
        consumed: number;
      };
    };
  };
  payment_matches: {
    counts: {
      total: number;
      statuses: {
        pending: number;
        paid: number;
        confirmed: number;
      };
    };
  };
}
