import { Link } from 'react-router-dom';
import {
  User,
  Wallet,
  ArrowUpRight,
  CreditCard,
  Users,
  GitBranch,
  TrendingUp,
  Plus,
  Eye,
  BarChart3,
  Award,
  Copy,
  ExternalLink,
  Crown,
  Star,
  Shield,
} from 'lucide-react';
import { useDashboardStats } from '../hooks/dashboard/useDashboardStats';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Badge } from '../components/ui/Badge';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { copyToClipboard } from '../utils/copy';
import { formatCurrencyAmount } from '../utils/format';

export function DashboardPage() {
  const { data, isLoading, isError } = useDashboardStats();
  const stats = data?.data;

  const getStatusColor = (status: string, count: number) => {
    if (count === 0) return 'outline';
    switch (status) {
      case 'active':
      case 'pending':
        return 'default'; // Primary background with white text
      case 'completed':
      case 'confirmed':
        return 'secondary'; // Secondary background
      case 'cancelled':
        return 'destructive'; // Red background with white text
      case 'paid':
        return 'default'; // Primary background
      default:
        return 'outline'; // Border only with foreground text
    }
  };

  const getEliteLevelInfo = (level: number) => {
    if (level === 0) {
      return {
        text: 'Inactive',
        variant: 'outline' as const,
        className: '',
        icon: <Star className='h-6 w-6 text-foreground-muted' />,
      };
    }

    const levelConfigs = {
      1: {
        text: 'Level 1',
        variant: 'default' as const,
        className: 'bg-yellow-500 text-white',
        icon: <Star className='h-6 w-6 text-yellow-500' />,
      },
      2: {
        text: 'Level 2',
        variant: 'default' as const,
        className: 'bg-orange-500 text-white',
        icon: <Star className='h-6 w-6 text-orange-500' />,
      },
      3: {
        text: 'Level 3',
        variant: 'default' as const,
        className: 'bg-red-500 text-white',
        icon: <Star className='h-6 w-6 text-red-500' />,
      },
      4: {
        text: 'Level 4',
        variant: 'default' as const,
        className: 'bg-purple-500 text-white',
        icon: <Star className='h-6 w-6 text-purple-500' />,
      },
    };

    return (
      levelConfigs[level as keyof typeof levelConfigs] || {
        text: 'Inactive',
        variant: 'outline' as const,
        className: '',
        icon: <Star className='h-6 w-6 text-foreground-muted' />,
      }
    );
  };

  if (isLoading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center py-12'>
          <div className='text-destructive text-6xl mb-4'>⚠️</div>
          <p className='text-destructive text-lg'>
            Failed to load dashboard data.
          </p>
          <Button
            variant='outline'
            className='mt-4'
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
          <div>
            <h1 className='text-3xl font-bold text-foreground flex items-center gap-3'>
              <TrendingUp className='h-8 w-8 text-primary' />
              Dashboard
            </h1>
            <p className='text-foreground-secondary mt-2'>
              Welcome back! Here's your Xashli overview
            </p>
          </div>
          <div className='flex gap-3'>
            <Link to='/funds/create'>
              <Button className='flex items-center gap-2'>
                <Plus className='h-4 w-4' />
                Create Fund
              </Button>
            </Link>
            <Link to='/payment-methods/create'>
              <Button variant='outline' className='flex items-center gap-2'>
                <CreditCard className='h-4 w-4' />
                Add Payment Method
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Quick Stats Row */}
      <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mb-8'>
        <Card className='bg-gradient-to-br from-primary/10 to-primary/5 border-primary/20'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-foreground-muted'>
                  Total Funds
                </p>
                <p className='text-3xl font-bold text-foreground'>
                  {stats.funds.counts.total}
                </p>
              </div>
              <Wallet className='h-8 w-8 text-primary' />
            </div>
          </CardContent>
        </Card>

        <Card className='bg-gradient-to-br from-success/10 to-success/5 border-success/20'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-foreground-muted'>
                  Active Withdraws
                </p>
                <p className='text-3xl font-bold text-foreground'>
                  {stats.withdraws.counts.statuses.in_progress}
                </p>
              </div>
              <ArrowUpRight className='h-8 w-8 text-success' />
            </div>
          </CardContent>
        </Card>

        <Card className='bg-gradient-to-br from-blue-500/10 to-blue-600/5 border-blue-500/30'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-foreground-muted'>
                  Payment Methods
                </p>
                <p className='text-3xl font-bold text-foreground'>
                  {stats.payment_methods.counts.total}
                </p>
              </div>
              <CreditCard className='h-8 w-8 text-blue-400' />
            </div>
          </CardContent>
        </Card>

        <Card className='bg-gradient-to-br from-warning/10 to-warning/5 border-warning/20'>
          <CardContent className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-foreground-muted'>
                  Referrals
                </p>
                <p className='text-3xl font-bold text-foreground'>
                  {stats.referrals.total_referees}
                </p>
              </div>
              <Users className='h-8 w-8 text-warning' />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className='grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6'>
        {/* Profile Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <User className='h-5 w-5 text-primary' />
              Profile Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Active Since
                </span>
                <span className='text-sm font-medium'>
                  {stats.profile.active_since}
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Referral Code
                </span>
                <Badge variant='outline' className='font-mono text-xs'>
                  {stats.profile.referral_code}
                </Badge>
              </div>
              <div className='flex flex-col space-y-2'>
                <span className='text-sm text-foreground-muted'>
                  Referral Link
                </span>
                <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded border'>
                  <span className='text-xs font-mono text-foreground-muted flex-1 truncate'>
                    {stats.profile.referral_link}
                  </span>
                  <button
                    onClick={() =>
                      copyToClipboard(
                        stats.profile.referral_link,
                        'Referral link copied!'
                      )
                    }
                    className='text-primary hover:text-primary-dark transition-colors'
                    title='Copy referral link'
                  >
                    <Copy className='h-4 w-4' />
                  </button>
                  <a
                    href={stats.profile.referral_link}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='text-primary hover:text-primary-dark transition-colors'
                    title='Open referral link'
                  >
                    <ExternalLink className='h-4 w-4' />
                  </a>
                </div>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Total Referees
                </span>
                <Badge variant='secondary' className='text-white font-bold'>
                  {stats.profile.referee_count}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privileges Card */}
        {stats.privileges && (
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='flex items-center gap-2 text-lg'>
                <Shield className='h-5 w-5 text-amber-500' />
                Account Privileges
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {/* Privilege Status */}
                <div className='grid grid-cols-2 gap-3'>
                  <div className='text-center p-3 bg-background-tertiary rounded border'>
                    <div className='flex items-center justify-center mb-2'>
                      {stats.privileges.has_premium_privilege ? (
                        <Crown className='h-6 w-6 text-amber-500' />
                      ) : (
                        <Crown className='h-6 w-6 text-foreground-muted' />
                      )}
                    </div>
                    <p className='text-xs font-medium mb-1'>Premium</p>
                    <Badge
                      variant={
                        stats.privileges.has_premium_privilege
                          ? 'default'
                          : 'outline'
                      }
                      className={
                        stats.privileges.has_premium_privilege
                          ? 'bg-amber-500 text-white'
                          : ''
                      }
                    >
                      {stats.privileges.has_premium_privilege
                        ? 'Active'
                        : 'Inactive'}
                    </Badge>
                  </div>
                  <div className='text-center p-3 bg-background-tertiary rounded border'>
                    <div className='flex items-center justify-center mb-2'>
                      {
                        getEliteLevelInfo(
                          stats.privileges.elite_privilege_level
                        ).icon
                      }
                    </div>
                    <p className='text-xs font-medium mb-1'>Elite</p>
                    <Badge
                      variant={
                        getEliteLevelInfo(
                          stats.privileges.elite_privilege_level
                        ).variant
                      }
                      className={
                        getEliteLevelInfo(
                          stats.privileges.elite_privilege_level
                        ).className
                      }
                    >
                      {
                        getEliteLevelInfo(
                          stats.privileges.elite_privilege_level
                        ).text
                      }
                    </Badge>
                  </div>
                </div>

                {/* Privilege Benefits */}
                <div className='space-y-3 border-t border-border pt-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-foreground-muted'>
                      Max Fiat Fund
                    </span>
                    <span className='text-sm font-medium'>
                      {formatCurrencyAmount(
                        stats.privileges.maximum_fund_amount.fiat,
                        'fiat'
                      )}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-foreground-muted'>
                      Max Crypto Fund
                    </span>
                    <span className='text-sm font-medium'>
                      {formatCurrencyAmount(
                        stats.privileges.maximum_fund_amount.crypto,
                        'crypto'
                      )}
                    </span>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-foreground-muted'>
                      Maturity Period
                    </span>
                    <Badge variant='outline' className='text-xs'>
                      {stats.privileges.maturity_days} days
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Funds Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <Wallet className='h-5 w-5 text-primary' />
              Funds Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {/* Fund status breakdown */}
              <div className='grid grid-cols-3 gap-2'>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'active',
                      stats.funds.counts.statuses.active
                    )}
                    className='text-white font-bold'
                  >
                    {stats.funds.counts.statuses.active}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>Active</p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'completed',
                      stats.funds.counts.statuses.completed
                    )}
                    className='text-white font-bold'
                  >
                    {stats.funds.counts.statuses.completed}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Completed
                  </p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'cancelled',
                      stats.funds.counts.statuses.cancelled
                    )}
                    className='text-white font-bold'
                  >
                    {stats.funds.counts.statuses.cancelled}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Cancelled
                  </p>
                </div>
              </div>

              {/* Currency breakdown */}
              <div className='grid grid-cols-2 gap-2 border-t border-border pt-3'>
                <div className='text-center'>
                  <Badge
                    variant='outline'
                    className='text-foreground font-bold text-sm px-3 py-1'
                  >
                    {stats.funds.counts.currencies.fiat}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Naira Funds
                  </p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant='outline'
                    className='text-foreground font-bold text-sm px-3 py-1'
                  >
                    {stats.funds.counts.currencies.crypto}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    SOL Funds
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fund Amounts */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <BarChart3 className='h-5 w-5 text-primary' />
              Fund Amounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <h4 className='text-sm font-medium mb-1'>Naira Funds</h4>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Total Amount
                </span>
                <span className='text-sm font-medium'>
                  {formatCurrencyAmount(stats.funds.amounts.fiat.total, 'fiat')}
                </span>
              </div>
              <div className='flex justify-between items-center mb-4'>
                <span className='text-sm text-foreground-muted'>Growth</span>
                <span className='text-sm font-medium text-success'>
                  {formatCurrencyAmount(
                    stats.funds.amounts.fiat.growth,
                    'fiat'
                  )}
                </span>
              </div>

              <h4 className='text-sm font-medium mb-1 border-t border-border pt-3'>
                SOL Funds
              </h4>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Total Amount
                </span>
                <span className='text-sm font-medium'>
                  {formatCurrencyAmount(
                    stats.funds.amounts.crypto.total,
                    'crypto'
                  )}
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>Growth</span>
                <span className='text-sm font-medium text-success'>
                  {formatCurrencyAmount(
                    stats.funds.amounts.crypto.growth,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Withdraws Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <ArrowUpRight className='h-5 w-5 text-primary' />
              Withdraws Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {/* Status breakdown */}
              <div className='grid grid-cols-3 gap-2'>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'pending',
                      stats.withdraws.counts.statuses.pending
                    )}
                    className='text-white font-bold'
                  >
                    {stats.withdraws.counts.statuses.pending}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>Pending</p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'active',
                      stats.withdraws.counts.statuses.in_progress
                    )}
                    className='text-white font-bold'
                  >
                    {stats.withdraws.counts.statuses.in_progress}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    In Progress
                  </p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'completed',
                      stats.withdraws.counts.statuses.completed
                    )}
                    className='text-white font-bold'
                  >
                    {stats.withdraws.counts.statuses.completed}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Completed
                  </p>
                </div>
              </div>

              {/* Currency breakdown */}
              <div className='grid grid-cols-2 gap-2 border-t border-border pt-3'>
                <div className='text-center'>
                  <Badge
                    variant='outline'
                    className='text-foreground font-bold text-sm px-3 py-1'
                  >
                    {stats.withdraws.counts.currencies.fiat}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Naira Withdraws
                  </p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant='outline'
                    className='text-foreground font-bold text-sm px-3 py-1'
                  >
                    {stats.withdraws.counts.currencies.crypto}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    SOL Withdraws
                  </p>
                </div>
              </div>

              {/* Amounts */}
              <div className='space-y-2 border-t border-border pt-3'>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-foreground-muted'>
                    Naira Amount
                  </span>
                  <span className='text-sm font-medium'>
                    {formatCurrencyAmount(
                      stats.withdraws.amounts.fiat.total,
                      'fiat'
                    )}
                  </span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-sm text-foreground-muted'>
                    SOL Amount
                  </span>
                  <span className='text-sm font-medium'>
                    {formatCurrencyAmount(
                      stats.withdraws.amounts.crypto.total,
                      'crypto'
                    )}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <CreditCard className='h-5 w-5 text-primary' />
              Payment Methods
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              <div className='text-center mb-2'>
                <p className='text-2xl font-bold text-foreground'>
                  {stats.payment_methods.counts.total}
                </p>
                <p className='text-sm text-foreground-muted'>Total Methods</p>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Bank Accounts
                </span>
                <Badge variant='secondary' className='text-white font-bold'>
                  {stats.payment_methods.counts.types.bank_accounts}
                </Badge>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-foreground-muted'>
                  Crypto Wallets
                </span>
                <Badge variant='secondary' className='text-white font-bold'>
                  {stats.payment_methods.counts.types.crypto_wallets}
                </Badge>
              </div>
              <div className='pt-3 border-t border-border'>
                <Link to='/payment-methods'>
                  <Button
                    variant='outline'
                    size='sm'
                    className='w-full flex items-center gap-2'
                  >
                    <Eye className='h-3 w-3' />
                    View All Methods
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Referrals Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <Award className='h-5 w-5 text-primary' />
              Referral Program
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              <div className='text-center'>
                <p className='text-2xl font-bold text-foreground'>
                  {stats.referrals.total_referees}
                </p>
                <p className='text-sm text-foreground-muted'>Total Referees</p>
              </div>

              {/* Naira Bonuses */}
              <div className='border-t border-border pt-3'>
                <h4 className='text-sm font-medium mb-2'>Naira Bonuses</h4>
                <div className='space-y-1'>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Total Earned
                    </span>
                    <span className='text-sm font-medium'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.fiat.total,
                        'fiat'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Available
                    </span>
                    <span className='text-sm font-medium text-success'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.fiat.available,
                        'fiat'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Consumed
                    </span>
                    <span className='text-sm font-medium text-muted-foreground'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.fiat.consumed,
                        'fiat'
                      )}
                    </span>
                  </div>
                </div>
              </div>

              {/* SOL Bonuses */}
              <div className='border-t border-border pt-3'>
                <h4 className='text-sm font-medium mb-2'>SOL Bonuses</h4>
                <div className='space-y-1'>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Total Earned
                    </span>
                    <span className='text-sm font-medium'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.crypto.total,
                        'crypto'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Available
                    </span>
                    <span className='text-sm font-medium text-success'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.crypto.available,
                        'crypto'
                      )}
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-xs text-foreground-muted'>
                      Consumed
                    </span>
                    <span className='text-sm font-medium text-muted-foreground'>
                      {formatCurrencyAmount(
                        stats.referrals.amounts.crypto.consumed,
                        'crypto'
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Matches Card */}
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center gap-2 text-lg'>
              <GitBranch className='h-5 w-5 text-primary' />
              Payment Matches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <div className='text-center'>
                <p className='text-2xl font-bold text-foreground'>
                  {stats.payment_matches.counts.total}
                </p>
                <p className='text-sm text-foreground-muted'>Total Matches</p>
              </div>
              <div className='grid grid-cols-3 gap-2'>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'pending',
                      stats.payment_matches.counts.statuses.pending
                    )}
                    className='text-white font-bold'
                  >
                    {stats.payment_matches.counts.statuses.pending}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>Pending</p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'paid',
                      stats.payment_matches.counts.statuses.paid
                    )}
                    className='text-white font-bold'
                  >
                    {stats.payment_matches.counts.statuses.paid}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>Paid</p>
                </div>
                <div className='text-center'>
                  <Badge
                    variant={getStatusColor(
                      'confirmed',
                      stats.payment_matches.counts.statuses.confirmed
                    )}
                    className='text-white font-bold'
                  >
                    {stats.payment_matches.counts.statuses.confirmed}
                  </Badge>
                  <p className='text-xs text-foreground-muted mt-1'>
                    Confirmed
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
