import { apiClient } from './api';
import type {
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  User,
  AuthResponse,
} from '../types/auth';
import type { ApiResponse } from '../types/common';

export const authService = {
  /**
   * Login user
   */
  login: async (
    credentials: LoginRequest
  ): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post<AuthResponse>('/auth/login', credentials);
  },

  /**
   * Register new user
   */
  register: async (
    userData: RegisterRequest
  ): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post<AuthResponse>('/auth/register', userData);
  },

  /**
   * Logout current user
   */
  logout: async (): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/auth/logout');
  },

  /**
   * Get current user profile
   */
  me: async (): Promise<ApiResponse<User>> => {
    return apiClient.get<User>('/auth/me');
  },

  /**
   * Refresh access token
   */
  refreshToken: async (
    refreshToken: string
  ): Promise<ApiResponse<AuthResponse>> => {
    return apiClient.post<AuthResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });
  },

  /**
   * Send forgot password email
   */
  forgotPassword: async (
    data: ForgotPasswordRequest
  ): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/auth/forgot-password', data);
  },

  /**
   * Reset password with token
   */
  resetPassword: async (
    data: ResetPasswordRequest
  ): Promise<ApiResponse<null>> => {
    return apiClient.post<null>('/auth/reset-password', data);
  },
};
