@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.5rem;
}

* {
  border-color: theme('colors.border');
}

body {
  @apply bg-background text-foreground;
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
  margin: 0;
  min-height: 100vh;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-background-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-brand-grey-300 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-brand-grey-400;
}

/* Form focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Button variants */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 focus-ring;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 focus-ring;
}

.btn-ghost {
  @apply hover:bg-accent hover:text-accent-foreground focus-ring;
}

/* Input styles */
.input-field {
  @apply flex h-10 w-full rounded-md border border-input bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}
