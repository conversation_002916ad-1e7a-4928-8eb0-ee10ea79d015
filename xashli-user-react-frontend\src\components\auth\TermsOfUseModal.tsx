import { Modal } from '@/components/ui/Modal';
import { But<PERSON> } from '@/components/ui/Button';
import { ScrollText, Check } from 'lucide-react';

interface TermsOfUseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept?: () => void; // Optional callback when user accepts
}

export function TermsOfUseModal({
  isOpen,
  onClose,
  onAccept,
}: TermsOfUseModalProps) {
  const handleAcceptAndContinue = () => {
    onAccept?.();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title='Terms of Use'
      size='xl'
      className='max-h-[90vh]'
    >
      <div className='flex flex-col h-full max-h-[calc(90vh-8rem)]'>
        {/* Scrollable Content */}
        <div className='flex-1 overflow-y-auto pr-2 space-y-6 text-foreground-secondary'>
          {/* Introduction */}
          <div className='space-y-3'>
            <p>
              Welcome to Xashli. These Terms of Use govern your access to and
              use of the Xashli platform. By signing up or using the platform,
              you agree to these terms.
            </p>
          </div>

          {/* Eligibility */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Eligibility
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>Users must be 18 years or older.</li>
              <li>You must provide accurate and verifiable information.</li>
            </ul>
          </section>

          {/* Platform Usage */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Platform Usage
            </h3>
            <p>
              Xashli allows users to stake funds and earn returns based on
              predefined cycles.
            </p>

            <div className='bg-background-secondary p-4 rounded-lg space-y-3'>
              <h4 className='font-semibold text-foreground'>
                User Tiers & Limits:
              </h4>
              <ul className='space-y-2 text-sm'>
                <li>
                  <strong>Standard users:</strong> Earn 50% in naira (funding
                  limit: ₦10k - ₦100k) or 55% in SOL (funding limit: 0.1 SOL -
                  10 SOL) in 10 days.
                </li>
                <li>
                  <strong>Premium users:</strong> Earn 50% in naira or 55% in
                  SOL in 7 days (requires being a Standard user or Elite User).
                </li>
                <li>
                  <strong>Elite Users 1:</strong> Fund up to ₦200,000 in Naira
                  or 20 SOL maximum limit.
                </li>
                <li>
                  <strong>Elite Users 2:</strong> Fund up to ₦300,000 in Naira
                  or 30 SOL maximum limit.
                </li>
                <li>
                  <strong>Elite Users 3:</strong> Fund up to ₦400,000 in Naira
                  or 40 SOL maximum limit.
                </li>
                <li>
                  <strong>Elite Users 4:</strong> Fund up to ₦500,000 in Naira
                  or 50 SOL maximum limit.
                </li>
              </ul>
            </div>

            <p>To withdraw, users must recommit by staking again.</p>
          </section>

          {/* Referral Program */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Referral Program
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>Users earn 10% direct commission on referred users.</li>
              <li>
                Referral bonuses are added to the user's wallet and are subject
                to withdrawal rules.
              </li>
            </ul>
          </section>

          {/* Premium User Status */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Premium User Status
            </h3>
            <p>
              Pro status is unlocked upon reaching ₦5,000,000 or 20 SOL in
              direct downline staking volume.
            </p>
          </section>

          {/* Platform Fees */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Platform Fees
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>
                To maintain a smooth, secure, and high-performing ecosystem,
                Xashli may apply a minimal service fee on staking transactions.
              </li>
              <li>This fee supports operational costs and sustainability.</li>
            </ul>
          </section>

          {/* Refund Policy */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Refund Policy
            </h3>
            <div className='space-y-3'>
              <p>
                At Xashli, our strength lies in the trust and collective support
                of our community. Every stake made by a user supports someone
                else in the cycle, that's the heartbeat of our ecosystem.
              </p>
              <p>
                Because of this real-time matching structure, all staking
                commitments are considered final once confirmed. We do not offer
                automatic refunds on fund commitments, as these are already in
                use sustaining the cycle and supporting others in the network.
                This model keeps the system alive, fair, and continuously
                rewarding for everyone involved.
              </p>
              <p>
                That said, we understand that life happens, and sometimes,
                circumstances shift in ways no one can predict. If you find
                yourself in an urgent or distressing situation, please reach out
                to our support team. Every request is reviewed on a case-by-case
                basis with compassion and sincerity. While we can't promise
                outcomes, we do promise understanding.
              </p>
              <p>
                We strongly encourage all users to only stake what they're
                comfortable with and to approach participation with full
                awareness of how the system works.
              </p>
              <p className='font-medium text-foreground'>
                Together, we grow a stronger financial alternative, one rooted
                in transparency, empathy, and community strength.
              </p>
            </div>
          </section>

          {/* User Responsibilities */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              User Responsibilities
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>Maintain the confidentiality of your login credentials.</li>
              <li>
                Use the platform ethically and avoid abuse or manipulation.
              </li>
            </ul>
          </section>

          {/* Limitation of Liability */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Limitation of Liability
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>
                Xashli is not a licensed financial institution. Earnings depend
                on community participation.
              </li>
              <li>
                We are not liable for losses due to user error, delays, or
                third-party service failures.
              </li>
            </ul>
          </section>

          {/* Account Suspension */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Account Suspension
            </h3>
            <p>
              Accounts may be suspended for suspicious activity or violations of
              these terms.
            </p>
          </section>

          {/* Dispute Resolution */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Dispute Resolution
            </h3>
            <p>
              Disputes between users will be resolved by the Xashli dispute and
              support team.
            </p>
          </section>

          {/* Contact */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Contact
            </h3>
            <p>For inquiries, email: <EMAIL></p>
          </section>

          {/* Disclaimer */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <ScrollText className='h-5 w-5 text-primary' />
              Disclaimer
            </h3>
            <div className='bg-warning/10 border border-warning/20 p-4 rounded-lg space-y-3'>
              <p>
                Xashli is built on the strength and trust of a growing
                community. While we aim to provide a rewarding experience
                through our staking model, we do not offer financial guarantees
                or investment promises.
              </p>
              <p>
                Your participation is entirely voluntary, and like any
                decentralized financial ecosystem, it comes with some level of
                risk. We encourage you to only commit what you can comfortably
                stake.
              </p>
              <p>
                The Xashli team is dedicated to transparency and continuous
                improvement, but we reserve the right to make changes to the
                platform, including service updates or pauses, as needed to
                protect the community and ensure long-term sustainability.
              </p>
            </div>
          </section>

          {/* Final Agreement */}
          <div className='border-t border-border pt-4'>
            <p className='text-center font-medium text-foreground'>
              By using Xashli, you confirm that you understand and accept these
              policies and disclaimers.
            </p>
          </div>
        </div>{' '}
        {/* Sticky Footer */}
        <div className='flex flex-row gap-3 pt-6 mt-6 border-t border-border'>
          <Button variant='outline' onClick={onClose}>
            Close
          </Button>
          {onAccept && (
            <Button onClick={handleAcceptAndContinue}>
              <Check className='h-4 w-4 mr-2' />
              Accept & Continue
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
