import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  AlertTriangle,
  Search,
  Filter,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { MainLayout } from '../../components/layout';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { LoadingSpinner } from '../../components/ui/loading';
import { Pagination } from '../../components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { useDisputes, useDisputeStats } from '../../hooks/useDisputes';
import {
  formatDateTime,
  formatCurrencyAmount,
  shortenId,
} from '../../utils/format';
import type {
  PaymentDispute,
  DisputeFilters,
  DisputeStatus,
} from '../../types/dispute';

// Statistics Cards Component
const DisputeStatsCards: React.FC = () => {
  const { data: stats, isLoading } = useDisputeStats();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <LoadingSpinner size="sm" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Provide default values even if no stats are returned
  const counts = stats?.data?.counts || {
    total_count: 0,
    statuses: {
      under_review: 0,
      resolved: 0,
      rejected: 0,
    },
    resolutions: {
      confirmed: 0,
      cancelled: 0,
    },
  };

  const statCards = [
    {
      title: 'Total Disputes',
      value: counts.total_count,
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
    {
      title: 'Under Review',
      value: counts.statuses.under_review,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Resolved',
      value: counts.statuses.resolved,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Rejected',
      value: counts.statuses.rejected,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((card, index) => {
        const IconComponent = card.icon;
        return (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <IconComponent className={`h-5 w-5 ${card.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {card.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {card.value}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

// Get status badge variant
const getStatusBadgeVariant = (status: DisputeStatus) => {
  switch (status) {
    case 'under_review':
      return {
        variant: 'secondary' as const,
        className: 'bg-yellow-100 text-yellow-800',
      };
    case 'resolved':
      return {
        variant: 'default' as const,
        className: 'bg-green-100 text-green-800',
      };
    case 'rejected':
      return {
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800',
      };
    default:
      return { variant: 'secondary' as const, className: '' };
  }
};

interface DisputesTableProps {
  disputes: PaymentDispute[];
  isLoading: boolean;
  onViewDetails: (dispute: PaymentDispute) => void;
  currentPage: number;
  pageSize: number;
}

const DisputesTable: React.FC<DisputesTableProps> = ({
  disputes,
  isLoading,
  onViewDetails,
  currentPage,
  pageSize,
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (disputes.length === 0) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No disputes found
        </h3>
        <p className="text-gray-500">
          No payment disputes match your current filters.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>SN</TableHead>
            <TableHead>Dispute ID</TableHead>
            <TableHead>Payment Match</TableHead>
            <TableHead>Dispute User</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Disputed At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {disputes.map((dispute, index) => {
            const statusBadge = getStatusBadgeVariant(dispute.status);
            return (
              <TableRow key={dispute.id}>
                <TableCell>
                  <span className="text-sm font-medium text-gray-900">
                    {(currentPage - 1) * pageSize + index + 1}
                  </span>
                </TableCell>
                <TableCell>
                  <div
                    className="text-sm font-medium text-gray-900"
                    title={dispute.id}
                  >
                    #{shortenId(dispute.id)}
                  </div>
                </TableCell>
                <TableCell>
                  <div
                    className="text-sm text-gray-900"
                    title={dispute.payment_match_id}
                  >
                    #{shortenId(dispute.payment_match_id)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {dispute.payment_match?.fund?.currency || 'N/A'}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm font-medium text-gray-900">
                    {dispute.dispute_user?.full_name || 'Unknown'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {dispute.dispute_user?.email || 'N/A'}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrencyAmount(
                      parseFloat(dispute.payment_match?.amount || '0'),
                      dispute.payment_match?.fund?.currency || 'fiat'
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={statusBadge.variant}
                    className={statusBadge.className}
                  >
                    {dispute.status.replace('_', ' ')}
                  </Badge>
                  {dispute.resolution && (
                    <div className="text-xs text-gray-500 mt-1">
                      {dispute.resolution.replace('_', ' ')}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-500">
                    {formatDateTime(dispute.disputed_at)}
                    {dispute.resolved_at && (
                      <div className="text-xs text-gray-400">
                        Resolved: {formatDateTime(dispute.resolved_at)}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center justify-end gap-2">
                    <button
                      onClick={() => onViewDetails(dispute)}
                      className="text-brand-gold-600 hover:text-brand-gold-900"
                      title="View Details"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export const DisputesPage: React.FC = () => {
  const navigate = useNavigate();

  const [filters, setFilters] = useState<DisputeFilters>({
    page: 1,
    per_page: 10,
    sort_field: 'disputed_at',
    sort_direction: 'desc',
  });
  const [pendingFilters, setPendingFilters] = useState<DisputeFilters>({
    page: 1,
    per_page: 10,
    sort_field: 'disputed_at',
    sort_direction: 'desc',
  });
  const [showFilters, setShowFilters] = useState(false);

  // Initialize pending filters with current filters
  React.useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Fetch disputes
  const { data: disputesResponse, isLoading } = useDisputes(filters);
  const disputes = disputesResponse?.data?.disputes || [];
  const pagination = disputesResponse?.data?.pagination || null;

  // Handle filter changes
  const handleFilterChange = (key: keyof DisputeFilters, value: any) => {
    setPendingFilters(prev => ({ ...prev, [key]: value }));
  };

  const applyFilters = () => {
    setFilters({
      ...pendingFilters,
      page: 1, // Reset to first page when filters change
    });
  };

  const clearFilters = () => {
    const clearedFilters = {
      page: 1,
      per_page: 10,
      sort_field: 'disputed_at',
      sort_direction: 'desc',
    } as DisputeFilters;
    setFilters(clearedFilters);
    setPendingFilters(clearedFilters);
  };

  const hasFilterChanges = () => {
    const { page, per_page, ...currentFiltersWithoutPagination } = filters;
    const {
      page: pendingPage,
      per_page: pendingPerPage,
      ...pendingFiltersWithoutPagination
    } = pendingFilters;
    return (
      JSON.stringify(currentFiltersWithoutPagination) !==
      JSON.stringify(pendingFiltersWithoutPagination)
    );
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, per_page: pageSize, page: 1 }));
  };

  const handleViewDetails = (dispute: PaymentDispute) => {
    navigate(`/disputes/${dispute.id}`);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Payment Disputes
            </h1>
            <p className="text-gray-600 mt-1">
              Manage and resolve payment disputes
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>
        {/* Statistics Cards */}
        <DisputeStatsCards />
        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <span>Filters</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Search
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search disputes..."
                      value={pendingFilters.search || ''}
                      onChange={e =>
                        handleFilterChange('search', e.target.value)
                      }
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Status
                  </label>
                  <Select
                    value={pendingFilters.status || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'status',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All statuses</SelectItem>
                      <SelectItem value="under_review">Under Review</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Resolution
                  </label>
                  <Select
                    value={pendingFilters.resolution || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'resolution',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All resolutions" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All resolutions</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Sort By
                  </label>
                  <Select
                    value={pendingFilters.sort_field || 'disputed_at'}
                    onValueChange={value =>
                      handleFilterChange('sort_field', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="disputed_at">Date Disputed</SelectItem>
                      <SelectItem value="resolved_at">Date Resolved</SelectItem>
                      <SelectItem value="amount">Amount</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Buttons container - separate div below input fields */}
              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters {hasFilterChanges() && '(*)'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Disputes Table */}
        <Card>
          <CardHeader>
            <CardTitle>Disputes List</CardTitle>
            <CardDescription>View and manage payment disputes</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <DisputesTable
              disputes={disputes}
              isLoading={isLoading}
              onViewDetails={handleViewDetails}
              currentPage={filters.page || 1}
              pageSize={filters.per_page || 10}
            />
            <Pagination
              pagination={pagination}
              currentPage={filters.page || 1}
              pageSize={filters.per_page || 10}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              isLoading={isLoading}
              itemLabel="Disputes"
            />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default DisputesPage;
