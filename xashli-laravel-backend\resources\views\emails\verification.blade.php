<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Verify Your Email - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2d3748;
        }
        .verification-code {
            background: #f7fafc;
            border: 2px dashed #4299e1;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            margin: 30px 0;
        }
        .verification-code .label {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .verification-code .code {
            font-size: 32px;
            font-weight: bold;
            color: #2b6cb0;
            font-family: 'Courier New', monospace;
            letter-spacing: 4px;
        }
        .expiry-notice {
            background: #fff5f5;
            border-left: 4px solid #fc8181;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .expiry-notice .icon {
            color: #e53e3e;
            font-weight: bold;
        }
        .footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            font-size: 14px;
            color: #718096;
            border-top: 1px solid #e2e8f0;
        }
        .security-note {
            background: #f0fff4;
            border-left: 4px solid #68d391;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .security-note .icon {
            color: #38a169;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 4px;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .verification-code .code {
                font-size: 28px;
                letter-spacing: 2px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p>Email Verification</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hello {{ $user->full_name }}! 👋
            </div>
            
            <p>Thank you for registering with {{ config('app.name') }}. To complete your account verification, please use the verification code below:</p>
            
            <div class="verification-code">
                <div class="label">Your Verification Code</div>
                <div class="code">{{ $verificationCode }}</div>
            </div>
              <div class="expiry-notice">
                <span class="icon">⏰</span>
                <strong>Important:</strong> This code will expire in <strong>1 hour</strong> for security reasons.
            </div>
            
            <div class="security-note">
                <span class="icon">🔒</span>
                <strong>Security Notice:</strong> For your protection, do not share this code with anyone. {{ config('app.name') }} will never ask for your verification code via phone or email.
            </div>
            
            <p>If you did not create an account with {{ config('app.name') }}, please ignore this email and no action is required.</p>
        </div>
        
        <div class="footer">
            <p><strong>Best regards,</strong><br>
            The {{ config('app.name') }} Team</p>
            
            <p style="margin-top: 20px; font-size: 12px;">
                This is an automated message. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>
</html>
