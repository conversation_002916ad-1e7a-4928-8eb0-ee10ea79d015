import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { authService } from '../../services';
import { showToast } from '../../utils/toast';

interface ResetPasswordData {
  password: string;
  confirmPassword: string;
}

export const useResetPassword = () => {
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const token = searchParams.get('token');

  const isValidToken = !!token;

  const handleResetPassword = async (data: ResetPasswordData) => {
    if (!token) {
      showToast.error(
        'Invalid reset link. Please request a new password reset.'
      );
      return;
    }

    setIsLoading(true);

    try {
      const response = await authService.resetPassword({
        token,
        password: data.password,
        password_confirmation: data.confirmPassword,
      });

      if (response.status === 'success') {
        setIsSuccess(true);
        showToast.success('Password reset successful!');
      } else {
        showToast.error(
          response.message || 'Failed to reset password. Please try again.'
        );
      }
    } catch (error: any) {
      showToast.error(
        error.message ||
          'Network error. Please check your connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    isSuccess,
    isValidToken,
    token,
    handleResetPassword,
  };
};
