import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
  isOpen?: boolean;
  onToggle?: () => void;
}

interface AccordionProps {
  children: React.ReactNode;
  allowMultiple?: boolean;
}

export function AccordionItem({
  title,
  children,
  isOpen = false,
  onToggle,
}: AccordionItemProps) {
  return (
    <div className='border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200'>
      <button
        className='w-full px-6 py-4 text-left bg-background hover:bg-muted/30 transition-all duration-200 flex items-center justify-between group'
        onClick={onToggle}
      >
        <h3 className='text-lg font-semibold text-foreground group-hover:text-primary transition-colors duration-200'>
          {title}
        </h3>
        <ChevronDown
          className={`h-5 w-5 text-foreground-secondary transition-all duration-300 ease-out group-hover:text-primary ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>{' '}
      <div
        className={`overflow-hidden transition-all duration-300 ease-out ${
          isOpen ? 'max-h-none opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className='px-6 py-4 bg-background border-t border-border'>
          {children}
        </div>
      </div>
    </div>
  );
}

export function Accordion({ children, allowMultiple = false }: AccordionProps) {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    if (allowMultiple) {
      setOpenItems(prev =>
        prev.includes(index) ? prev.filter(i => i !== index) : [...prev, index]
      );
    } else {
      setOpenItems(prev => (prev.includes(index) ? [] : [index]));
    }
  };

  return (
    <div className='space-y-4'>
      {Array.isArray(children)
        ? children.map((child, index) => {
            if (child?.type === AccordionItem) {
              return {
                ...child,
                key: index,
                props: {
                  ...child.props,
                  isOpen: openItems.includes(index),
                  onToggle: () => toggleItem(index),
                },
              };
            }
            return child;
          })
        : children}
    </div>
  );
}
