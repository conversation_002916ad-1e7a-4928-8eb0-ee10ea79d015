import { useState, useEffect } from 'react';
import { AlertTriangle, Mail, Clock } from 'lucide-react';
import { Alert, AlertDescription } from './Alert';
import moment from 'moment';
import type { User } from '../../types/auth';

interface DeactivatedUserAlertProps {
  user: User;
  className?: string;
  customMessage?: string;
}

export function DeactivatedUserAlert({
  user,
  className = '',
  customMessage,
}: DeactivatedUserAlertProps) {
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [isTemporary, setIsTemporary] = useState(false);

  // Calculate remaining time using moment.js
  const calculateRemainingTime = () => {
    if (!user.deactivated_at || !user.deactivation_duration) {
      setIsTemporary(false);
      setTimeRemaining('');
      return;
    }

    const deactivatedAt = moment(user.deactivated_at);
    const reactivationTime = deactivatedAt
      .clone()
      .add(user.deactivation_duration, 'minutes');
    const now = moment();

    if (now.isSameOrAfter(reactivationTime)) {
      setIsTemporary(false);
      setTimeRemaining('');
      return;
    }

    setIsTemporary(true);

    // Calculate duration using moment.js
    const duration = moment.duration(reactivationTime.diff(now));
    const days = Math.floor(duration.asDays());
    const hours = duration.hours();
    const minutes = duration.minutes();

    // Format the time remaining
    const parts = [];
    if (days > 0) parts.push(`${days} day${days > 1 ? 's' : ''}`);
    if (hours > 0) parts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
    if (minutes > 0) parts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);

    if (parts.length === 0) {
      setTimeRemaining('less than a minute');
    } else if (parts.length === 1) {
      setTimeRemaining(parts[0]);
    } else if (parts.length === 2) {
      setTimeRemaining(`${parts[0]} and ${parts[1]}`);
    } else {
      setTimeRemaining(`${parts[0]}, ${parts[1]}, and ${parts[2]}`);
    }
  };

  // Update countdown every minute
  useEffect(() => {
    calculateRemainingTime(); // Initial calculation
    const interval = setInterval(calculateRemainingTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [user.deactivated_at, user.deactivation_duration]);

  const defaultMessage = isTemporary
    ? 'Your account has been temporarily deactivated. You cannot create new funds or withdrawal requests while your account is inactive.'
    : 'Your account has been deactivated. You cannot create new funds or withdrawal requests while your account is inactive.';

  return (
    <Alert
      variant='destructive'
      className={`border-destructive/50 ${className}`}
    >
      <AlertTriangle className='h-4 w-4' />
      <AlertDescription className='space-y-3'>
        <div className='font-medium'>
          {isTemporary
            ? 'Account Temporarily Deactivated'
            : 'Account Deactivated'}
        </div>
        <div className='text-sm'>{customMessage || defaultMessage}</div>

        {/* Countdown Timer */}
        {isTemporary && timeRemaining && (
          <div className='bg-destructive/10 rounded-lg p-3 border border-destructive/20'>
            <div className='flex items-center gap-2 text-sm font-medium text-destructive mb-1'>
              <Clock className='h-4 w-4' />
              <span>Automatic Reactivation</span>
            </div>
            <div className='text-sm text-destructive/80'>
              Your account will be automatically reactivated in{' '}
              <span className='font-medium'>{timeRemaining}</span>
            </div>
          </div>
        )}

        {/* Contact Support */}
        <div className='text-sm'>
          <div className='font-medium mb-1'>
            {isTemporary ? 'Need immediate help?' : 'Need help?'} Contact{' '}
            <a
              href='mailto:<EMAIL>?subject=Account Deactivation Support'
              className='inline-flex items-center gap-1 text-destructive hover:text-destructive/80 underline'
            >
              <Mail className='h-3 w-3' />
              support
            </a>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}
