<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class JsonApiMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only process API routes
        if (! $request->is('api/*')) {
            return $next($request);
        }

        // Don't force JSON for multipart/form-data or if files are present
        $contentType = $request->header('Content-Type', '');
        $hasFiles = $request->hasFile('*') || str_contains($contentType, 'multipart/form-data');

        if (! $hasFiles) {
            $request->headers->set('Accept', 'application/json');
        }

        return $next($request);
    }
}
