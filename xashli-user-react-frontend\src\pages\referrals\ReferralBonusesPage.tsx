import { useState, useEffect } from 'react';
import { TrendingUp, RefreshCw } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ReferralBonuses } from '../../components/referrals';
import { useReferrals } from '../../hooks/referrals';
import type { ReferralBonusesFilters } from '../../types/referral';

export function ReferralBonusesPage() {
  const { bonuses, loadingBonuses, fetchBonuses } = useReferrals();

  const [filters, setFilters] = useState<ReferralBonusesFilters>({});

  useEffect(() => {
    fetchBonuses();
  }, [fetchBonuses]);

  const handleFilterChange = (newFilters: ReferralBonusesFilters) => {
    setFilters(newFilters);
    fetchBonuses(newFilters);
  };

  const handleRefresh = () => {
    fetchBonuses(filters);
  };

  if (loadingBonuses && !bonuses) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Referral Bonuses
          </h1>
          <p className='text-foreground-secondary mt-2'>
            Track your referral rewards and earnings
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={handleRefresh}
            disabled={loadingBonuses}
            className='flex items-center gap-2'
          >
            <RefreshCw
              className={`h-4 w-4 ${loadingBonuses ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Content */}
      {bonuses ? (
        <ReferralBonuses
          bonuses={bonuses}
          onFilterChange={handleFilterChange}
        />
      ) : (
        <div className='text-center py-8'>
          <TrendingUp className='h-12 w-12 text-foreground-muted mx-auto mb-4' />
          <h3 className='text-lg font-medium text-foreground mb-2'>
            Unable to Load Bonuses
          </h3>
          <p className='text-foreground-secondary'>
            Please try refreshing the page.
          </p>
        </div>
      )}
    </div>
  );
}
