import { useMemo } from 'react';

interface PasswordRequirement {
  label: string;
  met: boolean;
}

export const usePasswordRequirements = (password: string) => {
  const passwordRequirements: PasswordRequirement[] = useMemo(
    () => [
      { label: 'At least 8 characters', met: password.length >= 8 },
      { label: 'Contains uppercase letter', met: /[A-Z]/.test(password) },
      { label: 'Contains lowercase letter', met: /[a-z]/.test(password) },
      { label: 'Contains number', met: /\d/.test(password) },
    ],
    [password]
  );

  const allRequirementsMet = passwordRequirements.every(req => req.met);

  return {
    passwordRequirements,
    allRequirementsMet,
  };
};
