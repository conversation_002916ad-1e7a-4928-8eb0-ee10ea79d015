<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlatformFee extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'platform_fees';

    protected $fillable = [
        'fund_id',
        'admin_user_id',
        'fee_withdraw_id',
        'fund_amount',
        'fee_amount',
        'fee_percentage',
        'currency',
        'collected_at',
        'metadata',
    ];

    protected $casts = [
        'fund_amount' => 'decimal:8',
        'fee_amount' => 'decimal:8',
        'fee_percentage' => 'decimal:2',
        'collected_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the fund associated with this platform fee.
     */
    public function fund(): BelongsTo
    {
        return $this->belongsTo(Fund::class);
    }

    /**
     * Get the admin user who collected the fee.
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_user_id');
    }

    /**
     * Get the withdraw record for the platform fee.
     */
    public function withdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class, 'fee_withdraw_id');
    }

    /**
     * Scope to filter by currency type.
     */
    public function scopeCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('collected_at', [$startDate, $endDate]);
    }

    /**
     * Get total fees collected for a specific currency.
     */
    public static function getTotalFeesCollected(?string $currency = null)
    {
        $query = static::query();

        if ($currency) {
            $query->where('currency', $currency);
        }

        return $query->sum('fee_amount');
    }

    /**
     * Get fees collected statistics.
     */
    public static function getStats()
    {
        return [
            'total_fiat_fees' => static::getTotalFeesCollected('fiat'),            'total_crypto_fees' => static::getTotalFeesCollected('crypto'),
            'total_fees' => static::count(),
            'avg_fee_amount' => static::avg('fee_amount'),
            'latest_fee' => static::latest('collected_at')->first()?->collected_at,
        ];
    }
}
