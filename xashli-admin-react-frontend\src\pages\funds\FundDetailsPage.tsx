import React from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Label } from '../../components/ui/label';
import { Badge } from '../../components/ui/badge';
import {
  Calendar,
  User,
  CreditCard,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Hash,
  ArrowLeft,
  Eye,
} from 'lucide-react';
import { fundService } from '../../services/fund';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { MainLayout } from '../../components/layout/MainLayout';
import { LoadingSpinner } from '../../components/ui/loading';
import { formatCurrencyAmount } from '../../utils/format';

export const FundDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Fetch fund details
  const {
    data: fundResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['fund', id],
    queryFn: () => fundService.getFund(id!),
    enabled: !!id,
  });

  const fund = fundResponse?.data;
  // Cancel fund mutation
  const cancelMutation = useMutation({
    mutationFn: () => fundService.cancelFund(id!),
    onSuccess: () => {
      toast.success('Fund cancelled successfully');
      refetch();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Failed to cancel fund');
    },
  });

  const handleCancel = () => {
    cancelMutation.mutate();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
      matched: { color: 'bg-blue-100 text-blue-800', icon: TrendingUp },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (error || !fund) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Fund Not Found
            </h2>
            <p className="text-gray-600 mb-4">
              The fund you're looking for doesn't exist or you don't have
              permission to view it.
            </p>
            <Button onClick={() => navigate('/transactions/funds')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Funds
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate('/transactions/funds')}
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Fund Details</h1>
              <p className="text-gray-600 mt-1">Fund ID: #{fund.id}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(fund.status)}
            <Link to={`/transactions/payment-matches?fund_id=${fund.id}`}>
              <Button
                variant="outline"
                size="sm"
                className="bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 hover:text-blue-800"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Payment Matches
              </Button>
            </Link>
          </div>
        </div>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Fund Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Fund Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Amount</Label>
                  <p className="text-lg font-semibold">
                    {formatCurrencyAmount(fund.amount, fund.currency)}
                  </p>
                </div>{' '}
                <div>
                  <Label className="text-sm font-medium">Currency</Label>
                  <p className="text-lg">{fund.currency.toUpperCase()}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">
                    Growth Percentage
                  </Label>
                  <p className="text-lg text-green-600">
                    {fund.growth_percentage}%
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Growth Amount</Label>
                  <p className="text-lg text-green-600">
                    {formatCurrencyAmount(fund.growth_amount, fund.currency)}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Platform Fee</Label>
                  <p className="text-lg">{fund.platform_fee_percentage}%</p>
                </div>{' '}
                <div>
                  <Label className="text-sm font-medium">
                    Platform Fee Amount
                  </Label>
                  <p className="text-lg">
                    {formatCurrencyAmount(
                      fund.amount * (fund.platform_fee_percentage / 100),
                      fund.currency
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Timeline Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Start Date</Label>
                <p className="text-lg">
                  {fund.start_date
                    ? format(new Date(fund.start_date), 'PPP')
                    : 'TBD (set when payments complete)'}
                </p>
              </div>{' '}
              <div>
                <Label className="text-sm font-medium">Maturity Date</Label>
                <p className="text-lg">
                  {fund.maturity_date
                    ? format(new Date(fund.maturity_date), 'PPP')
                    : 'TBD (set when payments complete)'}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created At</Label>
                <p className="text-lg">
                  {format(new Date(fund.created_at), 'PPP')}
                </p>
              </div>
            </CardContent>
          </Card>
          {/* User Information */}
          {fund.user && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {' '}
                <div>
                  <Label className="text-sm font-medium">Name</Label>
                  <p className="text-lg">{fund.user.full_name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-lg">{fund.user.email}</p>
                </div>
              </CardContent>
            </Card>
          )}{' '}
          {/* Payment Method */}
          {fund.paymentMethod && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Type</Label>
                  <p className="text-lg capitalize">
                    {fund.paymentMethod.type}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Label</Label>
                  <p className="text-lg">{fund.paymentMethod.label}</p>
                </div>
                {fund.paymentMethod.type === 'bank' && (
                  <>
                    <div>
                      <Label className="text-sm font-medium">Bank Name</Label>
                      <p className="text-lg">{fund.paymentMethod.bank_name}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Account Number
                      </Label>
                      <p className="text-lg">
                        {fund.paymentMethod.account_number}
                      </p>
                    </div>
                  </>
                )}
                {fund.paymentMethod.type === 'crypto' && (
                  <>
                    <div>
                      <Label className="text-sm font-medium">
                        Wallet Address
                      </Label>
                      <p className="text-lg font-mono text-sm">
                        {fund.paymentMethod.wallet_address}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Network</Label>
                      <p className="text-lg">
                        {fund.paymentMethod.crypto_network}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </div>
        {/* Transaction Hash Display */}
        {fund.transaction_hash && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hash className="h-5 w-5" />
                Transaction Hash
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="font-mono text-sm bg-gray-100 p-2 rounded break-all">
                {fund.transaction_hash}
              </p>
            </CardContent>
          </Card>
        )}
        {/* Fund Actions Section */}
        {fund.status === 'pending' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Fund Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="destructive"
                  onClick={handleCancel}
                  disabled={cancelMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  {cancelMutation.isPending ? 'Cancelling...' : 'Cancel Fund'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
};
