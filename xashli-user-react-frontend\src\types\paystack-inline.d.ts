declare module '@paystack/inline-js' {
  export interface PaystackOptions {
    key: string;
    email: string;
    amount: number;
    currency?: string;
    ref?: string;
    metadata?: Record<string, any>;
    label?: string;
    onSuccess?: (transaction: any) => void;
    onCancel?: () => void;
    onClose?: () => void;
  }

  export interface PaystackTransaction {
    reference: string;
    status: string;
    trans: string;
    transaction: string;
    trxref: string;
    redirecturl?: string;
  }

  export interface PaystackInstance {
    newTransaction: (options: PaystackOptions) => void;
  }

  class PaystackPop implements PaystackInstance {
    newTransaction: (options: PaystackOptions) => void;
  }

  export default PaystackPop;
}
