import { useState } from 'react';
import { showToast } from './toast';

export const copyToClipboard = async (
  text: string,
  successMessage?: string,
  errorMessage?: string
): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    showToast.success(successMessage || 'Copied to clipboard!');
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    showToast.error(errorMessage || 'Failed to copy to clipboard');
    return false;
  }
};

export const useCopyToClipboard = (resetDelay: number = 2000) => {
  const [copied, setCopied] = useState(false);

  const copy = async (
    text: string,
    successMessage?: string,
    errorMessage?: string
  ) => {
    const success = await copyToClipboard(text, successMessage, errorMessage);

    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), resetDelay);
    }

    return success;
  };

  return { copied, copy };
};
