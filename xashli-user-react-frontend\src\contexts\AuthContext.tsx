import { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { User } from '@/types/auth';
import { authService } from '@/services';
import { tokenStorage } from '@/services/api';
import { showToast } from '@/utils/toast';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refresh: () => Promise<void>;
  updateUser: (updatedUser: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && tokenStorage.getAccessToken() !== null;

  // Check authentication status on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = tokenStorage.getAccessToken();
      if (!token) {
        setIsLoading(false);
        return;
      }

      const response = await authService.me();
      if (response.status === 'success' && response.data) {
        setUser(response.data);
      } else {
        // Invalid token, clear it
        tokenStorage.clearTokens();
        setUser(null);
      }
    } catch (error) {
      // Error fetching user, clear token
      tokenStorage.clearTokens();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authService.login({ email, password });

      if (response.status === 'success' && response.data) {
        const { user: userData, authorization } = response.data;

        // Store tokens
        tokenStorage.setAccessToken(authorization.access_token);
        tokenStorage.setRefreshToken(authorization.refresh_token);

        setUser(userData);
        showToast.auth.loginSuccess(userData.full_name || userData.email);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      showToast.auth.logoutSuccess();
    } catch (error) {
      // Even if logout fails on server, clear local state
      console.error('Logout error:', error);
      showToast.auth.logoutSuccess();
    } finally {
      tokenStorage.clearTokens();
      setUser(null);
      setIsLoading(false);
    }
  };

  const refresh = async () => {
    try {
      const response = await authService.me();
      if (response.status === 'success' && response.data) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Refresh error:', error);
    }
  };

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refresh,
    updateUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
