import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';
import Cookies from 'js-cookie';
import type { ApiResponse, ApiError } from '../types/common';

// Create axios instance with base configuration
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// Token management
const ACCESS_TOKEN_KEY = 'xashli_user_access_token';
const REFRESH_TOKEN_KEY = 'xashli_user_refresh_token';

export const tokenStorage = {
  getAccessToken: (): string | null => {
    return (
      Cookies.get(ACCESS_TOKEN_KEY) || localStorage.getItem('token') || null
    );
  },

  setAccessToken: (token: string): void => {
    Cookies.set(ACCESS_TOKEN_KEY, token, {
      expires: 7, // 7 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });
    localStorage.setItem('token', token); // Fallback for verification page
  },

  getRefreshToken: (): string | null => {
    return Cookies.get(REFRESH_TOKEN_KEY) || null;
  },

  setRefreshToken: (token: string): void => {
    Cookies.set(REFRESH_TOKEN_KEY, token, {
      expires: 14, // 14 days
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });
  },

  clearTokens: (): void => {
    Cookies.remove(ACCESS_TOKEN_KEY);
    Cookies.remove(REFRESH_TOKEN_KEY);
    localStorage.removeItem('token');
  },
};

// Request interceptor to add auth token
api.interceptors.request.use(
  config => {
    const token = tokenStorage.getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Response interceptor for token refresh and error handling
api.interceptors.response.use(
  response => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = tokenStorage.getRefreshToken();
      if (refreshToken) {
        try {
          const response = await axios.post(
            `${api.defaults.baseURL}/auth/refresh`,
            {
              refresh_token: refreshToken,
            }
          );
          const { access_token, refresh_token: newRefreshToken } =
            response.data.data.authorization;
          tokenStorage.setAccessToken(access_token);
          tokenStorage.setRefreshToken(newRefreshToken);

          // Retry original request with new token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${access_token}`;
          }
          return api(originalRequest);
        } catch (refreshError) {
          tokenStorage.clearTokens();
          window.location.href = '/auth/login';
          return Promise.reject(refreshError);
        }
      } else {
        tokenStorage.clearTokens();
        window.location.href = '/auth/login';
      }
    }

    return Promise.reject(error);
  }
);

// API wrapper functions
export const apiClient = {
  get: <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    return api.get(url, config).then(response => response.data);
  },

  post: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    return api.post(url, data, config).then(response => response.data);
  },

  put: <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    return api.put(url, data, config).then(response => response.data);
  },

  delete: <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    return api.delete(url, config).then(response => response.data);
  },
};

// Error handling utility
export const handleApiError = (error: unknown): ApiError => {
  if (axios.isAxiosError(error)) {
    if (error.response?.data) {
      return error.response.data as ApiError;
    }
    return {
      status: 'error',
      message: error.message || 'Network error occurred',
      data: null,
    };
  }

  return {
    status: 'error',
    message: 'An unexpected error occurred',
    data: null,
  };
};

export default api;
