import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { verificationService } from '../../services';
import { showToast } from '../../utils/toast';
import type { VerificationStatus } from '../../types/verification';

interface VerificationData {
  email_code: string;
}

export const useVerification = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [verificationStatus, setVerificationStatus] =
    useState<VerificationStatus | null>(null);
  const [countdown, setCountdown] = useState(45);

  const email = searchParams.get('email');

  // Check verification status on load
  useEffect(() => {
    checkVerificationStatus();
  }, []);

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const checkVerificationStatus = async () => {
    try {
      const response = await verificationService.getStatus();

      if (response.status === 'success' && response.data) {
        setVerificationStatus(response.data);
        // If email is verified, redirect to dashboard
        if (response.data.email_verified) {
          navigate('/dashboard');
        }
      }
    } catch (error) {
      console.error('Error checking verification status:', error);
    }
  };

  const handleVerification = async (data: VerificationData) => {
    setIsLoading(true);

    try {
      const response = await verificationService.verifyEmailCode({
        email_code: data.email_code,
      });

      if (response.status === 'success') {
        showToast.auth.verificationSuccess();
        // Refresh verification status
        await checkVerificationStatus();
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      } else {
        showToast.error(
          response.message ||
            'Verification failed. Please check your codes and try again.'
        );
      }
    } catch (error: any) {
      showToast.error(
        error.message ||
          'Network error. Please check your connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const resendEmailCode = async (resetForm?: () => void) => {
    setIsResending(true);

    try {
      const response = await verificationService.sendEmailCode();

      if (response.status === 'success') {
        showToast.success('Verification code sent successfully!');
        setCountdown(45); // Reset countdown to 45 seconds
        resetForm?.(); // Clear the form
        checkVerificationStatus(); // Refresh status
      } else {
        showToast.error(
          response.message ||
            'Failed to send verification code. Please try again.'
        );
      }
    } catch (error: any) {
      showToast.error(
        error.message ||
          'Network error. Please check your connection and try again.'
      );
    } finally {
      setIsResending(false);
    }
  };

  const maskEmail = (email: string) => {
    if (!email) return '';
    const [local, domain] = email.split('@');
    const maskedLocal =
      local.charAt(0) +
      '*'.repeat(Math.max(0, local.length - 2)) +
      local.slice(-1);
    return `${maskedLocal}@${domain}`;
  };

  return {
    isLoading,
    isResending,
    verificationStatus,
    countdown,
    email,
    handleVerification,
    resendEmailCode,
    checkVerificationStatus,
    maskEmail,
  };
};
