import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import {
  FileText,
  ArrowLeft,
  User,
  Clock,
  Info,
  Settings,
  Activity,
  DollarSign,
} from 'lucide-react';
import { MainLayout } from '../../components/layout';
import { Button } from '../../components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { LoadingSpinner } from '../../components/ui/loading';
import { Badge } from '../../components/ui/badge';
import { useAdminActivityLog } from '../../hooks/useAdminActivityLogs';
import { formatDate } from '../../utils';
import type { ActionType } from '../../types';

export const ActivityLogDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { data: logResponse, isLoading, error } = useAdminActivityLog(id!);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center min-h-96">
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (error || !logResponse?.data) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            Failed to load activity log details
          </div>
          <Button onClick={() => navigate('/activity-logs')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Activity Logs
          </Button>
        </div>
      </MainLayout>
    );
  }

  const log = logResponse.data;

  const getActionBadge = (actionType: ActionType) => {
    const badgeMap = {
      user_created: 'bg-green-100 text-green-800 border-green-200',
      user_updated: 'bg-blue-100 text-blue-800 border-blue-200',
      user_deleted: 'bg-red-100 text-red-800 border-red-200',
      dispute_resolved: 'bg-green-100 text-green-800 border-green-200',
      dispute_rejected: 'bg-red-100 text-red-800 border-red-200',
      payment_manually_matched:
        'bg-purple-100 text-purple-800 border-purple-200',
      auto_match_triggered: 'bg-orange-100 text-orange-800 border-orange-200',
      settings_created: 'bg-green-100 text-green-800 border-green-200',
      settings_updated: 'bg-blue-100 text-blue-800 border-blue-200',
      settings_deleted: 'bg-red-100 text-red-800 border-red-200',
      platform_fee_collected: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    };

    return badgeMap[actionType] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const formatActionType = (actionType: ActionType) => {
    return actionType
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getActionIcon = (actionType: ActionType) => {
    if (actionType.includes('user')) return User;
    if (actionType.includes('dispute')) return FileText;
    if (actionType.includes('payment') || actionType.includes('match'))
      return Activity;
    if (actionType.includes('settings')) return Settings;
    if (actionType.includes('platform_fee')) return DollarSign;
    return Info;
  };

  const ActionIcon = getActionIcon(log.action_type);

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => navigate('/activity-logs')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Activity Log Details
              </h1>
              <p className="text-sm text-gray-500">
                Detailed information about this administrative action
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Action Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <ActionIcon className="h-6 w-6 text-gray-600" />
                  </div>
                  Action Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Action Type
                  </label>
                  <div className="mt-1">
                    <Badge
                      className={`${getActionBadge(log.action_type)} font-medium text-sm`}
                    >
                      {formatActionType(log.action_type)}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Log ID
                  </label>
                  <p className="text-sm text-gray-900 font-mono mt-1">
                    {log.id}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-500">
                    Timestamp
                  </label>
                  <div className="flex items-center mt-1">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <p className="text-sm text-gray-900">
                      {formatDate(log.created_at)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Metadata */}
            {log.metadata && Object.keys(log.metadata).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Additional Information</CardTitle>
                  <CardDescription>
                    Metadata and context for this action
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
                      {JSON.stringify(log.metadata, null, 2)}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Admin Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Administrator
                </CardTitle>
              </CardHeader>
              <CardContent>
                {log.admin ? (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 bg-brand-gold-500 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-black">
                          {log.admin.full_name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {log.admin.full_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {log.admin.email}
                        </p>
                      </div>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Admin ID
                      </label>
                      <p className="text-sm text-gray-900 font-mono mt-1">
                        {log.admin.id}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">
                    No admin information available
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Target User Information */}
            {log.targetUser && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Target User
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {log.targetUser.full_name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {log.targetUser.full_name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {log.targetUser.email}
                        </p>
                      </div>
                    </div>
                    <div>
                      <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User ID
                      </label>
                      <p className="text-sm text-gray-900 font-mono mt-1">
                        {log.targetUser.id}
                      </p>
                    </div>
                    <Link to={`/users/${log.targetUser.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        View User Profile
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Context */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Context
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {log.action_type.includes('user') && 'User Management'}
                    {log.action_type.includes('dispute') &&
                      'Dispute Resolution'}
                    {log.action_type.includes('payment') &&
                      'Payment Processing'}
                    {log.action_type.includes('settings') &&
                      'Platform Settings'}
                    {log.action_type.includes('platform_fee') &&
                      'Fee Collection'}
                    {!log.action_type.includes('user') &&
                      !log.action_type.includes('dispute') &&
                      !log.action_type.includes('payment') &&
                      !log.action_type.includes('settings') &&
                      !log.action_type.includes('platform_fee') &&
                      'System Action'}
                  </p>
                </div>

                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Impact Level
                  </label>
                  <p className="text-sm text-gray-900 mt-1">
                    {log.action_type.includes('delete') ||
                    log.action_type.includes('reject')
                      ? 'High'
                      : log.action_type.includes('update') ||
                          log.action_type.includes('resolve')
                        ? 'Medium'
                        : 'Low'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};
