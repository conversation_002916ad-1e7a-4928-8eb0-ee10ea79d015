<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('admin_activity_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('admin_id');
            $table->string('action_type');
            $table->uuid('target_user_id')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('admin_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('target_user_id')->references('id')->on('users')->onDelete('set null');

            // Performance indexes for common query patterns
            $table->index('created_at', 'idx_admin_activity_logs_created_at');
            $table->index('admin_id', 'idx_admin_activity_logs_admin_id');
            $table->index('action_type', 'idx_admin_activity_logs_action_type');
            $table->index('target_user_id', 'idx_admin_activity_logs_target_user_id');

            // Composite indexes for common filter combinations
            $table->index(['admin_id', 'created_at'], 'idx_admin_activity_logs_admin_date');
            $table->index(['action_type', 'created_at'], 'idx_admin_activity_logs_action_date');
            $table->index(['created_at', 'admin_id', 'action_type'], 'idx_admin_activity_logs_date_admin_action');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_activity_logs');
    }
};
