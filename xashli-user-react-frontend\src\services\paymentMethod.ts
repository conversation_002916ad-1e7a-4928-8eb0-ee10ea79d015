import type {
  PaymentMethod,
  Bank,
  CreatePaymentMethodRequest,
  UpdatePaymentMethodRequest,
  DeletePaymentMethodRequest,
  BankValidationRequest,
  BankValidationResponse,
  ApiResponse,
} from '../types';
import api from './api';

export const paymentMethodService = {
  // Get all payment methods for the authenticated user
  async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    const response = await api.get('/payment-methods');
    return response.data;
  },

  // Get a specific payment method by ID
  async getPaymentMethod(id: string): Promise<ApiResponse<PaymentMethod>> {
    const response = await api.get(`/payment-methods/${id}`);
    return response.data;
  },

  // Create a new payment method
  async createPaymentMethod(
    data: CreatePaymentMethodRequest
  ): Promise<ApiResponse<PaymentMethod>> {
    const response = await api.post('/payment-methods', data);
    return response.data;
  },

  // Update an existing payment method
  async updatePaymentMethod(
    id: string,
    data: UpdatePaymentMethodRequest
  ): Promise<ApiResponse<PaymentMethod>> {
    const response = await api.put(`/payment-methods/${id}`, data);
    return response.data;
  },

  // Delete a payment method
  async deletePaymentMethod(
    id: string,
    data: DeletePaymentMethodRequest
  ): Promise<ApiResponse<null>> {
    const response = await api.delete(`/payment-methods/${id}`, { data });
    return response.data;
  },

  // Get list of banks from payment processor
  async getBanks(country = 'NG'): Promise<ApiResponse<Bank[]>> {
    const response = await api.get(`/payment-methods/banks/list`, {
      params: { country },
    });
    return response.data;
  },

  // Validate bank account details
  async validateBankAccount(
    data: BankValidationRequest
  ): Promise<ApiResponse<BankValidationResponse>> {
    const response = await api.post('/payment-methods/banks/validate', data);
    return response.data;
  },
};
