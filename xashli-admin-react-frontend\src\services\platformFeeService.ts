import { apiClient } from './api';
import type { ApiResponse } from '../types';
import type {
  PlatformFee,
  PlatformFeeFilters,
  PaginatedPlatformFees,
} from '../types/platformFee';

export interface PlatformFeeStatisticsResponse {
  counts: {
    total: number;
    fiat: number;
    crypto: number;
  };
  amounts: {
    total: {
      fiat: number;
      crypto: number;
    };
  };
}

class PlatformFeeService {
  /**
   * Get paginated list of platform fees
   */
  async getFees(
    filters?: PlatformFeeFilters
  ): Promise<ApiResponse<PaginatedPlatformFees>> {
    const params = new URLSearchParams();

    if (filters?.currency) params.append('currency', filters.currency);
    if (filters?.start_date) params.append('start_date', filters.start_date);
    if (filters?.end_date) params.append('end_date', filters.end_date);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.per_page)
      params.append('per_page', filters.per_page.toString());

    const queryString = params.toString();
    const url = queryString
      ? `/platform-fees?${queryString}`
      : '/platform-fees';

    return apiClient.get<PaginatedPlatformFees>(url);
  }
  /**
   * Get platform fee statistics (unified endpoint that includes summary data)
   */
  async getStatistics(
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<PlatformFeeStatisticsResponse>> {
    const params = new URLSearchParams();

    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const queryString = params.toString();
    const url = queryString
      ? `/platform-fees/statistics?${queryString}`
      : '/platform-fees/statistics';

    return apiClient.get<PlatformFeeStatisticsResponse>(url);
  }
  /**
   * Get details of a specific platform fee
   */
  async getFee(id: string): Promise<ApiResponse<PlatformFee>> {
    return apiClient.get<PlatformFee>(`/platform-fees/${id}`);
  }
}

export const platformFeeService = new PlatformFeeService();
