import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { paymentMethodService } from '../../services';
import type { PaymentMethod } from '../../types';
import toast from 'react-hot-toast';

export const usePaymentMethod = (id?: string) => {
  const navigate = useNavigate();
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const loadPaymentMethod = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await paymentMethodService.getPaymentMethod(id);
      if (response.status === 'success') {
        setPaymentMethod(response.data);
      } else {
        toast.error('Failed to load payment method');
        navigate('/payment-methods');
      }
    } catch (error) {
      toast.error('Failed to load payment method');
      navigate('/payment-methods');
    } finally {
      setLoading(false);
    }
  };
  const handleDelete = async (
    onSuccess?: () => void,
    confirmationCode?: string
  ) => {
    if (
      !paymentMethod ||
      !confirm('Are you sure you want to delete this payment method?')
    ) {
      return;
    }

    try {
      setDeleteLoading(true);
      const response = await paymentMethodService.deletePaymentMethod(
        paymentMethod.id,
        { confirmation_code: confirmationCode || '' }
      );
      if (response.status === 'success') {
        toast.success('Payment method deleted successfully');
        onSuccess?.() || navigate('/payment-methods');
      } else {
        toast.error('Failed to delete payment method');
      }
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Failed to delete payment method'
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadPaymentMethod();
    }
  }, [id]);

  return {
    paymentMethod,
    setPaymentMethod,
    loading,
    deleteLoading,
    loadPaymentMethod,
    handleDelete,
  };
};
