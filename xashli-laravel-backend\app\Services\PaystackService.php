<?php

namespace App\Services;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class PaystackService
{
    protected Client $client;

    protected string $baseUrl;

    protected string $secretKey;

    protected string $publicKey;

    public function __construct()
    {
        $this->client = new Client;
        $this->baseUrl = config('services.paystack.base_url', 'https://api.paystack.co');
        $this->secretKey = config('services.paystack.secret_key');
        $this->publicKey = config('services.paystack.public_key');
    }

    /**
     * Get list of banks from Paystack
     */
    public function getBanks(string $country = 'NG'): array
    {
        try {
            $response = $this->client->get($this->baseUrl . '/bank', [
                'headers' => $this->getHeaders(),
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['status'] === true) {
                $banks = $data['data'] ?? [];

                // If country is specified and not NG, filter banks
                if ($country !== 'NG' && ! empty($banks)) {
                    $banks = array_filter($banks, function ($bank) use ($country) {
                        return isset($bank['country']) && strtoupper($bank['country']) === strtoupper($country);
                    });
                    $banks = array_values($banks); // Re-index array
                }

                return [
                    'success' => true,
                    'banks' => $banks,
                    'message' => 'Banks retrieved successfully',
                ];
            }

            return [
                'success' => false,
                'banks' => [],
                'message' => $data['message'] ?? 'Failed to retrieve banks',
            ];

        } catch (RequestException $e) {
            Log::error('Paystack API Error: ' . $e->getMessage());

            return [
                'success' => false,
                'banks' => [],
                'message' => 'Failed to connect to Paystack API',
            ];
        } catch (Exception $e) {
            Log::error('Paystack Service Error: ' . $e->getMessage());

            return [
                'success' => false,
                'banks' => [],
                'message' => 'An error occurred while retrieving banks',
            ];
        }
    }

    /**
     * Validate bank account details with Paystack
     */
    public function validateBankAccount(string $accountNumber, string $bankCode): array
    {
        try {
            $payload = [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode,
            ];

            $response = $this->client->get($this->baseUrl . '/bank/resolve', [
                'headers' => $this->getHeaders(),
                'query' => $payload,
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['status'] === true) {
                return [
                    'success' => true,
                    'account_name' => $data['data']['account_name'],
                    'account_number' => $accountNumber,
                    'bank_code' => $bankCode,
                    'message' => 'Bank account validated successfully',
                ];
            }

            return [
                'success' => false,
                'message' => $data['message'] ?? 'Failed to validate bank account',
            ];

        } catch (RequestException $e) {
            Log::error('Paystack Account Validation Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to validate bank account',
            ];
        } catch (Exception $e) {
            Log::error('Paystack Validation Service Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'An error occurred while validating bank account',
            ];
        }
    }

    /**
     * Verify a payment transaction with Paystack
     *
     * @param  string  $reference  Transaction reference to verify
     * @param  array  $metadata  Additional metadata for verification
     */
    public function verifyPayment(string $reference, array $metadata = []): array
    {
        try {
            $response = $this->client->get($this->baseUrl . '/transaction/verify/' . rawurlencode($reference), [
                'headers' => $this->getHeaders(),
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (! $data || ! isset($data['status'])) {
                Log::error('Invalid Paystack verification response', [
                    'reference' => $reference,
                    'response' => $data,
                ]);

                return [
                    'status' => 'error',
                    'message' => 'Invalid response from Paystack',
                ];
            }

            if ($data['status'] !== true) {
                Log::warning('Paystack verification failed', [
                    'reference' => $reference,
                    'response' => $data,
                ]);

                return [
                    'status' => 'error',
                    'message' => $data['message'] ?? 'Payment verification failed',
                ];
            }

            $transactionData = $data['data'] ?? [];

            // Verify the transaction status
            $transactionStatus = $transactionData['status'] ?? 'failed';
            if ($transactionStatus !== 'success') {
                return [
                    'status' => 'error',
                    'message' => 'Transaction was not successful',
                    'data' => [
                        'transaction_status' => $transactionStatus,
                        'reference' => $reference,
                    ],
                ];
            }

            // If amount is provided in metadata, verify it matches or is above expected
            if (isset($metadata['amount'])) {
                $expectedAmount = (int) ($metadata['amount'] * 100);
                $actualAmount = (int) ($transactionData['amount'] ?? 0);

                // Allow overpayment but not underpayment
                if ($actualAmount < $expectedAmount) {
                    Log::warning('Paystack amount insufficient', [
                        'reference' => $reference,
                        'expected' => $expectedAmount,
                        'actual' => $actualAmount,
                        'original_metadata_amount' => $metadata['amount'],
                        'shortfall' => $expectedAmount - $actualAmount,
                    ]);

                    return [
                        'status' => 'error',
                        'message' => 'Payment amount insufficient',
                        'data' => [
                            'expected_amount' => $expectedAmount,
                            'actual_amount' => $actualAmount,
                            'shortfall' => $expectedAmount - $actualAmount,
                        ],
                    ];
                }

                // Log if there's an overpayment for tracking purposes
                if ($actualAmount > $expectedAmount) {
                    Log::info('Paystack overpayment received', [
                        'reference' => $reference,
                        'expected' => $expectedAmount,
                        'actual' => $actualAmount,
                        'original_metadata_amount' => $metadata['amount'],
                        'overpayment' => $actualAmount - $expectedAmount,
                    ]);
                }
            }

            Log::info('Paystack payment verified successfully', [
                'reference' => $reference,
                'transaction_id' => $transactionData['id'] ?? null,
                'amount' => $transactionData['amount'] ?? null,
            ]);

            return [
                'status' => 'success',
                'message' => 'Payment verified successfully',
                'data' => [
                    'transaction_status' => $transactionStatus,
                    'transaction_id' => $transactionData['id'] ?? null,
                    'reference' => $reference,
                    'amount' => $transactionData['amount'] ?? null,
                    'currency' => $transactionData['currency'] ?? null,
                    'paid_at' => $transactionData['paid_at'] ?? null,
                    'channel' => $transactionData['channel'] ?? null,
                    'gateway_response' => $transactionData['gateway_response'] ?? null,
                    'customer' => $transactionData['customer'] ?? null,
                    'raw_data' => $transactionData,
                ],
            ];

        } catch (RequestException $e) {
            Log::error('Paystack verification request failed', [
                'reference' => $reference,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);

            return [
                'status' => 'error',
                'message' => 'Failed to connect to Paystack for verification',
            ];

        } catch (Exception $e) {
            Log::error('Paystack verification error', [
                'reference' => $reference,
                'error' => $e->getMessage(),
            ]);

            return [
                'status' => 'error',
                'message' => 'An error occurred during payment verification',
            ];
        }
    }

    /**
     * Get the provider name
     */
    public function getProviderName(): string
    {
        return 'paystack';
    }

    /**
     * Get HTTP headers for Paystack API
     */
    protected function getHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $this->secretKey,
        ];
    }

    /**
     * Generate a unique account reference
     */
    protected function generateAccountReference(array $accountData): string
    {
        return 'ACC-' . strtoupper(uniqid());
    }
}
