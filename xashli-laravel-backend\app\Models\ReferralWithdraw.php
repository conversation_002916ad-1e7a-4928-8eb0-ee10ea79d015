<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReferralWithdraw extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'withdraw_id',
        'amount',
        'currency',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:8',
        'currency' => 'string',
    ];

    /**
     * Get the user who made the referral withdraw.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the withdraw record this referral withdraw is associated with.
     */
    public function withdraw(): BelongsTo
    {
        return $this->belongsTo(Withdraw::class);
    }

    /**
     * Check if this is a fiat referral withdraw.
     */
    public function isFiat(): bool
    {
        return $this->currency === 'fiat';
    }

    /**
     * Check if this is a crypto referral withdraw.
     */
    public function isCrypto(): bool
    {
        return $this->currency === 'crypto';
    }
}
