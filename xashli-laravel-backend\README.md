# Xashli API

This is the backend API for the Xashli platform, built with Laravel.

## Requirements

- PHP 8.1+
- Composer
- MySQL 8.0+
- <PERSON><PERSON> (optional, for queue processing)

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   composer install
   ```
3. Copy the `.env.example` file to `.env` and configure your environment variables:
   ```
   cp .env.example .env
   ```
4. Generate an application key:
   ```
   php artisan key:generate
   ```
5. Generate a JWT secret:
   ```
   php artisan jwt:secret
   ```
6. Run the migrations:
   ```
   php artisan migrate
   ```
7. Seed the database with initial data:
   ```
   php artisan db:seed
   ```
8. Initialize the matching cycle:
   ```
   php artisan app:init-matching-cycle
   ```

## API Structure

The API follows a RESTful structure with the following main endpoints:

### Authentication

- `POST /api/v1/auth/register` - Register a new user
- `POST /api/v1/auth/login` - Login and get JWT token
- `POST /api/v1/auth/refresh` - Refresh JWT token
- `POST /api/v1/auth/logout` - Logout (invalidate token)
- `GET /api/v1/auth/me` - Get current user profile

### Payment Methods

- `GET /api/v1/payment-methods` - List user's payment methods
- `POST /api/v1/payment-methods` - Create a new payment method
- `GET /api/v1/payment-methods/{id}` - Get a specific payment method
- `PUT /api/v1/payment-methods/{id}` - Update a payment method
- `DELETE /api/v1/payment-methods/{id}` - Delete a payment method
- `GET /api/v1/payment-methods/banks/list` - Get list of banks from payment processor
- `POST /api/v1/payment-methods/banks/validate` - Validate bank account details
- `DELETE /api/v1/payment-methods/{id}` - Delete a payment method

### Funds

- `GET /api/v1/funds` - List user's funds
- `POST /api/v1/funds` - Create a new fund
- `GET /api/v1/funds/{id}` - Get a specific fund
- `POST /api/v1/funds/{id}/cancel` - Cancel a pending fund

### Withdraws

- `GET /api/v1/withdraws` - List user's withdraws
- `POST /api/v1/withdraws` - Create a new withdraw request
- `GET /api/v1/withdraws/{id}` - Get a specific withdraw
- `POST /api/v1/withdraws/matches/{matchId}/confirm` - Confirm receipt of a match
- `POST /api/v1/withdraws/matches/{matchId}/dispute` - Dispute a match

### Referrals

- `GET /api/v1/referrals/info` - Get user's referral information
- `GET /api/v1/referrals/referees` - Get user's referees (users they referred)
- `GET /api/v1/referrals/bonuses` - Get user's referral bonuses

### Admin (Protected by admin middleware)

- `GET /api/v1/dashboard` - Get dashboard statistics
- `GET /api/v1/users` - List all users
- `GET /api/v1/users/{id}` - Get a specific user
- `POST /api/v1/users` - Create a new user
- `PUT /api/v1/users/{id}` - Update a user
- `GET /api/v1/platform-settings` - Get platform settings
- `PUT /api/v1/platform-settings` - Update platform settings
- `GET /api/v1/admin-activity-logs` - Get admin activity logs

#### Payment Match Management

**For All Authenticated Users:**
- `GET /api/v1/payment-matches` - List payment matches (admins see all, users see only their own)
- `GET /api/v1/payment-matches/statistics` - Get payment match statistics (role-based data)
- `GET /api/v1/payment-matches/{id}` - Get specific payment match details (role-based access)

**Admin Only (Protected by admin middleware):**
- `POST /api/v1/payment-matches/manual-match` - Manually create a payment match
- `POST /api/v1/payment-matches/auto-match` - Trigger automatic payment matching

## Response Format

All API responses follow a standard format:

```json
{
  "status": "success|error",
  "message": "Human-readable message",
  "data": {
    // Response data
  }
}
```

## Authentication

The API uses JWT (JSON Web Token) for authentication. To access protected endpoints, include the token in the Authorization header:

```
Authorization: Bearer {your_token}
```

Tokens expire after 60 minutes by default. Use the refresh endpoint to get a new token.

## Scheduled Commands

The application includes several scheduled commands:

- `app:auto-match` - Runs hourly to automatically match pending funds with pending withdraws

## Payment Integration

The application uses Paystack for payment processing and bank-related services.

### Configuration

Add the following environment variables to your `.env` file:

```
# Paystack Configuration
PAYSTACK_SECRET_KEY=your_secret_key_here
PAYSTACK_PUBLIC_KEY=your_public_key_here
PAYSTACK_BASE_URL=https://api.paystack.co
```

For development, use test keys:
```
PAYSTACK_SECRET_KEY=sk_test_your_test_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_test_public_key
```

For production, use live keys:
```
PAYSTACK_SECRET_KEY=sk_live_your_live_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_live_public_key
```

### Available Bank Services

#### Get Banks List

**Endpoint:** `GET /api/v1/payment-methods/banks/list`

**Query Parameters:**
- `country` (optional) - Country code (default: NG for Nigeria)

**Response:**
```json
{
  "status": "success",
  "message": "Banks retrieved successfully",
  "data": [
    {
      "id": 1,
      "code": "044",
      "name": "Access Bank"
    }
  ]
}
```

#### Validate Bank Account

**Endpoint:** `POST /api/v1/payment-methods/banks/validate`

**Request Body:**
```json
{
  "account_number": "**********",
  "bank_code": "044"
}
```

**Response:**
```json
{
  "status": "success", 
  "message": "Bank account validated successfully",
  "data": {
    "account_name": "John Doe",
    "account_number": "**********",
    "bank_code": "044"
  }
}
```

### Payment Architecture

The application uses Paystack service for payment processing:

- **`PaystackService`** - Handles all payment operations, bank validation, and transaction verification

Bank services are directly integrated with Paystack APIs for Nigerian banking operations.

## Development

To start the development server:

```
php artisan serve
```

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
