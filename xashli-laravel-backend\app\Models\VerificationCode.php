<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerificationCode extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'type',
        'code',
        'expires_at',
        'is_used',
        'used_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    /**
     * Get the user that owns the verification code.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the verification code is valid.
     */
    public function isValid(): bool
    {
        return ! $this->is_used && $this->expires_at->isFuture();
    }

    /**
     * Mark the verification code as used.
     */
    public function markAsUsed(): void
    {
        $this->update([
            'is_used' => true,
            'used_at' => now(),
        ]);
    }

    /**
     * Generate a random 6-digit verification code.
     */
    public static function generateCode(): string
    {
        return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create a new verification code for a user.
     */
    public static function createForUser(User $user, string $type): self
    {
        // Delete any existing unused codes for this user and type
        static::where('user_id', $user->id)
            ->where('type', $type)
            ->where('is_used', false)
            ->delete();

        return static::create([
            'user_id' => $user->id,
            'type' => $type,
            'code' => static::generateCode(),
            'expires_at' => now()->addHour(),
        ]);
    }

    /**
     * Generate a new verification code for a user (alias for createForUser).
     */
    public static function generate(User $user, string $type): self
    {
        return static::createForUser($user, $type);
    }

    /**
     * Find a valid verification code for a user.
     */
    public static function findValidCode(User $user, string $type, string $code): ?self
    {
        return static::where('user_id', $user->id)
            ->where('type', $type)
            ->where('code', $code)
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Verify this verification code instance.
     */
    public function verify(): bool
    {
        if (! $this->isValid()) {
            return false;
        }

        $this->markAsUsed();

        // Mark email as verified if it's an email verification
        if ($this->type === 'email' && ! $this->user->hasVerifiedEmail()) {
            $this->user->markEmailAsVerified();
        }

        return true;
    }

    /**
     * Verify a code for a user (static method).
     */
    public static function verifyCode(User $user, string $type, string $code): bool
    {
        $verificationCode = static::findValidCode($user, $type, $code);

        if (! $verificationCode) {
            return false;
        }

        return $verificationCode->verify();
    }
}
