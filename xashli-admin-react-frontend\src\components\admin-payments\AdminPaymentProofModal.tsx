import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading';
import { X, Upload, AlertCircle } from 'lucide-react';
import type { PaymentMatchDetails } from '@/types/paymentMatch';
import { formatCurrencyAmount } from '@/utils/format';
import { toast } from 'react-hot-toast';

interface AdminPaymentProofModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (file: File) => Promise<void>;
  paymentMatch: PaymentMatchDetails;
  loading?: boolean;
}

export function AdminPaymentProofModal({
  isOpen,
  onClose,
  onConfirm,
  paymentMatch,
  loading = false,
}: AdminPaymentProofModalProps) {
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const amount = parseFloat(paymentMatch.amount || '0');
  const currency = paymentMatch.fund?.currency || 'fiat';

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file to upload');
      return;
    }

    setUploading(true);

    try {
      await onConfirm(selectedFile);
      onClose();
      resetForm();
    } catch (error) {
      console.error('Payment proof upload failed:', error);
      toast.error('Failed to upload payment proof. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-white rounded-lg max-w-md w-full my-8 p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-brand-black">
              Upload Payment Proof
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              disabled={uploading}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Match Details */}
          <div className="bg-brand-grey-50 rounded-lg p-3 mb-4">
            <div className="flex items-center gap-2 mb-3">
              <Upload className="h-4 w-4 text-brand-gold-600" />
              <span className="text-sm font-medium text-brand-grey-900">
                Match Details
              </span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-brand-grey-600">Amount:</span>
                <span className="font-medium text-brand-grey-900">
                  {formatCurrencyAmount(amount, currency as 'fiat' | 'crypto')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-brand-grey-600">Status:</span>
                <span className="font-medium text-brand-grey-900 capitalize">
                  {paymentMatch.status}
                </span>
              </div>
            </div>
          </div>

          {/* File Upload */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-brand-grey-900 mb-2">
              Payment Proof Image
            </label>
            <div className="space-y-2">
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
                id="payment-proof-upload"
              />

              <label
                htmlFor="payment-proof-upload"
                className="flex items-center justify-center gap-2 w-full p-2 border-2 border-dashed border-brand-grey-300 hover:border-brand-gold-400 rounded-lg cursor-pointer transition-colors bg-brand-grey-50 hover:bg-brand-gold-50"
              >
                <Upload className="h-4 w-4 text-brand-gold-600" />
                <span className="text-brand-grey-900">
                  {selectedFile ? selectedFile.name : 'Choose image file'}
                </span>
              </label>

              {selectedFile && (
                <div className="flex items-center justify-between text-sm text-brand-grey-600">
                  <span>File selected: {selectedFile.name}</span>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedFile(null);
                    }}
                    className="text-brand-gold-600 hover:text-brand-gold-700"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>
            <p className="text-xs text-brand-grey-600 mt-1">
              Upload a screenshot or photo of your payment confirmation
            </p>
          </div>

          {/* Guidelines */}
          <div className="mb-4 p-2 bg-brand-grey-50 rounded border border-brand-grey-200">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-brand-gold-600 flex-shrink-0" />
              <div className="text-sm text-brand-grey-800">
                <p className="font-medium">Guidelines:</p>
                <p className="text-xs">
                  Max 5MB • JPG, PNG, GIF • Clear and readable
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex-1"
              onClick={handleClose}
              disabled={loading || uploading}
            >
              Cancel
            </Button>
            <Button
              className="flex-1 bg-brand-gold-600 hover:bg-brand-gold-700 text-white"
              onClick={handleUpload}
              disabled={loading || uploading || !selectedFile}
            >
              {uploading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Uploading...
                </>
              ) : (
                'Upload Proof'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
