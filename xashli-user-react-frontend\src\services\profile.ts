import { apiClient } from './api';
import type {
  UpdateProfileRequest,
  ProfileUpdateResponse,
  ProfileImageUploadResponse,
  User,
  ApiResponse,
} from '../types';

export const profileService = {
  /**
   * Get current user profile
   */
  getProfile: async (): Promise<ApiResponse<User>> => {
    return apiClient.get<User>('/profile');
  },

  /**
   * Update user profile
   */
  updateProfile: async (
    data: UpdateProfileRequest
  ): Promise<ApiResponse<ProfileUpdateResponse>> => {
    return apiClient.put<ProfileUpdateResponse>('/profile', data);
  },

  /**
   * Upload profile image
   */
  uploadProfileImage: async (
    file: File
  ): Promise<ApiResponse<ProfileImageUploadResponse>> => {
    const formData = new FormData();
    formData.append('image', file);

    return apiClient.post<ProfileImageUploadResponse>(
      '/profile/upload-image',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },

  /**
   * Remove profile image
   */
  removeProfileImage: async (): Promise<ApiResponse<ProfileUpdateResponse>> => {
    return apiClient.delete<ProfileUpdateResponse>('/profile/upload-image');
  },
};
