import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { withdrawService } from '../../services';
import type { WithdrawStatistics } from '../../types';

export function useWithdrawStatistics() {
  const [statistics, setStatistics] = useState<WithdrawStatistics | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchStatistics = useCallback(async () => {
    try {
      setLoading(true);
      const response = await withdrawService.getStatistics();

      if (response.status === 'success' && response.data) {
        setStatistics(response.data);
      } else {
        toast.error(response.message || 'Failed to fetch withdraw statistics');
      }
    } catch (error) {
      console.error('Error fetching withdraw statistics:', error);
      toast.error('Failed to fetch withdraw statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshStatistics = useCallback(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return {
    statistics,
    loading,
    fetchStatistics,
    refreshStatistics,
  };
}
