import { Modal } from '@/components/ui/Modal';
import { But<PERSON> } from '@/components/ui/Button';
import { Shield, Check, Eye, Lock, UserCheck } from 'lucide-react';

interface PrivacyPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept?: () => void; // Optional callback when user accepts
}

export function PrivacyPolicyModal({
  isOpen,
  onClose,
  onAccept,
}: PrivacyPolicyModalProps) {
  const handleAcceptAndContinue = () => {
    onAccept?.();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title='Privacy Policy'
      size='xl'
      className='max-h-[90vh]'
    >
      <div className='flex flex-col h-full max-h-[calc(90vh-8rem)]'>
        {/* Scrollable Content */}
        <div className='flex-1 overflow-y-auto pr-2 space-y-6 text-foreground-secondary'>
          {/* Introduction */}
          <div className='space-y-3'>
            <p>
              At Xashli, we respect your privacy and are committed to protecting
              your personal data. This Privacy Policy outlines how we collect,
              use, store, and protect your information when you use our
              platform.
            </p>
          </div>

          {/* Information We Collect */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <Eye className='h-5 w-5 text-primary' />
              Information We Collect
            </h3>
            <div className='space-y-4'>
              <div className='bg-background-secondary p-4 rounded-lg'>
                <h4 className='font-semibold text-foreground mb-2'>
                  Personal Data:
                </h4>
                <p>
                  Name, phone number, email address, wallet address, bank
                  details.
                </p>
              </div>
              <div className='bg-background-secondary p-4 rounded-lg'>
                <h4 className='font-semibold text-foreground mb-2'>
                  Activity Data:
                </h4>
                <p>
                  Transaction history, referrals, login times, IP address,
                  device details.
                </p>
              </div>
            </div>
          </section>

          {/* How We Use Your Information */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <UserCheck className='h-5 w-5 text-primary' />
              How We Use Your Information
            </h3>
            <ul className='space-y-2 ml-6 list-disc'>
              <li>To verify and manage user accounts</li>
              <li>To process staking, matching, and withdrawal operations</li>
              <li>
                To send notifications and updates (via SMS, email, or WhatsApp)
              </li>
              <li>To improve our services and prevent fraud</li>
            </ul>
          </section>

          {/* Data Sharing */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <Shield className='h-5 w-5 text-primary' />
              Data Sharing
            </h3>
            <div className='bg-success/10 border border-success/20 p-4 rounded-lg space-y-3'>
              <ul className='space-y-2 ml-6 list-disc'>
                <li>
                  <strong>We do not sell your data.</strong>
                </li>
                <li>
                  We may share data with third-party services (e.g., payment
                  processors, SMS/OTP providers) strictly for platform
                  operations.
                </li>
              </ul>
            </div>
          </section>

          {/* Data Security */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <Lock className='h-5 w-5 text-primary' />
              Data Security
            </h3>
            <div className='bg-background-secondary p-4 rounded-lg space-y-3'>
              <ul className='space-y-2 ml-6 list-disc'>
                <li>
                  We implement strong encryption and data protection protocols.
                </li>
                <li>
                  User data is stored securely and access is restricted to
                  authorized personnel only.
                </li>
              </ul>
            </div>
          </section>

          {/* User Rights */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <UserCheck className='h-5 w-5 text-primary' />
              User Rights
            </h3>
            <p>
              You may request access, correction, or deletion of your data by
              contacting us at{' '}
              <a
                href='mailto:<EMAIL>'
                className='text-primary hover:text-primary/90 font-medium'
              >
                <EMAIL>
              </a>
              .
            </p>
          </section>

          {/* Changes to This Policy */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <Eye className='h-5 w-5 text-primary' />
              Changes to This Policy
            </h3>
            <p>
              We may update this Privacy Policy periodically. Users will be
              notified via the platform or email.
            </p>
          </section>

          {/* Disclaimer */}
          <section className='space-y-3'>
            <h3 className='text-lg font-semibold text-foreground flex items-center gap-2'>
              <Shield className='h-5 w-5 text-primary' />
              Disclaimer
            </h3>
            <div className='bg-background-secondary border border-border p-4 rounded-lg space-y-3'>
              <p>
                At Xashli, we are passionate about delivering a smooth and
                rewarding experience to every member of our community. However,
                we cannot always control every external factor; occasional
                downtimes, service delays, bank service delays or third-party
                outages may occur despite our best efforts.
              </p>
              <p>
                By using our platform, you acknowledge that while we strive for
                excellence, perfection isn't always guaranteed. We deeply
                appreciate your understanding and trust as we grow together.
              </p>
              <p className='font-medium'>
                Your continued use of Xashli means you agree with this policy
                and our shared commitment to a secure, evolving platform.
              </p>
            </div>
          </section>
        </div>{' '}
        {/* Sticky Footer */}
        <div className='flex flex-row gap-3 pt-6 mt-6 border-t border-border'>
          <Button variant='outline' onClick={onClose}>
            Close
          </Button>
          {onAccept && (
            <Button onClick={handleAcceptAndContinue}>
              <Check className='h-4 w-4 mr-2' />
              Accept & Continue
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}
