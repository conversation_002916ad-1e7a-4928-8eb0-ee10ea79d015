import React from 'react';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  CheckCircle,
  AlertTriangle,
  User,
  DollarSign,
  ArrowLeft,
  TrendingUp,
  Calendar,
  Eye,
  XCircle,
} from 'lucide-react';

import { withdrawService } from '../../services/withdraw';
import { formatCurrencyAmount } from '../../utils/format';

import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Separator } from '../../components/ui/separator';
import { LoadingSpinner } from '../../components/ui/loading';
import { MainLayout } from '../../components/layout/MainLayout';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';

export const WithdrawDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Fetch withdraw details
  const {
    data: withdrawResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['withdraw', id],
    queryFn: () => withdrawService.getWithdraw(id!),
    enabled: !!id,
  });

  const withdraw = withdrawResponse?.data;

  const getWithdrawStatusBadge = (status: string) => {
    const statusConfig = {
      pending: {
        label: 'Pending',
        color: 'bg-yellow-100 text-yellow-800',
        icon: AlertTriangle,
      },
      matched: {
        label: 'Matched',
        color: 'bg-blue-100 text-blue-800',
        icon: TrendingUp,
      },
      completed: {
        label: 'Completed',
        color: 'bg-green-100 text-green-800',
        icon: CheckCircle,
      },
      cancelled: {
        label: 'Cancelled',
        color: 'bg-red-100 text-red-800',
        icon: XCircle,
      },
    };

    const config =
      statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (error || !withdraw) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Withdraw Not Found
            </h2>
            <p className="text-gray-600 mb-4">
              The withdraw you're looking for doesn't exist or you don't have
              permission to view it.
            </p>
            <Button onClick={() => navigate('/transactions/withdraws')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Withdraws
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  const currency = withdraw.fund?.currency || 'fiat';
  const totalWithdrawalAmount =
    withdraw.total_withdrawable_amount ||
    withdraw.base_withdrawable_amount + withdraw.withdrawable_referral_bonus;

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate('/transactions/withdraws')}
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Withdraw Details
              </h1>
              <p className="text-gray-600 mt-1">Withdraw ID: #{withdraw.id}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getWithdrawStatusBadge(withdraw.status)}
            <RouterLink
              to={`/transactions/payment-matches?withdraw_id=${withdraw.id}`}
            >
              <Button
                variant="outline"
                size="sm"
                className="bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700 hover:text-blue-800"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Payment Matches
              </Button>
            </RouterLink>
          </div>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* User Information */}
          {withdraw.user && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Full Name
                  </label>
                  <p className="text-lg font-semibold">
                    {withdraw.user.full_name}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Email
                  </label>
                  <p className="text-sm">{withdraw.user.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    User ID
                  </label>
                  <p className="font-mono text-sm">{withdraw.user.id}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Financial Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Financial Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Eligible Amount
                  </label>
                  <p className="text-lg font-semibold text-green-600">
                    {formatCurrencyAmount(
                      withdraw.base_withdrawable_amount,
                      currency
                    )}
                  </p>
                </div>
                {withdraw.withdrawable_referral_bonus > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Referral Bonus
                    </label>
                    <p className="text-lg font-semibold text-blue-600">
                      {formatCurrencyAmount(
                        withdraw.withdrawable_referral_bonus,
                        currency
                      )}
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Total Withdrawal
                  </label>
                  <p className="text-xl font-bold">
                    {formatCurrencyAmount(totalWithdrawalAmount, currency)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Amount Matched
                  </label>
                  <p className="text-lg font-semibold">
                    {formatCurrencyAmount(withdraw.amount_matched, currency)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {(
                      (withdraw.amount_matched / totalWithdrawalAmount) *
                      100
                    ).toFixed(1)}
                    % matched
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium">Matching Progress</h4>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all"
                    style={{
                      width: `${Math.min((withdraw.amount_matched / totalWithdrawalAmount) * 100, 100)}%`,
                    }}
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  {formatCurrencyAmount(withdraw.amount_matched, currency)} of{' '}
                  {formatCurrencyAmount(totalWithdrawalAmount, currency)}{' '}
                  matched
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Fund Information */}
          {withdraw.fund && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Source Fund
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Fund ID
                    </label>
                    <p className="font-mono text-sm">{withdraw.fund.id}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Original Amount
                    </label>
                    <p className="text-lg font-semibold">
                      {formatCurrencyAmount(withdraw.fund.amount, currency)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Growth Percentage
                    </label>
                    <p className="text-lg text-green-600">
                      {withdraw.fund.growth_percentage}%
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Maturity Date
                    </label>
                    <p className="text-sm">
                      {withdraw.fund.maturity_date
                        ? format(
                            new Date(withdraw.fund.maturity_date),
                            'MMM dd, yyyy'
                          )
                        : 'TBD'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Timeline Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Created At
                </label>
                <p className="text-lg">
                  {format(new Date(withdraw.created_at), 'PPP')}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  Last Updated
                </label>
                <p className="text-lg">
                  {format(new Date(withdraw.updated_at), 'PPP')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
};
