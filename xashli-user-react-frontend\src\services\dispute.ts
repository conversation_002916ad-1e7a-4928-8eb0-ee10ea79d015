import { apiClient } from './api';
import type {
  CreateDisputeRequest,
  CreateDisputeResponse,
  PaymentDispute,
} from '../types/dispute';
import type { ApiResponse } from '../types/common';

export const disputeService = {
  /**
   * Create a new payment dispute
   */
  createDispute: async (
    matchId: string,
    data: CreateDisputeRequest
  ): Promise<ApiResponse<CreateDisputeResponse>> => {
    return apiClient.post<CreateDisputeResponse>(
      `/payment-disputes/${matchId}`,
      data
    );
  },

  /**
   * Get user's disputes with optional filters
   */
  getUserDisputes: async (params?: {
    status?: string;
    page?: number;
    per_page?: number;
  }): Promise<
    ApiResponse<{
      disputes: PaymentDispute[];
      pagination: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
      };
    }>
  > => {
    return apiClient.get('/payment-disputes', { params });
  },

  /**
   * Get disputes for a specific payment match
   */
  getDisputesByMatch: async (
    matchId: string
  ): Promise<ApiResponse<PaymentDispute[]>> => {
    return apiClient.get<PaymentDispute[]>(
      `/payment-disputes/match/${matchId}`
    );
  },

  /**
   * Get a specific dispute by ID
   */
  getDispute: async (
    disputeId: string
  ): Promise<ApiResponse<PaymentDispute>> => {
    return apiClient.get<PaymentDispute>(`/payment-disputes/${disputeId}`);
  },
};
