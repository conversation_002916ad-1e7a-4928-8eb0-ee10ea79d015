import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Eye, EyeOff, Check, X, CheckCircle } from 'lucide-react';
import {
  useResetPassword,
  usePasswordVisibility,
  usePasswordRequirements,
} from '@/hooks/auth';

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain uppercase, lowercase and number'
      ),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordPage() {
  const { handleResetPassword, isLoading, isSuccess, isValidToken } =
    useResetPassword();
  const {
    showPassword,
    togglePasswordVisibility,
    showConfirmPassword,
    toggleConfirmPasswordVisibility,
  } = usePasswordVisibility();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const password = watch('password', '');
  const { passwordRequirements } = usePasswordRequirements(password);

  const onSubmit = async (data: ResetPasswordFormData) => {
    await handleResetPassword({
      password: data.password,
      confirmPassword: data.confirmPassword,
    });
  };

  // Check if we have a valid token
  if (!isValidToken) {
    return (
      <div className='space-y-6'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-foreground'>
            Invalid reset link
          </h2>
          <p className='mt-2 text-foreground-secondary'>
            The password reset link is invalid or has expired
          </p>
        </div>

        <Card>
          <CardContent className='pt-6'>
            <div className='text-center space-y-4'>
              <div className='mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center'>
                <X className='w-8 h-8 text-destructive' />
              </div>

              <div className='space-y-2'>
                <h3 className='text-lg font-semibold text-foreground'>
                  Link expired or invalid
                </h3>
                <p className='text-foreground-secondary text-sm'>
                  The password reset link you used is either invalid or has
                  expired.
                </p>
              </div>

              <div className='space-y-3 pt-4'>
                <Link to='/auth/forgot-password'>
                  <Button className='w-full'>Request new reset link</Button>
                </Link>

                <Link to='/auth/login'>
                  <Button variant='ghost' className='w-full'>
                    Back to sign in
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isSuccess) {
    return (
      <div className='space-y-6'>
        <div className='text-center'>
          <h2 className='text-3xl font-bold text-foreground'>
            Password reset successful
          </h2>
          <p className='mt-2 text-foreground-secondary'>
            Your password has been successfully updated
          </p>
        </div>

        <Card>
          <CardContent className='pt-6'>
            <div className='text-center space-y-4'>
              <div className='mx-auto w-16 h-16 bg-success/10 rounded-full flex items-center justify-center'>
                <CheckCircle className='w-8 h-8 text-success' />
              </div>

              <div className='space-y-2'>
                <h3 className='text-lg font-semibold text-foreground'>
                  All set!
                </h3>
                <p className='text-foreground-secondary text-sm'>
                  Your password has been updated. You can now sign in with your
                  new password.
                </p>
              </div>

              <div className='pt-4'>
                <Link to='/auth/login'>
                  <Button className='w-full'>Continue to sign in</Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold text-foreground'>Set new password</h2>
        <p className='mt-2 text-foreground-secondary'>
          Choose a strong password for your account
        </p>
      </div>

      <Card>
        <CardContent className='pt-6'>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
            <div className='relative'>
              <Input
                label='New Password'
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter your new password'
                error={errors.password?.message}
                {...register('password')}
              />
              <button
                type='button'
                className='absolute right-3 bottom-[10px] text-foreground-muted hover:text-foreground'
                onClick={togglePasswordVisibility}
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            {password && (
              <div className='space-y-2'>
                <p className='text-sm font-medium text-foreground-secondary'>
                  Password requirements:
                </p>
                <div className='grid grid-cols-2 gap-2'>
                  {passwordRequirements.map((req, index) => (
                    <div key={index} className='flex items-center space-x-2'>
                      {req.met ? (
                        <Check size={16} className='text-success' />
                      ) : (
                        <X size={16} className='text-foreground-muted' />
                      )}
                      <span
                        className={`text-xs ${req.met ? 'text-success' : 'text-foreground-muted'}`}
                      >
                        {req.label}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className='relative'>
              <Input
                label='Confirm New Password'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm your new password'
                error={errors.confirmPassword?.message}
                {...register('confirmPassword')}
              />
              <button
                type='button'
                className='absolute right-3 bottom-[10px] text-foreground-muted hover:text-foreground'
                onClick={toggleConfirmPasswordVisibility}
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>

            <Button type='submit' className='w-full' isLoading={isLoading}>
              {isLoading ? 'Updating password...' : 'Update password'}
            </Button>
          </form>

          <div className='mt-6'>
            <Link to='/auth/login'>
              <Button variant='ghost' className='w-full'>
                Back to sign in
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
