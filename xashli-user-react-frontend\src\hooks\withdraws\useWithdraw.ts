import { useState, useEffect, useCallback } from 'react';
import { withdrawService } from '../../services';
import { paymentMatchService } from '../../services/paymentMatch';
import type { Withdraw, PaymentMatch } from '../../types';

export function useWithdraw(id?: string) {
  const [withdraw, setWithdraw] = useState<Withdraw | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWithdraw = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);
    try {
      const response = await withdrawService.getWithdraw(id);
      if (response.status === 'success' && response.data) {
        setWithdraw(response.data);
      } else {
        setError(response.message || 'Failed to fetch withdraw details');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const confirmPaymentReceived = useCallback(async (matchId: string) => {
    setLoading(true);
    setError(null);
    try {
      const response =
        await paymentMatchService.confirmPaymentReceived(matchId);
      if (response.status === 'success' && response.data) {
        // Update the payment match in the withdraw object
        setWithdraw(prev => {
          if (!prev || !prev.payment_matches) return prev;

          const updatedMatches = prev.payment_matches.map(match =>
            match.id === matchId
              ? ({
                  ...match,
                  status: 'confirmed',
                  is_payment_received_confirmed: true,
                  payment_received_confirmed_at: new Date().toISOString(),
                } as PaymentMatch)
              : match
          );

          return {
            ...prev,
            payment_matches: updatedMatches,
          };
        });
        return true;
      } else {
        setError(response.message || 'Failed to confirm payment');
        return false;
      }
    } catch (err) {
      setError('An unexpected error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    if (id) {
      fetchWithdraw();
    }
  }, [id, fetchWithdraw]);

  return {
    withdraw,
    loading,
    error,
    refetch: fetchWithdraw,
    confirmPaymentReceived,
  };
}
