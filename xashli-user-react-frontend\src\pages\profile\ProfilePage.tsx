import { <PERSON> } from 'react-router-dom';
import { Edit3, User, Mail, Phone, Calendar, Shield } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Badge } from '../../components/ui/Badge';
import { useAuth } from '../../contexts/AuthContext';
import { getImageUrl } from '../../utils/images';

export function ProfilePage() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center py-12'>
          <p className='text-foreground-muted'>No profile data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>My Profile</h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your account information and preferences
          </p>
        </div>
        <Link to='/profile/edit' className='shrink-0'>
          <Button className='flex items-center gap-2'>
            <Edit3 className='h-4 w-4' />
            Edit Profile
          </Button>
        </Link>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        {/* Profile Picture Card */}
        <div className='lg:col-span-1'>
          <Card className='p-6'>
            <div className='text-center'>
              {' '}
              <div className='relative mx-auto mb-4'>
                {getImageUrl(user.profile_image) ? (
                  <img
                    src={getImageUrl(user.profile_image)!}
                    alt={`${user.full_name || 'User'}'s avatar`}
                    className='w-32 h-32 rounded-full object-cover border-4 border-border mx-auto'
                    onError={e => {
                      // Fallback to default avatar if image fails to load
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove(
                        'hidden'
                      );
                    }}
                  />
                ) : null}
                <div
                  className={`w-32 h-32 rounded-full bg-background-secondary border-4 border-border flex items-center justify-center mx-auto ${getImageUrl(user.profile_image) ? 'hidden' : ''}`}
                >
                  <User className='h-16 w-16 text-foreground-muted' />
                </div>
              </div>
              <h3 className='text-xl font-semibold text-foreground mb-2'>
                {user.full_name || 'User'}
              </h3>
              <p className='text-foreground-secondary mb-4'>{user.email}</p>
              <div className='flex justify-center'>
                <Badge
                  variant={user.email_verified_at ? 'default' : 'secondary'}
                >
                  <Shield className='h-3 w-3 mr-1' />
                  {user.email_verified_at ? 'Verified' : 'Unverified'}
                </Badge>
              </div>
            </div>
          </Card>
        </div>

        {/* Profile Information Card */}
        <div className='lg:col-span-2'>
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-foreground mb-6'>
              Personal Information
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-4'>
                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <User className='h-4 w-4' />
                    Full Name
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.full_name || 'Not provided'}
                  </p>
                </div>

                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <Mail className='h-4 w-4' />
                    Email Address
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.email}
                  </p>
                </div>

                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <Phone className='h-4 w-4' />
                    Phone Number
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.phone || 'Not provided'}
                  </p>
                </div>
              </div>

              <div className='space-y-4'>
                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <User className='h-4 w-4' />
                    Referral Code
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.referral_code}
                  </p>
                </div>

                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <User className='h-4 w-4' />
                    Referee Count
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.referee_count}
                  </p>
                </div>

                <div>
                  <label className='flex items-center gap-2 text-sm font-medium text-foreground-secondary mb-2'>
                    <Calendar className='h-4 w-4' />
                    Member Since
                  </label>
                  <p className='text-foreground bg-background-secondary border border-border rounded-lg px-3 py-2'>
                    {user.created_at
                      ? new Date(user.created_at).toLocaleDateString()
                      : 'Unknown'}
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Account Status */}
      <div className='mt-8'>
        <Card className='p-6'>
          <h3 className='text-lg font-semibold text-foreground mb-4'>
            Account Status
          </h3>{' '}
          <div className='grid grid-cols-1 sm:grid-cols-3 gap-4'>
            <div className='flex items-center gap-3'>
              <div
                className={`w-3 h-3 rounded-full ${
                  user.email_verified_at ? 'bg-green-500' : 'bg-yellow-500'
                }`}
              />
              <span className='text-foreground'>
                Email {user.email_verified_at ? 'Verified' : 'Pending'}
              </span>
            </div>
            <div className='flex items-center gap-3'>
              <div
                className={`w-3 h-3 rounded-full ${
                  user.is_active ? 'bg-green-500' : 'bg-red-500'
                }`}
              />
              <span className='text-foreground'>
                Account {user.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className='flex items-center gap-3'>
              <div className='w-3 h-3 rounded-full bg-blue-500' />
              <span className='text-foreground'>
                Profile {user.full_name ? 'Complete' : 'Incomplete'}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
