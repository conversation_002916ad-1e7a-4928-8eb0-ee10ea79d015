<?php

namespace App\Http\Controllers;

use App\Traits\UserManagement;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    use UserManagement;

    /**
     * Update the authenticated user's profile.
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();

            $validator = Validator::make($request->all(), $this->getUserValidationRules($user->id, false));

            if ($validator->fails()) {
                return $this->validationError($validator->errors()->toArray());
            }

            $this->updateUserFields($user, $request, false);
            $user->save();

            return $this->success($user, 'Profile updated successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to update profile: ' . $e->getMessage());
        }
    }

    /**
     * Upload a profile image for the authenticated user.
     */
    public function uploadProfileImage(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048', // 2MB max
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors()->toArray());
            }

            $user = auth()->user();

            // Delete old profile image if exists
            if ($user->profile_image) {
                // Extract the filename from the URL to delete from storage
                $oldImagePath = str_replace('/storage/', '', $user->profile_image);
                Storage::disk('public')->delete($oldImagePath);
            }

            // Store the new profile image
            $file = $request->file('image');
            $filename = 'uploads/profile_images/user_' . $user->id . '_' . uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $storedFilePath = $file->storeAs('', $filename, 'public');

            if (! $storedFilePath) {
                return $this->serverError('Failed to upload image');
            }

            // Get the public URL
            $publicPath = Storage::url($storedFilePath);

            // Update user with the new profile image path
            $user->profile_image = $publicPath;
            $user->save();

            return $this->success([
                'profile_image_url' => $publicPath,
                'user' => $user,
            ], 'Profile image uploaded successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to upload profile image: ' . $e->getMessage());
        }
    }
}
