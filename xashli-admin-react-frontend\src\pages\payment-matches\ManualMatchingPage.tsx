import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  RefreshCw,
  ArrowDownToLine,
  ArrowUpFromLine,
  Play,
  Zap,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { MainLayout } from '@/components/layout';
import { fundService } from '@/services/fund';
import { withdrawService } from '@/services/withdraw';
import { paymentMatchService } from '@/services/paymentMatch';
import { toNumber } from '@/utils/convert';
import type { Fund } from '@/types/fund';
import type { Withdraw } from '@/types/withdraw';

interface MatchableItem {
  id: string;
  amount: number;
  amountMatched: number;
  currency: 'fiat' | 'crypto';
  remainingAmount: number;
  user: {
    id: string;
    full_name: string;
    email: string;
  };
  created_at: string;
  paymentMethod?: {
    type: string;
    label?: string;
  };
}

const ManualMatchingPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [autoMatchLoading, setAutoMatchLoading] = useState(false);
  const [availableFunds, setAvailableFunds] = useState<MatchableItem[]>([]);
  const [availableWithdraws, setAvailableWithdraws] = useState<MatchableItem[]>(
    []
  );
  const [selectedFund, setSelectedFund] = useState<MatchableItem | null>(null);
  const [selectedWithdraw, setSelectedWithdraw] =
    useState<MatchableItem | null>(null);
  const [matchAmount, setMatchAmount] = useState<string>('');
  const [creating, setCreating] = useState(false);
  const [filters, setFilters] = useState({
    currency: 'all',
    search: '',
  });

  const fetchMatchableItems = async () => {
    try {
      setLoading(true);
      // Prepare filters with proper types
      const fundParams = {
        status: 'pending' as const,
        per_page: 100,
        ...(filters.currency && filters.currency !== 'all'
          ? { currency: filters.currency as 'fiat' | 'crypto' }
          : {}),
        ...(filters.search ? { search: filters.search } : {}),
      };

      const withdrawParams = {
        status: 'pending' as const,
        per_page: 100,
        ...(filters.currency && filters.currency !== 'all'
          ? { currency: filters.currency as 'fiat' | 'crypto' }
          : {}),
        ...(filters.search ? { search: filters.search } : {}),
      };

      // Fetch pending funds
      const fundsResponse = await fundService.getFunds(fundParams);

      // Fetch pending withdraws
      const withdrawsResponse =
        await withdrawService.getWithdraws(withdrawParams);

      if (fundsResponse?.data && withdrawsResponse?.data) {
        // Extract the arrays from the response data
        const fundsData = fundsResponse.data.funds;
        const withdrawsData = withdrawsResponse.data.withdraws;

        // Transform funds to matchable items
        const funds: MatchableItem[] = fundsData
          .map((fund: Fund) => ({
            id: fund.id,
            amount: toNumber(fund.amount),
            amountMatched: toNumber(fund.amount_matched, 0),
            currency: fund.currency,
            remainingAmount:
              toNumber(fund.amount) - toNumber(fund.amount_matched, 0),
            user: fund.user!,
            created_at: fund.created_at,
            paymentMethod: fund.paymentMethod,
          }))
          .filter((fund: MatchableItem) => fund.remainingAmount > 0);

        // Transform withdraws to matchable items
        const withdraws: MatchableItem[] = withdrawsData
          .map((withdraw: Withdraw) => ({
            id: withdraw.id,
            amount: toNumber(withdraw.total_withdrawable_amount),
            amountMatched: toNumber(withdraw.amount_matched, 0),
            currency: withdraw.fund!.currency,
            remainingAmount:
              toNumber(withdraw.total_withdrawable_amount) -
              toNumber(withdraw.amount_matched, 0),
            user: withdraw.user!,
            created_at: withdraw.created_at,
            paymentMethod: withdraw.payment_method,
          }))
          .filter((withdraw: MatchableItem) => withdraw.remainingAmount > 0);

        setAvailableFunds(funds);
        setAvailableWithdraws(withdraws);
      } else {
        toast.error('Failed to fetch matchable items');
      }
    } catch (error) {
      toast.error('Error fetching data');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoMatch = async () => {
    try {
      setAutoMatchLoading(true);
      const response = await paymentMatchService.triggerAutoMatch();

      if (response.status === 'success' && response.data) {
        toast.success(
          `Auto-matching completed! Created ${response.data.matches_created} matches.`
        );
        fetchMatchableItems(); // Refresh the lists
        resetSelection();
      } else {
        toast.error(response.message || 'Auto-matching failed');
      }
    } catch (error) {
      toast.error('Error triggering auto-match');
      console.error('Error:', error);
    } finally {
      setAutoMatchLoading(false);
    }
  };

  const handleManualMatch = async () => {
    if (!selectedFund || !selectedWithdraw || !matchAmount) {
      toast.error('Please select fund, withdraw, and enter match amount');
      return;
    }

    const amount = toNumber(matchAmount);
    if (amount <= 0) {
      toast.error('Match amount must be greater than 0');
      return;
    }

    if (amount > selectedFund.remainingAmount) {
      toast.error('Match amount exceeds available fund amount');
      return;
    }

    if (amount > selectedWithdraw.remainingAmount) {
      toast.error('Match amount exceeds remaining withdraw amount');
      return;
    }

    if (selectedFund.currency !== selectedWithdraw.currency) {
      toast.error('Fund and withdraw currencies must match');
      return;
    }

    try {
      setCreating(true);
      const response = await paymentMatchService.createManualMatch({
        fund_id: selectedFund.id,
        withdraw_id: selectedWithdraw.id,
        amount: amount,
      });

      if (response.status === 'success') {
        toast.success('Manual match created successfully!');
        fetchMatchableItems(); // Refresh the lists
        resetSelection();
      } else {
        toast.error(response.message || 'Failed to create manual match');
      }
    } catch (error) {
      toast.error('Error creating manual match');
      console.error('Error:', error);
    } finally {
      setCreating(false);
    }
  };

  const resetSelection = () => {
    setSelectedFund(null);
    setSelectedWithdraw(null);
    setMatchAmount('');
  };

  const calculateMaxMatchAmount = (): number => {
    if (!selectedFund || !selectedWithdraw) return 0;
    return Math.min(
      selectedFund.remainingAmount,
      selectedWithdraw.remainingAmount
    );
  };

  const setMaxAmount = () => {
    const maxAmount = calculateMaxMatchAmount();
    setMatchAmount(maxAmount.toString());
  };

  useEffect(() => {
    fetchMatchableItems();
  }, [filters]);

  const formatCurrencyAmount = (amount: number, currency: string) => {
    return currency === 'fiat'
      ? `₦${amount.toFixed(2)}`
      : `${amount.toFixed(4)} SOL`;
  };

  const getCurrencyBadgeColor = (currency: string) => {
    return currency === 'fiat'
      ? 'bg-green-100 text-green-800'
      : 'bg-purple-100 text-purple-800';
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">Manual Payment Matching</h1>
            <p className="text-gray-600">
              Create manual matches between funds and withdraws
            </p>
          </div>
          <div className="flex flex-row gap-3">
            <Button
              onClick={fetchMatchableItems}
              variant="outline"
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
            <Button
              onClick={handleAutoMatch}
              disabled={autoMatchLoading}
              className="flex items-center gap-2 bg-brand-gold-500 hover:bg-brand-gold-600 text-black font-medium"
            >
              {autoMatchLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Zap className="w-4 h-4" />
              )}
              Trigger Auto-Match
            </Button>
          </div>
        </div>
        {/* Match Creation Panel */}
        {(selectedFund || selectedWithdraw) && (
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <Play className="w-5 h-5" />
                Create Manual Match
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Selected Fund */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Selected Fund</label>
                  {selectedFund ? (
                    <div className="p-3 border rounded-lg bg-white">
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          className={getCurrencyBadgeColor(
                            selectedFund.currency
                          )}
                        >
                          {selectedFund.currency.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          #{selectedFund.id.slice(0, 8)}
                        </span>
                      </div>
                      <div className="space-y-1">
                        <div className="text-lg font-semibold text-green-600">
                          {formatCurrencyAmount(
                            selectedFund.remainingAmount,
                            selectedFund.currency
                          )}{' '}
                          <span className="text-sm text-gray-500">
                            available
                          </span>
                        </div>
                        {selectedFund.amountMatched > 0 && (
                          <div className="text-sm text-gray-600">
                            {formatCurrencyAmount(
                              selectedFund.amount,
                              selectedFund.currency
                            )}{' '}
                            total •{' '}
                            {formatCurrencyAmount(
                              selectedFund.amountMatched,
                              selectedFund.currency
                            )}{' '}
                            matched
                          </div>
                        )}
                        {selectedFund.amountMatched === 0 && (
                          <div className="text-sm text-gray-600">
                            {formatCurrencyAmount(
                              selectedFund.amount,
                              selectedFund.currency
                            )}{' '}
                            total
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        User: {selectedFund.user.full_name}
                      </div>
                    </div>
                  ) : (
                    <div className="p-3 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500">
                      Select a fund from the list below
                    </div>
                  )}
                </div>

                {/* Selected Withdraw */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Selected Withdraw
                  </label>
                  {selectedWithdraw ? (
                    <div className="p-3 border rounded-lg bg-white">
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          className={getCurrencyBadgeColor(
                            selectedWithdraw.currency
                          )}
                        >
                          {selectedWithdraw.currency.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          #{selectedWithdraw.id.slice(0, 8)}
                        </span>
                      </div>
                      <div className="space-y-1">
                        <div className="text-lg font-semibold text-red-600">
                          {formatCurrencyAmount(
                            selectedWithdraw.remainingAmount,
                            selectedWithdraw.currency
                          )}{' '}
                          <span className="text-sm text-gray-500">needed</span>
                        </div>
                        {selectedWithdraw.amountMatched > 0 && (
                          <div className="text-sm text-gray-600">
                            {formatCurrencyAmount(
                              selectedWithdraw.amount,
                              selectedWithdraw.currency
                            )}{' '}
                            total •{' '}
                            {formatCurrencyAmount(
                              selectedWithdraw.amountMatched,
                              selectedWithdraw.currency
                            )}{' '}
                            matched
                          </div>
                        )}
                        {selectedWithdraw.amountMatched === 0 && (
                          <div className="text-sm text-gray-600">
                            {formatCurrencyAmount(
                              selectedWithdraw.amount,
                              selectedWithdraw.currency
                            )}{' '}
                            total
                          </div>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        User: {selectedWithdraw.user.full_name}
                      </div>
                    </div>
                  ) : (
                    <div className="p-3 border-2 border-dashed border-gray-300 rounded-lg text-center text-gray-500">
                      Select a withdraw from the list below
                    </div>
                  )}
                </div>
              </div>

              {/* Match Amount Input */}
              {selectedFund && selectedWithdraw && (
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <label className="block text-sm font-medium mb-1">
                        Match Amount
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={calculateMaxMatchAmount()}
                        value={matchAmount}
                        onChange={e => setMatchAmount(e.target.value)}
                        placeholder="Enter amount to match"
                        className="text-lg"
                      />
                    </div>
                    <Button
                      onClick={setMaxAmount}
                      variant="outline"
                      size="sm"
                      className="mt-6"
                    >
                      Max:{' '}
                      {formatCurrencyAmount(
                        calculateMaxMatchAmount(),
                        selectedFund.currency
                      )}
                    </Button>
                  </div>

                  <div className="flex items-center gap-3">
                    <Button
                      onClick={handleManualMatch}
                      disabled={
                        creating || !matchAmount || parseFloat(matchAmount) <= 0
                      }
                      className="flex items-center gap-2"
                    >
                      {creating ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <CheckCircle className="w-4 h-4" />
                      )}
                      Create Match
                    </Button>
                    <Button onClick={resetSelection} variant="outline">
                      Clear Selection
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}{' '}
        {/* Available Items Grid */}
        <div className="space-y-4">
          {/* Consolidated Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filter Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">
                    Currency
                  </label>
                  <Select
                    value={filters.currency}
                    onValueChange={value =>
                      setFilters(prev => ({ ...prev, currency: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All currencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All currencies</SelectItem>
                      <SelectItem value="fiat">Fiat (₦)</SelectItem>
                      <SelectItem value="crypto">Crypto (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">
                    Search
                  </label>
                  <Input
                    placeholder="Search by user name or ID..."
                    value={filters.search}
                    onChange={e =>
                      setFilters(prev => ({
                        ...prev,
                        search: e.target.value,
                      }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Available Funds */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <ArrowDownToLine className="w-5 h-5 text-green-600" />
                    Available Funds
                    <Badge variant="secondary">{availableFunds.length}</Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                    <p className="text-gray-600">Loading funds...</p>
                  </div>
                ) : availableFunds.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>No available funds found</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {availableFunds.map(fund => (
                      <div
                        key={fund.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedFund?.id === fund.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedFund(fund)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Badge
                            className={getCurrencyBadgeColor(fund.currency)}
                          >
                            {fund.currency.toUpperCase()}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            #{fund.id.slice(0, 8)}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <div className="text-lg font-semibold text-green-600">
                            {formatCurrencyAmount(
                              fund.remainingAmount,
                              fund.currency
                            )}{' '}
                            <span className="text-sm text-gray-500">
                              remaining
                            </span>
                          </div>
                          {fund.amountMatched > 0 && (
                            <div className="text-sm text-gray-600">
                              {formatCurrencyAmount(fund.amount, fund.currency)}{' '}
                              total •{' '}
                              {formatCurrencyAmount(
                                fund.amountMatched,
                                fund.currency
                              )}{' '}
                              matched
                            </div>
                          )}
                          {fund.amountMatched === 0 && (
                            <div className="text-sm text-gray-600">
                              {formatCurrencyAmount(fund.amount, fund.currency)}{' '}
                              total
                            </div>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>User: {fund.user.full_name}</div>
                          <div>
                            Created:{' '}
                            {new Date(fund.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>{' '}
            {/* Available Withdraws */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <ArrowUpFromLine className="w-5 h-5 text-red-600" />
                    Available Withdraws
                    <Badge variant="secondary">
                      {availableWithdraws.length}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                    <p className="text-gray-600">Loading withdraws...</p>
                  </div>
                ) : availableWithdraws.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>No available withdraws found</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {availableWithdraws.map(withdraw => (
                      <div
                        key={withdraw.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedWithdraw?.id === withdraw.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedWithdraw(withdraw)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Badge
                            className={getCurrencyBadgeColor(withdraw.currency)}
                          >
                            {withdraw.currency.toUpperCase()}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            #{withdraw.id.slice(0, 8)}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <div className="text-lg font-semibold text-red-600">
                            {formatCurrencyAmount(
                              withdraw.remainingAmount,
                              withdraw.currency
                            )}{' '}
                            <span className="text-sm text-gray-500">
                              remaining
                            </span>
                          </div>
                          {withdraw.amountMatched > 0 && (
                            <div className="text-sm text-gray-600">
                              {formatCurrencyAmount(
                                withdraw.amount,
                                withdraw.currency
                              )}{' '}
                              total •{' '}
                              {formatCurrencyAmount(
                                withdraw.amountMatched,
                                withdraw.currency
                              )}{' '}
                              matched
                            </div>
                          )}
                          {withdraw.amountMatched === 0 && (
                            <div className="text-sm text-gray-600">
                              {formatCurrencyAmount(
                                withdraw.amount,
                                withdraw.currency
                              )}{' '}
                              total
                            </div>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>User: {withdraw.user.full_name}</div>
                          <div>
                            Created:{' '}
                            {new Date(withdraw.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default ManualMatchingPage;
