<?php

namespace App\Http\Controllers;

use App\Models\ReferralBonus;
use App\Models\ReferralWithdraw;
use App\Models\User;
use App\Models\UserStat;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ReferralController extends Controller
{
    /**
     * Get the user's referral information.
     *
     * For users: Returns their own referral info
     * For admins: Can specify user_id or referral_code to get any user's referral info
     */
    public function info(Request $request): JsonResponse
    {
        $currentUser = auth()->user();
        $targetUser = $currentUser;

        // If admin, allow querying other users' referral info
        if ($currentUser->isAdmin()) {
            if ($request->has('user_id')) {
                $targetUser = User::find($request->user_id);
                if (! $targetUser) {
                    return $this->notFound('User not found');
                }
            } elseif ($request->has('referral_code')) {
                $targetUser = User::where('referral_code', $request->referral_code)->first();
                if (! $targetUser) {
                    return $this->notFound('User with referral code not found');
                }
            }
        }

        $response = [
            'target_user' => [
                'id' => $targetUser->id,
                'full_name' => $targetUser->full_name,
                'email' => $targetUser->email,
                'referral_code' => $targetUser->referral_code,
            ],
            'referral_code' => $targetUser->referral_code,
            'referee_count' => $targetUser->referee_count,
            'referral_link' => config('app.frontend_url') . '/auth/register?ref=' . $targetUser->referral_code,
        ];

        return $this->success($response, 'Referral information retrieved successfully');
    }

    /**
     * Get referees (users referred by a specific user).
     *
     * For users: Returns their own referees
     * For admins: Can specify user_id or referral_code to get any user's referees
     */
    public function referees(Request $request): JsonResponse
    {
        $currentUser = auth()->user();
        $targetUser = $currentUser;

        // If admin, allow querying other users' referees
        if ($currentUser->isAdmin()) {
            if ($request->has('user_id')) {
                $targetUser = User::find($request->user_id);
                if (! $targetUser) {
                    return $this->notFound('User not found');
                }
            } elseif ($request->has('referral_code')) {
                $targetUser = User::where('referral_code', $request->referral_code)->first();
                if (! $targetUser) {
                    return $this->notFound('User with referral code not found');
                }
            }
        }

        // Get direct referees (level 1)
        $directReferees = User::where('referrer_id', $targetUser->id)
            ->select('id', 'full_name', 'email', 'created_at', 'referee_count', 'is_active')
            ->get()
            ->map(function ($referee) {
                $referee->level = 1;

                return $referee;
            });

        // Get level 2 referees (referees of referees)
        $level2Referees = User::whereIn('referrer_id', $directReferees->pluck('id'))
            ->select('id', 'full_name', 'email', 'created_at', 'referee_count', 'referrer_id', 'is_active')
            ->get()
            ->map(function ($referee) {
                $referee->level = 2;

                return $referee;
            });

        // Get level 3 referees
        $level3Referees = User::whereIn('referrer_id', $level2Referees->pluck('id'))
            ->select('id', 'full_name', 'email', 'created_at', 'referee_count', 'referrer_id', 'is_active')
            ->get()
            ->map(function ($referee) {
                $referee->level = 3;

                return $referee;
            });

        // Merge all referees and sort by creation date (newest first)
        $allReferees = $directReferees->concat($level2Referees)->concat($level3Referees)
            ->sortByDesc('created_at')
            ->values();

        // Update referral counts in UserStat for the target user
        $userStat = UserStat::firstOrCreate(['user_id' => $targetUser->id], [
            'updated_at' => now(),
        ]);
        $userStat->updateRefereeCounts();

        $referees = [
            'target_user' => [
                'id' => $targetUser->id,
                'full_name' => $targetUser->full_name,
                'email' => $targetUser->email,
                'referral_code' => $targetUser->referral_code,
            ],
            'referees' => [
                'level1' => $directReferees,
                'level2' => $level2Referees,
                'level3' => $level3Referees,
                'all' => $allReferees,
            ],
            'counts' => [
                'level1' => $directReferees->count(),
                'level2' => $level2Referees->count(),
                'level3' => $level3Referees->count(),
                'total' => $directReferees->count() + $level2Referees->count() + $level3Referees->count(),
            ],
        ];

        return $this->success($referees, 'Referees retrieved successfully');
    }

    /**
     * Get referral bonuses for a specific user.
     *
     * For users: Returns their own bonuses
     * For admins: Can specify user_id or referral_code to get any user's bonuses
     *
     * Query parameters:
     * - date_from: Filter bonuses from this date (YYYY-MM-DD format)
     * - date_to: Filter bonuses to this date (YYYY-MM-DD format)
     * - level: Filter by referral level (1, 2, or 3)
     */
    public function bonuses(Request $request): JsonResponse
    {
        $currentUser = auth()->user();
        $targetUser = $currentUser;

        // If admin, allow querying for other users
        if ($currentUser->isAdmin()) {
            if ($request->has('user_id')) {
                $targetUser = User::find($request->user_id);
                if (! $targetUser) {
                    return $this->error('User not found', 404);
                }
            } elseif ($request->has('referral_code')) {
                $targetUser = User::where('referral_code', $request->referral_code)->first();
                if (! $targetUser) {
                    return $this->error('User with referral code not found', 404);
                }
            }
        }

        $query = ReferralBonus::where('referrer_user_id', $targetUser->id)
            ->with(['refereeUser:id,full_name,email', 'fund:id,amount,currency']);

        // Filter by date range if provided
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by level if provided
        if ($request->has('level') && in_array($request->level, [1, 2, 3])) {
            $query->where('level', $request->level);
        }

        // Sort by created_at in descending order (newest first)
        $bonuses = $query->orderBy('created_at', 'desc')->get();

        // Group bonuses by currency
        $fiatBonuses = $bonuses->where('fund.currency', 'fiat');
        $cryptoBonuses = $bonuses->where('fund.currency', 'crypto');

        // Get user stats for accurate referral bonus tracking
        $userStat = UserStat::firstOrCreate(['user_id' => $targetUser->id], [
            'updated_at' => now(),
        ]);

        $summary = [
            'fiat' => [
                'total_referral_bonus_amount' => (float) $userStat->total_fiat_referral_bonus_earned,
                'withdrawable_referral_amount' => (float) $userStat->available_fiat_referral_bonus,
                'consumed_referral_amount' => (float) $userStat->consumed_fiat_referral_bonus,
                'pending_referral_amount' => $fiatBonuses->sum('amount'), // Bonuses not yet added to user stats
                'count' => $fiatBonuses->count(),
            ],
            'crypto' => [
                'total_referral_bonus_amount' => (float) $userStat->total_crypto_referral_bonus_earned,
                'withdrawable_referral_amount' => (float) $userStat->available_crypto_referral_bonus,
                'consumed_referral_amount' => (float) $userStat->consumed_crypto_referral_bonus,
                'pending_referral_amount' => $cryptoBonuses->sum('amount'), // Bonuses not yet added to user stats
                'count' => $cryptoBonuses->count(),
            ],
            'total_count' => $bonuses->count(),
        ];

        $response = [
            'bonuses' => $bonuses,
            'summary' => $summary,
        ];

        // Include target user info if admin is querying for another user
        if ($currentUser->isAdmin() && $targetUser->id !== $currentUser->id) {
            $response['target_user'] = [
                'id' => $targetUser->id,
                'full_name' => $targetUser->full_name,
                'email' => $targetUser->email,
                'referral_code' => $targetUser->referral_code,
            ];
        }

        return $this->success($response, 'Referral bonuses retrieved successfully');
    }

    /**
     * Get referral withdraws.
     *
     * For users: Returns their own referral withdraws
     * For admins: Can specify user_id to get any user's referral withdraws, or get all if no user_id specified
     */
    public function withdraws(Request $request): JsonResponse
    {
        $currentUser = auth()->user();
        $targetUser = $currentUser;

        // If admin, allow querying other users' referral withdraws
        if ($currentUser->isAdmin()) {
            if ($request->has('user_id')) {
                $targetUser = User::find($request->user_id);
                if (! $targetUser) {
                    return $this->notFound('User not found');
                }
            } elseif ($request->has('referral_code')) {
                $targetUser = User::where('referral_code', $request->referral_code)->first();
                if (! $targetUser) {
                    return $this->notFound('User with referral code not found');
                }
            }
        }

        $query = ReferralWithdraw::with(['user:id,full_name,email', 'withdraw:id,fund_id,status,created_at'])
            ->where('user_id', $targetUser->id);

        // Apply filters
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $query->where('currency', $request->currency);
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Order by most recent first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $perPage = min($request->get('per_page', 15), 50);
        $withdraws = $query->paginate($perPage);

        // Calculate summary statistics
        $summaryQuery = ReferralWithdraw::where('user_id', $targetUser->id);

        // Apply same filters to summary
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $summaryQuery->where('currency', $request->currency);
        }
        if ($request->has('date_from')) {
            $summaryQuery->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->has('date_to')) {
            $summaryQuery->whereDate('created_at', '<=', $request->date_to);
        }

        $fiatWithdraws = (clone $summaryQuery)->where('currency', 'fiat');
        $cryptoWithdraws = (clone $summaryQuery)->where('currency', 'crypto');

        $summary = [
            'fiat' => [
                'count' => $fiatWithdraws->count(),
                'total_amount' => (float) $fiatWithdraws->sum('amount'),
            ],
            'crypto' => [
                'count' => $cryptoWithdraws->count(),
                'total_amount' => (float) $cryptoWithdraws->sum('amount'),
            ],
            'total_count' => $summaryQuery->count(),
        ];

        $response = [
            'withdraws' => $withdraws->items(),
            'summary' => $summary,
            'pagination' => [
                'current_page' => $withdraws->currentPage(),
                'last_page' => $withdraws->lastPage(),
                'per_page' => $withdraws->perPage(),
                'total' => $withdraws->total(),
            ],
        ];

        // Include target user info if admin is querying for another user
        if ($currentUser->isAdmin() && $targetUser->id !== $currentUser->id) {
            $response['target_user'] = [
                'id' => $targetUser->id,
                'full_name' => $targetUser->full_name,
                'email' => $targetUser->email,
                'referral_code' => $targetUser->referral_code,
            ];
        }

        return $this->success($response, 'Referral withdraws retrieved successfully');
    }
}
