<?php

namespace App\Http\Controllers;

use App\Models\Fund;
use App\Models\PaymentMatch;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Models\Withdraw;
use App\Services\UserPrivilegeService;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    /**
     * Get admin dashboard statistics.
     */
    public function admin(): JsonResponse
    {
        // Get all user statistics in one query
        $users = User::where('role', 'user')
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,
                SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_count,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today_count
            ')
            ->first();

        // Get all fund statistics in one query
        $funds = Fund::selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = "matched" THEN 1 ELSE 0 END) as matched_count,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count,
                SUM(CASE WHEN status = "cancelled" THEN 1 ELSE 0 END) as cancelled_count,
                SUM(CASE WHEN currency = "fiat" THEN amount ELSE 0 END) as total_fiat_amount,
                SUM(CASE WHEN currency = "crypto" THEN amount ELSE 0 END) as total_crypto_amount,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today_count
            ')
            ->first();

        // Get all withdraw statistics in one query
        $withdraws = Withdraw::join('funds', 'withdraws.fund_id', '=', 'funds.id')
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN withdraws.status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN withdraws.status = "matched" THEN 1 ELSE 0 END) as matched_count,
                SUM(CASE WHEN withdraws.status = "completed" THEN 1 ELSE 0 END) as completed_count,
                SUM(CASE WHEN funds.currency = "fiat" THEN withdraws.base_withdrawable_amount ELSE 0 END) as total_fiat_amount,
                SUM(CASE WHEN funds.currency = "crypto" THEN withdraws.base_withdrawable_amount ELSE 0 END) as total_crypto_amount,
                SUM(CASE WHEN DATE(withdraws.created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today_count
            ')
            ->first();

        // Get all payment match statistics in one query
        $matches = PaymentMatch::join('funds', 'payment_matches.fund_id', '=', 'funds.id')
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN payment_matches.status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN payment_matches.status = "paid" THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN payment_matches.status = "confirmed" THEN 1 ELSE 0 END) as confirmed_count,
                SUM(CASE WHEN payment_matches.status = "disputed" THEN 1 ELSE 0 END) as disputed_count,
                SUM(CASE WHEN funds.currency = "fiat" THEN payment_matches.amount ELSE 0 END) as total_fiat_amount,
                SUM(CASE WHEN funds.currency = "crypto" THEN payment_matches.amount ELSE 0 END) as total_crypto_amount,
                SUM(CASE WHEN DATE(payment_matches.created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today_count
            ')
            ->first();

        return $this->success([
            'users' => [
                'counts' => [
                    'total' => $users->total_count ?? 0,
                    'active' => $users->active_count ?? 0,
                    'inactive' => $users->inactive_count ?? 0,
                    'new_today' => $users->new_today_count ?? 0,
                ],
            ],
            'funds' => [
                'counts' => [
                    'total' => $funds->total_count ?? 0,
                    'statuses' => [
                        'pending' => $funds->pending_count ?? 0,
                        'matched' => $funds->matched_count ?? 0,
                        'completed' => $funds->completed_count ?? 0,
                        'cancelled' => $funds->cancelled_count ?? 0,
                    ],
                    'new_today' => $funds->new_today_count ?? 0,
                ],
                'amounts' => [
                    'fiat' => $funds->total_fiat_amount ?? 0,
                    'crypto' => $funds->total_crypto_amount ?? 0,
                ],
            ],
            'withdraws' => [
                'counts' => [
                    'total' => $withdraws->total_count ?? 0,
                    'statuses' => [
                        'pending' => $withdraws->pending_count ?? 0,
                        'matched' => $withdraws->matched_count ?? 0,
                        'completed' => $withdraws->completed_count ?? 0,
                    ],
                    'new_today' => $withdraws->new_today_count ?? 0,
                ],
                'amounts' => [
                    'fiat' => $withdraws->total_fiat_amount ?? 0,
                    'crypto' => $withdraws->total_crypto_amount ?? 0,
                ],
            ],
            'matches' => [
                'counts' => [
                    'total' => $matches->total_count ?? 0,
                    'statuses' => [
                        'pending' => $matches->pending_count ?? 0,
                        'paid' => $matches->paid_count ?? 0,
                        'confirmed' => $matches->confirmed_count ?? 0,
                        'disputed' => $matches->disputed_count ?? 0,
                    ],
                    'new_today' => $matches->new_today_count ?? 0,
                ],
                'amounts' => [
                    'fiat' => $matches->total_fiat_amount ?? 0,
                    'crypto' => $matches->total_crypto_amount ?? 0,
                ],
            ],
        ], 'Admin dashboard statistics retrieved successfully');
    }

    /**
     * Get user dashboard statistics.
     */
    public function user(): JsonResponse
    {
        $user = auth()->user()->load('stats');
        $userId = $user->id;
        $privilegeService = new UserPrivilegeService;

        // Get all fund statistics in one query
        $funds = Fund::where('user_id', $userId)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status IN ("pending", "matched") THEN 1 ELSE 0 END) as active_count,
                SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_count,
                SUM(CASE WHEN status = "cancelled" THEN 1 ELSE 0 END) as cancelled_count,
                SUM(CASE WHEN currency = "fiat" THEN 1 ELSE 0 END) as fiat_count,
                SUM(CASE WHEN currency = "crypto" THEN 1 ELSE 0 END) as crypto_count,
                SUM(CASE WHEN currency = "fiat" THEN amount ELSE 0 END) as total_fiat_amount,
                SUM(CASE WHEN currency = "fiat" THEN growth_amount ELSE 0 END) as fiat_growth_amount,
                SUM(CASE WHEN currency = "crypto" THEN amount ELSE 0 END) as total_crypto_amount,
                SUM(CASE WHEN currency = "crypto" THEN growth_amount ELSE 0 END) as crypto_growth_amount
            ')
            ->first();

        // Get all withdraw statistics in one query
        $withdraws = Withdraw::where('withdraws.user_id', $userId)
            ->join('funds', 'withdraws.fund_id', '=', 'funds.id')
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN withdraws.status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN withdraws.status = "matched" THEN 1 ELSE 0 END) as in_progress_count,
                SUM(CASE WHEN withdraws.status = "completed" THEN 1 ELSE 0 END) as completed_count,
                SUM(CASE WHEN funds.currency = "fiat" THEN 1 ELSE 0 END) as fiat_count,
                SUM(CASE WHEN funds.currency = "crypto" THEN 1 ELSE 0 END) as crypto_count,
                SUM(CASE WHEN funds.currency = "fiat" THEN withdraws.base_withdrawable_amount ELSE 0 END) as total_fiat_amount,
                SUM(CASE WHEN funds.currency = "crypto" THEN withdraws.base_withdrawable_amount ELSE 0 END) as total_crypto_amount
            ')
            ->first();

        // Get payment method statistics
        $paymentMethods = PaymentMethod::where('user_id', $userId)
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN type = "bank_account" THEN 1 ELSE 0 END) as bank_accounts_count,
                SUM(CASE WHEN type = "crypto_wallet" THEN 1 ELSE 0 END) as crypto_wallets_count
            ')
            ->first();

        // Get payment match statistics
        $paymentMatches = PaymentMatch::where(function ($query) use ($userId) {
            $query->where('fund_user_id', $userId)->orWhere('withdraw_user_id', $userId);
        })
            ->selectRaw('
                COUNT(*) as total_count,
                SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = "paid" THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN status = "confirmed" THEN 1 ELSE 0 END) as confirmed_count
            ')
            ->first();

        return $this->success([
            'profile' => [
                'active_since' => $user->created_at->format('M d, Y'),
                'referral_code' => $user->referral_code,
                'referral_link' => config('app.frontend_url') . '/auth/register?ref=' . $user->referral_code,
                'referee_count' => $user->referee_count,
            ],
            'privileges' => [
                'has_premium_privilege' => $user->hasPremiumPrivilege(),
                'has_elite_privilege' => $user->hasElitePrivilege(),
                'elite_privilege_level' => $user->getElitePrivilegeLevel()->value,
                'maximum_fund_amount' => [
                    'fiat' => $privilegeService->getMaximumFundAmount($user, 'fiat'),
                    'crypto' => $privilegeService->getMaximumFundAmount($user, 'crypto'),
                ],
                'maturity_days' => $privilegeService->getMaturityDays($user),
            ],
            'funds' => [
                'counts' => [
                    'total' => $funds->total_count ?? 0,
                    'statuses' => [
                        'active' => $funds->active_count ?? 0,
                        'completed' => $funds->completed_count ?? 0,
                        'cancelled' => $funds->cancelled_count ?? 0,
                    ],
                    'currencies' => [
                        'fiat' => $funds->fiat_count ?? 0,
                        'crypto' => $funds->crypto_count ?? 0,
                    ],
                ],
                'amounts' => [
                    'fiat' => [
                        'total' => $funds->total_fiat_amount ?? 0,
                        'growth' => $funds->fiat_growth_amount ?? 0,
                    ],
                    'crypto' => [
                        'total' => $funds->total_crypto_amount ?? 0,
                        'growth' => $funds->crypto_growth_amount ?? 0,
                    ],
                ],
            ],
            'withdraws' => [
                'counts' => [
                    'total' => $withdraws->total_count ?? 0,
                    'statuses' => [
                        'pending' => $withdraws->pending_count ?? 0,
                        'in_progress' => $withdraws->in_progress_count ?? 0,
                        'completed' => $withdraws->completed_count ?? 0,
                    ],
                    'currencies' => [
                        'fiat' => $withdraws->fiat_count ?? 0,
                        'crypto' => $withdraws->crypto_count ?? 0,
                    ],
                ],
                'amounts' => [
                    'fiat' => ['total' => $withdraws->total_fiat_amount ?? 0],
                    'crypto' => ['total' => $withdraws->total_crypto_amount ?? 0],
                ],
            ],
            'payment_methods' => [
                'counts' => [
                    'total' => $paymentMethods->total_count ?? 0,
                    'types' => [
                        'bank_accounts' => $paymentMethods->bank_accounts_count ?? 0,
                        'crypto_wallets' => $paymentMethods->crypto_wallets_count ?? 0,
                    ],
                ],
            ],
            'referrals' => [
                'total_referees' => $user->referee_count,
                'amounts' => [
                    'fiat' => [
                        'total' => $user->stats->total_fiat_referral_bonus_earned ?? 0,
                        'available' => $user->stats->available_fiat_referral_bonus ?? 0,
                        'consumed' => $user->stats->consumed_fiat_referral_bonus ?? 0,
                    ],
                    'crypto' => [
                        'total' => $user->stats->total_crypto_referral_bonus_earned ?? 0,
                        'available' => $user->stats->available_crypto_referral_bonus ?? 0,
                        'consumed' => $user->stats->consumed_crypto_referral_bonus ?? 0,
                    ],
                ],
            ],
            'payment_matches' => [
                'counts' => [
                    'total' => $paymentMatches->total_count ?? 0,
                    'statuses' => [
                        'pending' => $paymentMatches->pending_count ?? 0,
                        'paid' => $paymentMatches->paid_count ?? 0,
                        'confirmed' => $paymentMatches->confirmed_count ?? 0,
                    ],
                ],
            ],
        ], 'User dashboard statistics retrieved successfully');
    }
}
