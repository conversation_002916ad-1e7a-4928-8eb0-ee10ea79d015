<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserStat;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateAllRefereeCountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-all-referee-counts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update referee counts for all users with referees';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting referee count update process...');

        try {
            // Get all users who have referred someone (direct referees)
            $users = User::whereIn('id', function ($query) {
                $query->select('referrer_id')
                    ->from('users')
                    ->whereNotNull('referrer_id');
            })->get();

            $this->info('Found ' . $users->count() . ' users with referees to process.');

            $updatedCount = 0;

            foreach ($users as $user) {
                $this->info("Processing user ID: {$user->id}");

                try {
                    $userStat = UserStat::firstOrCreate(['user_id' => $user->id], [
                        'updated_at' => now(),
                    ]);
                    $userStat->updateRefereeCounts();
                    $updatedCount++;
                    $this->info("Updated referee counts for user ID: {$user->id}");
                } catch (\Exception $e) {
                    $this->error("Error updating user ID {$user->id}: " . $e->getMessage());
                    Log::error("Referee count update error for user {$user->id}: " . $e->getMessage());
                }
            }

            $this->info("Referral count update completed. Updated {$updatedCount} users.");

            return 0;

        } catch (\Exception $e) {
            $this->error('Error in referral count update process: ' . $e->getMessage());
            Log::error('Referral count update process error: ' . $e->getMessage());

            return 1;
        }
    }
}
