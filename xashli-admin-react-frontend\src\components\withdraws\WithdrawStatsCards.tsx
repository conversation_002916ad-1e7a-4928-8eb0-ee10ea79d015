import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import {
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
} from 'lucide-react';
import type { WithdrawStats } from '../../types/withdraw';
import { formatCurrencyAmount } from '../../utils/format';
import { LoadingSpinner } from '@/components/ui/loading';

interface WithdrawStatsCardsProps {
  stats: WithdrawStats | null;
  isLoading: boolean;
}

export const WithdrawStatsCards: React.FC<WithdrawStatsCardsProps> = ({
  stats,
  isLoading,
}) => {
  const statsCards = [
    {
      title: 'Total Withdraws',
      count: stats?.overview?.total_count || 0,
      fiatAmount: stats?.overview?.amount?.fiat || 0,
      cryptoAmount: stats?.overview?.amount?.crypto || 0,
      icon: TrendingUp,
      iconColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Pending',
      count: stats?.statuses?.pending?.total_count || 0,
      fiatAmount: stats?.statuses?.pending?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.pending?.amount?.crypto || 0,
      icon: Clock,
      iconColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Matched',
      count: stats?.statuses?.matched?.total_count || 0,
      fiatAmount: stats?.statuses?.matched?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.matched?.amount?.crypto || 0,
      icon: AlertTriangle,
      iconColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Completed',
      count: stats?.statuses?.completed?.total_count || 0,
      fiatAmount: stats?.statuses?.completed?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.completed?.amount?.crypto || 0,
      icon: CheckCircle,
      iconColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Cancelled',
      count: stats?.statuses?.cancelled?.total_count || 0,
      fiatAmount: stats?.statuses?.cancelled?.amount?.fiat || 0,
      cryptoAmount: stats?.statuses?.cancelled?.amount?.crypto || 0,
      icon: XCircle,
      iconColor: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      {statsCards.map(card => {
        const Icon = card.icon;

        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.iconColor}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  card.count.toLocaleString()
                )}
              </div>
              <div className="mt-1 space-y-1">
                <p className="text-xs text-muted-foreground">
                  {formatCurrencyAmount(card.fiatAmount, 'fiat')}
                </p>
                <p className="text-xs text-muted-foreground">
                  {formatCurrencyAmount(card.cryptoAmount, 'crypto')}
                </p>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
