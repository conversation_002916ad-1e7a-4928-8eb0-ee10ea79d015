<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_matching_cycles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->enum('mode', ['auto', 'manual'])->default('auto');
            $table->integer('frequency_hours');
            $table->timestamp('next_run')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_matching_cycles');
    }
};
