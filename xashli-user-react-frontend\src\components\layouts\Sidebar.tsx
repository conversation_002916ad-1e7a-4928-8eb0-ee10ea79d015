import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/utils/cn';
import { useSidebar } from '@/contexts/SidebarContext';
import xashliLogo from '@/assets/xashli_logo.png';
import {
  LayoutDashboard,
  CreditCard,
  ArrowUpDown,
  User,
  ChevronDown,
  Wallet,
  X,
  Users,
  TrendingUp,
} from 'lucide-react';

interface SidebarProps {
  className?: string;
}

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Payment Methods',
    href: '/payment-methods',
    icon: CreditCard,
  },
  {
    title: 'Transactions',
    href: '/transactions',
    icon: ArrowUpDown,
    children: [
      {
        title: 'Funds',
        href: '/funds',
        icon: Wallet,
      },
      {
        title: 'Withdraws',
        href: '/withdraws',
        icon: ArrowUpDown,
      },
    ],
  },
  {
    title: 'Referrals',
    href: '/referrals',
    icon: Users,
    children: [
      {
        title: 'My Referrals',
        href: '/referrals',
        icon: Users,
      },
      {
        title: 'Bonuses',
        href: '/referrals/bonuses',
        icon: TrendingUp,
      },
    ],
  },
  {
    title: 'Profile',
    href: '/profile',
    icon: User,
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = React.useState<string[]>([]);
  const { isOpen, isMobile, close } = useSidebar();

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev =>
      prev.includes(href) ? prev.filter(item => item !== href) : [...prev, href]
    );
  };

  const isActive = (href: string) => {
    // For exact match always return true first
    if (location.pathname === href) {
      return true;
    }

    // Special handling for dashboard - only exact match
    if (href === '/dashboard') {
      return false;
    }

    // For other paths, check if current path starts with href followed by a slash
    return location.pathname.startsWith(href + '/');
  };

  const isExpanded = (href: string) => expandedItems.includes(href);

  React.useEffect(() => {
    // Auto-expand parent items for active routes
    navigationItems.forEach(item => {
      if (item.children) {
        const hasActiveChild = item.children.some(child =>
          isActive(child.href)
        );
        if (hasActiveChild && !expandedItems.includes(item.href)) {
          setExpandedItems(prev => [...prev, item.href]);
        }
      }
    });
  }, [location.pathname]);

  const renderNavItem = (item: NavItem, level = 0) => {
    const Icon = item.icon;
    const hasChildren = item.children && item.children.length > 0;
    const active = isActive(item.href);
    const expanded = isExpanded(item.href);

    return (
      <div key={item.href}>
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.href)}
            className={cn(
              'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors',
              level > 0 && 'ml-6',
              active
                ? 'bg-primary/10 text-primary border-r-2 border-primary'
                : 'text-foreground-secondary hover:text-foreground hover:bg-background-secondary'
            )}
          >
            <div className='flex items-center'>
              <Icon className='mr-3 h-5 w-5' />
              <span>{item.title}</span>
              {item.badge && (
                <span className='ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary'>
                  {item.badge}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform',
                expanded && 'transform rotate-180'
              )}
            />
          </button>
        ) : (
          <Link
            to={item.href}
            onClick={() => isMobile && close()}
            className={cn(
              'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
              level > 0 && 'ml-6',
              active
                ? 'bg-primary/10 text-primary border-r-2 border-primary'
                : 'text-foreground-secondary hover:text-foreground hover:bg-background-secondary'
            )}
          >
            <Icon className='mr-3 h-5 w-5' />
            <span>{item.title}</span>
            {item.badge && (
              <span className='ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary'>
                {item.badge}
              </span>
            )}
          </Link>
        )}

        {hasChildren && expanded && (
          <div className='mt-1 space-y-1'>
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && isOpen && (
        <div
          className='fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden'
          onClick={close}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'flex flex-col h-screen bg-background border-r border-border transition-transform duration-300 ease-in-out z-50',
          isMobile ? 'fixed inset-y-0 left-0 w-64' : 'relative',
          isMobile && !isOpen && '-translate-x-full',
          className
        )}
      >
        {/* Mobile Close Button */}
        {isMobile && (
          <div className='flex items-center justify-end px-4 py-2 lg:hidden'>
            <button
              onClick={close}
              className='text-foreground-secondary hover:text-foreground'
            >
              <X className='h-6 w-6' />
            </button>
          </div>
        )}

        {/* Logo */}
        <div
          className={cn(
            'flex items-center justify-center px-4 border-b border-border',
            isMobile ? 'h-16 py-4' : 'h-16'
          )}
        >
          <Link to='/dashboard' className='flex items-center'>
            <img src={xashliLogo} alt='Xashli Logo' className='h-6 w-auto' />
          </Link>
        </div>

        {/* Navigation */}
        <nav className='flex-1 px-4 py-6 space-y-2 overflow-y-auto'>
          {navigationItems.map(item => renderNavItem(item))}
        </nav>

        {/* Footer */}
        <div className='px-4 py-4 border-t border-border'>
          <div className='text-xs text-foreground-secondary text-center'>
            Xashli User v1.0
          </div>
        </div>
      </div>
    </>
  );
};
