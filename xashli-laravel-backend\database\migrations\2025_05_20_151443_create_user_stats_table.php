<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_stats', function (Blueprint $table) {
            $table->uuid('user_id')->primary();
            $table->decimal('total_fiat_funded', 20, 8)->default(0);
            $table->decimal('total_crypto_funded', 20, 8)->default(0);
            $table->decimal('total_fiat_withdrawn', 20, 8)->default(0);
            $table->decimal('total_crypto_withdrawn', 20, 8)->default(0);
            $table->decimal('total_fiat_referral_bonus_earned', 20, 8)->default(0);
            $table->decimal('total_crypto_referral_bonus_earned', 20, 8)->default(0);
            $table->decimal('available_fiat_referral_bonus', 20, 8)->default(0);
            $table->decimal('available_crypto_referral_bonus', 20, 8)->default(0);
            $table->decimal('consumed_fiat_referral_bonus', 20, 8)->default(0);
            $table->decimal('consumed_crypto_referral_bonus', 20, 8)->default(0);
            $table->integer('referee_count_level1')->default(0);
            $table->integer('referee_count_level2')->default(0);
            $table->integer('referee_count_level3')->default(0);
            $table->decimal('pending_fiat_withdrawal', 20, 8)->default(0);
            $table->decimal('pending_crypto_withdrawal', 20, 8)->default(0);
            $table->timestamp('next_withdraw_eligibility')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_stats');
    }
};
