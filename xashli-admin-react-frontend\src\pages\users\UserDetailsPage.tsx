import { useNavigate, use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  Edit,
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Activity,
  Wallet,
  TrendingUp,
  TrendingDown,
  Users,
  Clock,
  CreditCard,
  Building2,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { userService } from '@/services/user';
import { paymentMethodService } from '@/services/paymentMethod';
import type { User as UserType } from '@/types/user';
import type { ApiResponse } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading';
import { MainLayout } from '@/components/layout/MainLayout';
import { formatCurrencyAmount, formatDate } from '@/utils/format';

export default function UserDetailsPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Fetch user data
  const {
    data: user,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['user', id],
    queryFn: () => userService.getUserById(id!),
    enabled: !!id,
  }) as {
    data: ApiResponse<UserType> | undefined;
    isLoading: boolean;
    error: Error | null;
  };

  // Fetch user's payment methods
  const { data: paymentMethodsResponse, isLoading: isLoadingPaymentMethods } =
    useQuery({
      queryKey: ['payment-methods', { user_id: id }],
      queryFn: () => paymentMethodService.getPaymentMethods({ user_id: id! }),
      enabled: !!id,
    });

  const getUserTypeIcon = (type: string) => {
    switch (type) {
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'user':
        return <User className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getUserTypeBadge = (type: string) => {
    const config = {
      admin: { label: 'Admin', className: 'bg-red-100 text-red-800' },
      user: { label: 'User', className: 'bg-blue-100 text-blue-800' },
    };

    const { label, className } =
      config[type as keyof typeof config] || config.user;

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${className}`}
      >
        {getUserTypeIcon(type)}
        <span className="ml-1">{label}</span>
      </span>
    );
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-1.5"></div>
        Active
      </span>
    ) : (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
        <div className="w-1.5 h-1.5 bg-red-600 rounded-full mr-1.5"></div>
        Inactive
      </span>
    );
  };

  const getPrivilegeBadge = (hasPremium: boolean, hasElite: boolean) => {
    if (hasPremium && hasElite) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Premium + Elite
        </span>
      );
    } else if (hasPremium) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          Premium
        </span>
      );
    } else if (hasElite) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          Elite
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Standard
        </span>
      );
    }
  };

  const getEliteLevelBadge = (level: number) => {
    if (level === 0) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          No Elite Level
        </span>
      );
    }

    const colors = {
      1: 'bg-yellow-100 text-yellow-800',
      2: 'bg-orange-100 text-orange-800',
      3: 'bg-red-100 text-red-800',
      4: 'bg-purple-100 text-purple-800',
    };

    const colorClass =
      colors[level as keyof typeof colors] || 'bg-gray-100 text-gray-800';

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        Elite Level {level}
      </span>
    );
  };

  // Payment method utility functions
  const getPaymentMethodIcon = (type: string) => {
    return type === 'bank' ? Building2 : CreditCard;
  };

  const getPaymentMethodStatusBadge = (status: string) => {
    return status === 'active' ? (
      <Badge className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Active
      </Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800">
        <XCircle className="w-3 h-3 mr-1" />
        Inactive
      </Badge>
    );
  };

  const paymentMethods = paymentMethodsResponse?.data || [];

  if (isLoading) {
    return (
      <MainLayout>
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <LoadingSpinner size="lg" className="mx-auto mb-4" />
              <p className="text-gray-600">Loading user details...</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">
            Error Loading User
          </h2>
          <p className="text-gray-600">
            There was an error loading the user data.
          </p>
          <Button onClick={() => navigate('/users')} className="mt-4">
            Back to Users
          </Button>
        </div>
      </div>
    );
  }
  if (!user) {
    return (
      <MainLayout>
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            {' '}
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                User Not Found
              </h2>
              <p className="text-gray-600">
                The requested user could not be found.
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Destructure user data from API response
  const userData = user.data;

  if (!userData) {
    return (
      <MainLayout>
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            {' '}
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                Invalid User Data
              </h2>
              <p className="text-gray-600">
                The user data could not be loaded properly.
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto space-y-6">
        {' '}
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Details</h1>
            <p className="text-sm text-gray-600">
              View and manage user information
            </p>
          </div>
          <Link to={`/users/${id}/edit`}>
            <Button className="flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Edit User
            </Button>
          </Link>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Full Name
                      </label>
                      <p className="text-sm font-semibold text-gray-900">
                        {userData.full_name}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Email
                      </label>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <p className="text-sm text-gray-900">
                          {userData.email}
                        </p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Phone
                      </label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <p className="text-sm text-gray-900">
                          {userData.phone || 'Not provided'}
                        </p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Privileges
                      </label>
                      <div className="mt-1">
                        {getPrivilegeBadge(
                          userData.has_premium_privilege,
                          userData.has_elite_privilege
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        User Type
                      </label>
                      <div className="mt-1">
                        {getUserTypeBadge(userData.role)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Status
                      </label>
                      <div className="mt-1">
                        {getStatusBadge(userData.is_active)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Elite Level
                      </label>
                      <div className="mt-1">
                        {getEliteLevelBadge(userData.elite_privilege_level)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">
                        Joined
                      </label>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <p className="text-sm text-gray-900">
                          {formatDate(userData.created_at)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Account Statistics
                </CardTitle>
                <CardDescription>
                  Overview of user activity and financial metrics
                </CardDescription>
              </CardHeader>{' '}
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Funding Statistics */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">
                      Funding Activity
                    </h4>

                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Wallet className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">
                          Fiat Funded
                        </span>
                      </div>
                      <span className="text-sm font-bold text-blue-900">
                        {formatCurrencyAmount(
                          userData.stats?.total_fiat_funded || 0,
                          'fiat'
                        )}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Wallet className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">
                          Crypto Funded
                        </span>
                      </div>
                      <span className="text-sm font-bold text-blue-900">
                        {formatCurrencyAmount(
                          userData.stats?.total_crypto_funded || 0,
                          'crypto'
                        )}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-900">
                          Fiat Withdrawn
                        </span>
                      </div>
                      <span className="text-sm font-bold text-green-900">
                        {formatCurrencyAmount(
                          userData.stats?.total_fiat_withdrawn || 0,
                          'fiat'
                        )}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-900">
                          Crypto Withdrawn
                        </span>
                      </div>
                      <span className="text-sm font-bold text-green-900">
                        {formatCurrencyAmount(
                          userData.stats?.total_crypto_withdrawn || 0,
                          'crypto'
                        )}
                      </span>
                    </div>
                  </div>

                  {/* Referral & Bonus Statistics */}
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-3">
                      Referral & Bonuses
                    </h4>

                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-medium text-purple-900">
                          Total Referrals
                        </span>
                      </div>
                      <span className="text-sm font-bold text-purple-900">
                        {userData.referee_count || 0}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Wallet className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-900">
                          Available Fiat Bonus
                        </span>
                      </div>
                      <span className="text-sm font-bold text-yellow-900">
                        {formatCurrencyAmount(
                          userData.stats?.available_fiat_referral_bonus || 0,
                          'fiat'
                        )}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Wallet className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm font-medium text-yellow-900">
                          Available Crypto Bonus
                        </span>
                      </div>
                      <span className="text-sm font-bold text-yellow-900">
                        {formatCurrencyAmount(
                          userData.stats?.available_crypto_referral_bonus || 0,
                          'crypto'
                        )}
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-orange-600" />
                        <span className="text-sm font-medium text-orange-900">
                          Last Active
                        </span>
                      </div>
                      <span className="text-sm font-bold text-orange-900">
                        {userData.last_login
                          ? formatDate(userData.last_login)
                          : 'Never'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  Payment Methods
                </CardTitle>
                <CardDescription>
                  User's registered payment methods for transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingPaymentMethods ? (
                  <div className="flex items-center justify-center py-8">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2 text-sm text-gray-600">
                      Loading payment methods...
                    </span>
                  </div>
                ) : paymentMethods.length === 0 ? (
                  <div className="text-center py-8">
                    <Wallet className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-sm text-gray-500">
                      No payment methods registered
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {paymentMethods.map(method => {
                      const Icon = getPaymentMethodIcon(method.type);
                      return (
                        <div
                          key={method.id}
                          className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-gray-100 rounded-lg">
                              <Icon className="h-4 w-4 text-gray-600" />
                            </div>
                            <div>
                              {method.type === 'bank' ? (
                                <div>
                                  <p className="font-medium text-sm">
                                    {method.bank_name || 'Bank Account'}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {method.account_name} • ••••
                                    {method.account_number?.slice(-4)}
                                    {method.bank_code &&
                                      ` • ${method.bank_code}`}
                                  </p>
                                </div>
                              ) : (
                                <div>
                                  <p className="font-medium text-sm">
                                    {method.crypto_network || 'Crypto Wallet'}
                                  </p>
                                  <p className="text-xs text-gray-500 font-mono">
                                    {method.wallet_address?.slice(0, 6)}...
                                    {method.wallet_address?.slice(-4)}
                                  </p>
                                </div>
                              )}
                              <p className="text-xs text-gray-400">
                                Added {formatDate(method.created_at)}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {getPaymentMethodStatusBadge(method.status)}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link to={`/users/${id}/edit`} className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit User
                  </Button>
                </Link>

                <Link
                  to={`/transactions/funds?user_id=${userData.id}`}
                  className="block"
                >
                  <Button variant="outline" className="w-full justify-start">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Funds
                  </Button>
                </Link>

                <Link
                  to={`/transactions/withdraws?user_id=${userData.id}`}
                  className="block"
                >
                  <Button variant="outline" className="w-full justify-start">
                    <TrendingDown className="h-4 w-4 mr-2" />
                    View Withdrawals
                  </Button>
                </Link>
                {userData.referral_code && (
                  <Link to={`/users/${id}/referrals`} className="block">
                    <Button variant="outline" className="w-full justify-start">
                      <Users className="h-4 w-4 mr-2" />
                      View Referrals
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
            {/* User Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>System Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">User ID</span>
                  <span className="font-mono text-gray-900">
                    #{userData.id}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Created</span>
                  <span className="text-gray-900">
                    {formatDate(userData.created_at)}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Updated</span>
                  <span className="text-gray-900">
                    {formatDate(userData.updated_at)}
                  </span>
                </div>

                {userData.email_verified_at && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Email Verified</span>
                    <span className="text-green-600">Verified</span>
                  </div>
                )}

                {userData.referral_code && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Referral Code</span>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                      {userData.referral_code}
                    </code>
                  </div>
                )}
              </CardContent>
            </Card>{' '}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
