import { useState } from 'react';
import { authService } from '../../services';
import { showToast } from '../../utils/toast';

interface ForgotPasswordData {
  email: string;
}

export const useForgotPassword = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleForgotPassword = async (data: ForgotPasswordData) => {
    setIsLoading(true);

    try {
      const response = await authService.forgotPassword(data);

      if (response.status === 'success') {
        setIsSubmitted(true);
        showToast.success('Password reset instructions sent to your email');
      } else {
        showToast.error(
          response.message || 'Failed to send reset email. Please try again.'
        );
      }
    } catch (error: any) {
      showToast.error(
        error.message ||
          'Network error. Please check your connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async (email: string) => {
    if (!email) return;

    setIsLoading(true);

    try {
      const response = await authService.forgotPassword({ email });

      if (response.status === 'success') {
        showToast.success('Reset instructions sent again');
      } else {
        showToast.error(
          response.message || 'Failed to resend email. Please try again.'
        );
      }
    } catch (error: any) {
      showToast.error(
        error.message ||
          'Network error. Please check your connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const resetSubmittedState = () => {
    setIsSubmitted(false);
  };

  return {
    isLoading,
    isSubmitted,
    handleForgotPassword,
    handleResend,
    resetSubmittedState,
  };
};
