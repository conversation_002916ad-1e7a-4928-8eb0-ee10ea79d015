import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import {
  CreditCard,
  Wallet,
  Building2,
  Edit,
  Settings,
  CheckCircle,
  XCircle,
  Save,
  X,
} from 'lucide-react';
import { paymentMethodService } from '../../services/paymentMethod';
import type {
  PaymentMethod,
  PaymentMethodUpdateData,
} from '../../types/paymentMethod';
import { MainLayout } from '../../components/layout/MainLayout';
import { LoadingSpinner } from '../../components/ui/loading';
import { formatDate } from '../../utils/format';
import { toast } from 'sonner';

export const PlatformPaymentMethodsPage: React.FC = () => {
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(
    null
  );
  const [editFormData, setEditFormData] = useState<PaymentMethodUpdateData>({});
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch platform payment methods
  const {
    data: paymentMethodsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['payment-methods', { only_admins_payment_methods: 'true' }],
    queryFn: () =>
      paymentMethodService.getPaymentMethods({
        only_admins_payment_methods: 'true',
      }),
  });

  // Update payment method mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: PaymentMethodUpdateData }) =>
      paymentMethodService.updatePaymentMethod(id, data),
    onSuccess: () => {
      toast.success('Payment method updated successfully');
      queryClient.invalidateQueries({ queryKey: ['payment-methods'] });
      setEditingMethod(null);
      setEditFormData({});
      setIsDialogOpen(false);
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || 'Failed to update payment method'
      );
    },
  });

  const paymentMethods = paymentMethodsResponse?.data || [];

  const getTypeIcon = (type: string) => {
    return type === 'bank' ? Building2 : Wallet;
  };

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      inactive: { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const statusConfig = config[status as keyof typeof config] || config.active;
    const Icon = statusConfig.icon;

    return (
      <Badge className={`${statusConfig.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };
  const handleEdit = (method: PaymentMethod) => {
    setEditingMethod(method);
    setEditFormData({
      type: method.type,
      bank_name: method.bank_name || '',
      account_number: method.account_number || '',
      account_name: method.account_name || '',
      wallet_address: method.wallet_address || '',
      crypto_network: method.crypto_network || '',
      bank_code: method.bank_code || '',
      status: method.status,
    });
    setIsDialogOpen(true);
  };

  const handleSave = () => {
    if (!editingMethod) return;

    updateMutation.mutate({
      id: editingMethod.id,
      data: editFormData,
    });
  };

  const handleCancel = () => {
    setEditingMethod(null);
    setEditFormData({});
    setIsDialogOpen(false);
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-96">
          <LoadingSpinner size="lg" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <Alert>
            <AlertDescription>
              Error loading platform payment methods. Please try again.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold">Platform Payment Methods</h1>
            <p className="text-gray-600">
              Manage payment methods used for platform operations
            </p>
          </div>
        </div>

        {/* Platform Context Banner */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="flex items-center gap-4 pt-6">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
              <Settings className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Platform Payment Methods
              </h3>
              <p className="text-sm text-blue-700">
                These payment methods are used for platform fee collection and
                withdrawal payouts
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods List */}
        {paymentMethods.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <CreditCard className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Platform Payment Methods Found
              </h3>
              <p className="text-gray-600 text-center">
                No payment methods have been configured for platform operations.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {paymentMethods.map(paymentMethod => {
              const TypeIcon = getTypeIcon(paymentMethod.type);

              return (
                <Card
                  key={paymentMethod.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <TypeIcon className="h-5 w-5 text-gray-600" />
                        <CardTitle className="text-lg capitalize">
                          {paymentMethod.type} Account
                        </CardTitle>{' '}
                      </div>
                      {getStatusBadge(paymentMethod.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {' '}
                    {/* Bank Details */}
                    {paymentMethod.type === 'bank' && (
                      <>
                        <div>
                          <p className="text-sm font-medium text-gray-500">
                            Bank Name
                          </p>
                          <p className="text-sm">{paymentMethod.bank_name}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">
                            Account Number
                          </p>
                          <p className="text-sm font-mono">
                            {paymentMethod.account_number}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">
                            Account Name
                          </p>
                          <p className="text-sm">
                            {paymentMethod.account_name}
                          </p>
                        </div>
                        {paymentMethod.bank_code && (
                          <div>
                            <p className="text-sm font-medium text-gray-500">
                              Bank Code
                            </p>
                            <p className="text-sm font-mono">
                              {paymentMethod.bank_code}
                            </p>
                          </div>
                        )}
                      </>
                    )}
                    {/* Crypto Details */}
                    {paymentMethod.type === 'crypto' && (
                      <>
                        <div>
                          <p className="text-sm font-medium text-gray-500">
                            Network
                          </p>
                          <p className="text-sm">
                            {paymentMethod.crypto_network}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">
                            Wallet Address
                          </p>
                          <p className="text-sm font-mono break-all text-xs">
                            {paymentMethod.wallet_address}
                          </p>
                        </div>
                      </>
                    )}
                    {/* Owner Info */}
                    {paymentMethod.user && (
                      <div className="pt-2 border-t">
                        <p className="text-sm font-medium text-gray-500">
                          Managed By
                        </p>
                        <p className="text-sm">
                          {paymentMethod.user.full_name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {paymentMethod.user.email}
                        </p>
                      </div>
                    )}
                    {/* Metadata */}
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-xs text-gray-500">
                          <span>
                            Created {formatDate(paymentMethod.created_at)}
                          </span>
                        </div>
                        <Dialog
                          open={isDialogOpen}
                          onOpenChange={setIsDialogOpen}
                        >
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(paymentMethod)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle>Edit Payment Method</DialogTitle>
                              <DialogDescription>
                                Update the details for this platform payment
                                method.
                              </DialogDescription>
                            </DialogHeader>
                            {editingMethod && (
                              <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-4 items-center gap-4">
                                  <Label
                                    htmlFor="status"
                                    className="text-right"
                                  >
                                    Status
                                  </Label>
                                  <Select
                                    value={editFormData.status}
                                    onValueChange={(
                                      value: 'active' | 'inactive'
                                    ) =>
                                      setEditFormData(prev => ({
                                        ...prev,
                                        status: value,
                                      }))
                                    }
                                  >
                                    <SelectTrigger className="col-span-3">
                                      <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="active">
                                        Active
                                      </SelectItem>
                                      <SelectItem value="inactive">
                                        Inactive
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>

                                {editingMethod.type === 'bank' && (
                                  <>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="bank_name"
                                        className="text-right"
                                      >
                                        Bank Name
                                      </Label>
                                      <Input
                                        id="bank_name"
                                        value={editFormData.bank_name || ''}
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            bank_name: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="account_number"
                                        className="text-right"
                                      >
                                        Account Number
                                      </Label>
                                      <Input
                                        id="account_number"
                                        value={
                                          editFormData.account_number || ''
                                        }
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            account_number: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                      />
                                    </div>{' '}
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="account_name"
                                        className="text-right"
                                      >
                                        Account Name
                                      </Label>
                                      <Input
                                        id="account_name"
                                        value={editFormData.account_name || ''}
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            account_name: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="bank_code"
                                        className="text-right"
                                      >
                                        Bank Code
                                      </Label>
                                      <Input
                                        id="bank_code"
                                        value={editFormData.bank_code || ''}
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            bank_code: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                        placeholder="Optional bank code"
                                      />
                                    </div>
                                  </>
                                )}

                                {editingMethod.type === 'crypto' && (
                                  <>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="crypto_network"
                                        className="text-right"
                                      >
                                        Network
                                      </Label>
                                      <Input
                                        id="crypto_network"
                                        value={
                                          editFormData.crypto_network || ''
                                        }
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            crypto_network: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                      />
                                    </div>
                                    <div className="grid grid-cols-4 items-center gap-4">
                                      <Label
                                        htmlFor="wallet_address"
                                        className="text-right"
                                      >
                                        Wallet Address
                                      </Label>
                                      <Input
                                        id="wallet_address"
                                        value={
                                          editFormData.wallet_address || ''
                                        }
                                        onChange={e =>
                                          setEditFormData(prev => ({
                                            ...prev,
                                            wallet_address: e.target.value,
                                          }))
                                        }
                                        className="col-span-3"
                                      />
                                    </div>
                                  </>
                                )}
                              </div>
                            )}

                            <DialogFooter>
                              <Button variant="outline" onClick={handleCancel}>
                                <X className="h-4 w-4 mr-2" />
                                Cancel
                              </Button>
                              <Button
                                onClick={handleSave}
                                disabled={updateMutation.isPending}
                              >
                                <Save className="h-4 w-4 mr-2" />
                                {updateMutation.isPending
                                  ? 'Saving...'
                                  : 'Save Changes'}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </MainLayout>
  );
};
