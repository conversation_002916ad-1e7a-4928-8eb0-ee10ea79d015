// Common types used across the application

// Currency type
export type Currency = 'fiat' | 'crypto';

// API Response types
export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message: string;
  data: T | null;
}

export interface ApiError {
  status: 'error';
  message: string;
  data: {
    errors?: Record<string, string[]>;
  } | null;
}

// Pagination types
export interface PaginationMeta {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number | null;
  to: number | null;
}

// Common utility types
export type Status = 'success' | 'error' | 'loading' | 'idle';

export interface BaseEntity {
  id: string | number;
  created_at: string;
  updated_at: string;
}

// HTTP method types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// Common filter base interface
export interface BaseFilters {
  page?: number;
  per_page?: number;
  search?: string;
  start_date?: string;
  end_date?: string;
}
