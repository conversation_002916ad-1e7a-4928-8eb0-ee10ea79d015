import { Upload, X } from 'lucide-react';
import { useState } from 'react';
import { Button } from '../ui/Button';
import { Badge } from '../ui';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { formatCurrencyAmount } from '../../utils/format';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { Currency } from '@/types';

interface PaymentProofUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (file: File) => Promise<void>;
  paymentMatch: PaymentMatch;
  currency: string;
  loading?: boolean;
}

export function PaymentProofUploadModal({
  isOpen,
  onClose,
  onSubmit,
  paymentMatch,
  currency,
  loading = false,
}: PaymentProofUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const getPaymentMatchStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-warning/20 text-warning border-warning/30',
      paid: 'bg-primary/20 text-primary border-primary/30',
      confirmed: 'bg-success/20 text-success border-success/30',
      disputed: 'bg-destructive/20 text-destructive border-destructive/30',
      cancelled: 'bg-muted/20 text-muted-foreground border-muted/30',
    };
    return (
      variants[status as keyof typeof variants] ||
      'bg-muted/20 text-muted-foreground border-muted/30'
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) return;

    await onSubmit(selectedFile);
    setSelectedFile(null);
  };

  const handleClose = () => {
    setSelectedFile(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
      <div className='bg-background rounded-lg max-w-md w-full p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-foreground'>
            Upload Payment Proof
          </h2>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleClose}
            disabled={loading}
          >
            <X className='h-4 w-4' />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4'>
          <div className='bg-background-secondary rounded-lg p-4 mb-4'>
            <div className='flex items-center gap-3 mb-2'>
              <Upload className='h-5 w-5 text-primary' />
              <span className='font-medium text-foreground'>Match Details</span>
            </div>
            <div className='text-sm space-y-1'>
              <div className='flex justify-between'>
                <span className='text-foreground-secondary'>Amount:</span>
                <span className='font-medium text-foreground'>
                  {formatCurrencyAmount(
                    paymentMatch.amount || 0,
                    currency as Currency
                  )}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-foreground-secondary'>Status:</span>
                <Badge
                  className={getPaymentMatchStatusBadge(paymentMatch.status)}
                >
                  {paymentMatch.status}
                </Badge>
              </div>
            </div>
          </div>

          <div>
            <label className='block text-sm font-medium text-foreground mb-2'>
              Payment Proof Image
            </label>
            <div className='space-y-2'>
              <input
                type='file'
                accept='image/*'
                onChange={e => setSelectedFile(e.target.files?.[0] || null)}
                className='hidden'
                id='payment-proof-upload'
                disabled={loading}
              />
              <label
                htmlFor='payment-proof-upload'
                className={`flex items-center justify-center gap-2 w-full p-2 border-2 border-dashed border-border hover:border-primary/50 rounded-lg cursor-pointer transition-colors bg-background-secondary hover:bg-background-tertiary ${
                  loading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <Upload className='h-4 w-4 text-primary' />
                <span className='text-foreground'>
                  {selectedFile ? selectedFile.name : 'Choose image file'}
                </span>
              </label>
            </div>
            <p className='text-xs text-foreground-secondary mt-1'>
              Upload a screenshot or photo of your payment confirmation
            </p>
          </div>

          <div className='flex gap-3'>
            <Button
              type='button'
              variant='outline'
              className='flex-1'
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              className='flex-1'
              disabled={loading || !selectedFile}
            >
              {loading ? <LoadingSpinner size='sm' className='mr-2' /> : null}
              Upload Proof
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
