<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Withdraw extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'fund_id',
        'base_withdrawable_amount',
        'available_referral_bonus',
        'withdrawable_referral_bonus',
        'total_withdrawable_amount',
        'amount_matched',
        'status',
        'payment_method_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'base_withdrawable_amount' => 'decimal:8',
        'available_referral_bonus' => 'decimal:8',
        'withdrawable_referral_bonus' => 'decimal:8',
        'total_withdrawable_amount' => 'decimal:8',
        'amount_matched' => 'decimal:8',
        'status' => 'string',
    ];

    /**
     * Get the user that owns the withdraw.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the fund for the withdraw.
     */
    public function fund(): BelongsTo
    {
        return $this->belongsTo(Fund::class);
    }

    /**
     * Get the payment method for the withdraw.
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    /**
     * Get the payment matches for the withdraw.
     */
    public function paymentMatches(): HasMany
    {
        return $this->hasMany(PaymentMatch::class);
    }

    /**
     * Get the platform fee record for this withdrawal (if it's an admin fee withdrawal).
     */
    public function platformFee(): HasOne
    {
        return $this->hasOne(PlatformFee::class, 'fee_withdraw_id');
    }

    /**
     * Check if this withdraw is for platform fee collection.
     */
    public function isAdminFeeWithdraw(): bool
    {
        return $this->platformFee !== null;
    }

    /**
     * Check if this is a regular user withdraw.
     */
    public function isUserWithdraw(): bool
    {
        return $this->platformFee === null;
    }

    /**
     * Check if the withdraw is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the withdraw is matched.
     */
    public function isMatched(): bool
    {
        return $this->status === 'matched';
    }

    /**
     * Check if the withdraw is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Get the total required to fully match this withdraw.
     */
    public function getTotalToFullyMatchAttribute(): float
    {
        return (float) $this->total_withdrawable_amount;
    }

    /**
     * Check if the withdraw is fully matched.
     */
    public function isFullyMatched(): bool
    {
        // Fully matched if amount_matched equals total_withdrawable_amount
        return $this->amount_matched >= $this->total_withdrawable_amount;
    }

    /**
     * Check if all non-cancelled payment matches for the withdraw are confirmed
     * and update withdraw status accordingly.
     */
    public function checkAndUpdateCompletion(): bool
    {
        // Check if withdraw is fully matched
        if (! $this->isFullyMatched() || $this->status === 'completed') {
            return false;
        }

        // Get all non-cancelled payment matches for this withdraw
        $nonCancelledMatches = $this->paymentMatches()
            ->where('status', '!=', 'cancelled')
            ->get();

        // Check if all non-cancelled matches are confirmed
        $allConfirmed = $nonCancelledMatches->every(function ($match) {
            return $match->status === 'confirmed';
        });

        // Only mark withdraw as completed if all non-cancelled matches are confirmed
        if ($allConfirmed && $nonCancelledMatches->count() > 0) {
            $this->status = 'completed';
            $this->save();

            return true;
        }

        return false;
    }

    /**
     * Get the referral withdraw record for this withdrawal.
     */
    public function referralWithdraw(): HasOne
    {
        return $this->hasOne(ReferralWithdraw::class);
    }
}
