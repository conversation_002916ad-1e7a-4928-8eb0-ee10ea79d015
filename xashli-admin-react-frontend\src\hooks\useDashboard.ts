import { useQuery } from '@tanstack/react-query';
import { dashboardService } from '../services/dashboard';

// Query Keys
export const dashboardKeys = {
  admin: ['dashboard', 'admin'] as const,
} as const;

// Hook for Admin Dashboard
export const useAdminDashboard = () => {
  return useQuery({
    queryKey: dashboardKeys.admin,
    queryFn: dashboardService.getAdminDashboard,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
  });
};
