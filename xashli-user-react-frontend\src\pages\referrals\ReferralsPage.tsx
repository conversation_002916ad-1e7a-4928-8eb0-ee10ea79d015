import { useState, useEffect } from 'react';
import { Users, BarChart3, RefreshCw } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ReferralOverview, RefereesList } from '../../components/referrals';
import { useReferrals } from '../../hooks/referrals';

export function ReferralsPage() {
  const {
    referralInfo,
    referees,
    loading,
    loadingReferees,
    fetchReferees,
    copyReferralLink,
    copyReferralCode,
  } = useReferrals();

  const [activeTab, setActiveTab] = useState<'overview' | 'referees'>(
    'overview'
  );

  useEffect(() => {
    if (activeTab === 'referees' && !referees) {
      fetchReferees();
    }
  }, [activeTab, referees, fetchReferees]);

  if (loading && !referralInfo) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  if (!referralInfo) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center py-8'>
          <p className='text-foreground-secondary'>
            Unable to load referral information.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>My Referrals</h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your referral network and track your rewards
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => {
              if (activeTab === 'referees') {
                fetchReferees();
              }
            }}
            disabled={loadingReferees}
            className='flex items-center gap-2'
          >
            <RefreshCw
              className={`h-4 w-4 ${loadingReferees ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className='flex border-b border-border mb-6'>
        <button
          onClick={() => setActiveTab('overview')}
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'overview'
              ? 'border-primary text-primary'
              : 'border-transparent text-foreground-secondary hover:text-foreground'
          }`}
        >
          <div className='flex items-center gap-2'>
            <BarChart3 className='h-4 w-4' />
            Overview
          </div>
        </button>
        <button
          onClick={() => setActiveTab('referees')}
          className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'referees'
              ? 'border-primary text-primary'
              : 'border-transparent text-foreground-secondary hover:text-foreground'
          }`}
        >
          <div className='flex items-center gap-2'>
            <Users className='h-4 w-4' />
            My Referees ({referralInfo.referee_count})
          </div>
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <ReferralOverview
          referralInfo={referralInfo}
          onCopyLink={copyReferralLink}
          onCopyCode={copyReferralCode}
        />
      )}

      {activeTab === 'referees' && (
        <div>
          {loadingReferees && !referees ? (
            <div className='flex items-center justify-center min-h-[200px]'>
              <LoadingSpinner size='lg' />
            </div>
          ) : referees ? (
            <RefereesList referees={referees} />
          ) : (
            <div className='text-center py-8'>
              <p className='text-foreground-secondary'>
                Unable to load referees information.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
