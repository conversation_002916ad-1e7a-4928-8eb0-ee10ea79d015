import type { User } from './auth';

// Profile update request - matches backend validation rules
export interface UpdateProfileRequest {
  full_name?: string;
  email?: string;
  phone?: string;
  password?: string;
}

// Legacy profile form data interface - use schema-based ProfileFormData from schemas/profile.ts instead

// Profile image upload
export interface ProfileImageUpload {
  file: File;
}

// Profile response types
export interface ProfileUpdateResponse {
  user: User;
  message: string;
}

export interface ProfileImageUploadResponse {
  user: User;
  profile_image_url: string;
  message: string;
}

// Profile error types
export interface ProfileError {
  field: string;
  message: string;
}

export interface ProfileValidationErrors {
  errors: Record<string, string[]>;
  message: string;
}
