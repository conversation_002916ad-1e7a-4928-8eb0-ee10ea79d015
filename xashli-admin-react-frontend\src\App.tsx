import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'sonner';
import { LoginPage, ForgotPasswordPage, ResetPasswordPage } from './pages/auth';
import { DashboardPage } from './pages/DashboardPage';
// User Management Pages
import { UsersListPage } from './pages/users/UsersListPage';
import UserEditPage from './pages/users/UserEditPage';
import UserDetailsPage from './pages/users/UserDetailsPage';
import ReferralsPage from './pages/users/ReferralsPage';
// Fund Management Pages
import { FundsPage } from './pages/funds/FundsPage';
import { FundDetailsPage } from './pages/funds/FundDetailsPage';
// Withdraw Management Pages
import { WithdrawsPage } from './pages/withdraws/WithdrawsPage';
import { WithdrawDetailsPage } from './pages/withdraws/WithdrawDetailsPage';
// Payment Match Management Pages
import PaymentMatchesPage from './pages/payment-matches/PaymentMatchesPage';
import PaymentMatchDetailsPage from './pages/payment-matches/PaymentMatchDetailsPage';
import ManualMatchingPage from './pages/payment-matches/ManualMatchingPage';
import PaymentMatchStatisticsPage from './pages/payment-matches/PaymentMatchStatisticsPage';
// Dispute Management Pages
import { DisputesPage, DisputeDetailsPage } from './pages/disputes';
// Platform Fee Pages
import { PlatformFeeCollectionsPage } from './pages/platform-fees';
// Platform Settings Pages
import { SettingsCategoryPage, FundingSchedulePage } from './pages/settings';
import { PlatformPaymentMethodsPage } from './pages/settings/PlatformPaymentMethodsPage';
// Activity Log Pages
import {
  ActivityLogsPage,
  ActivityLogDetailsPage,
} from './pages/activity-logs';
import { ProtectedRoute } from './components/common';
import { AuthProvider, useAuth, SidebarProvider } from './contexts';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

function AppContent() {
  const { isAuthenticated } = useAuth();

  return (
    <QueryClientProvider client={queryClient}>
      <SidebarProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              {/* Public Routes */}
              <Route
                path="/auth/login"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <LoginPage />
                  )
                }
              />
              <Route
                path="/auth/forgot-password"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <ForgotPasswordPage />
                  )
                }
              />
              <Route
                path="/auth/reset-password"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <ResetPasswordPage />
                  )
                }
              />

              {/* Protected Routes */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <DashboardPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users"
                element={
                  <ProtectedRoute>
                    <UsersListPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users/:id"
                element={
                  <ProtectedRoute>
                    <UserDetailsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users/:id/edit"
                element={
                  <ProtectedRoute>
                    <UserEditPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/users/:id/referrals"
                element={
                  <ProtectedRoute>
                    <ReferralsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/funds"
                element={
                  <ProtectedRoute>
                    <FundsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/funds/:id"
                element={
                  <ProtectedRoute>
                    <FundDetailsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/withdraws"
                element={
                  <ProtectedRoute>
                    <WithdrawsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/withdraws/:id"
                element={
                  <ProtectedRoute>
                    <WithdrawDetailsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/payment-matches"
                element={
                  <ProtectedRoute>
                    <PaymentMatchesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/payment-matches/:id"
                element={
                  <ProtectedRoute>
                    <PaymentMatchDetailsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/payment-matches-statistics"
                element={
                  <ProtectedRoute>
                    <PaymentMatchStatisticsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transactions/manual-matching"
                element={
                  <ProtectedRoute>
                    <ManualMatchingPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/disputes"
                element={
                  <ProtectedRoute>
                    <DisputesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/disputes/:id"
                element={
                  <ProtectedRoute>
                    <DisputeDetailsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/platform-fees"
                element={
                  <ProtectedRoute>
                    <PlatformFeeCollectionsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/:category"
                element={
                  <ProtectedRoute>
                    <SettingsCategoryPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/payment-methods"
                element={
                  <ProtectedRoute>
                    <PlatformPaymentMethodsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings/funding-schedule"
                element={
                  <ProtectedRoute>
                    <FundingSchedulePage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/activity-logs"
                element={
                  <ProtectedRoute>
                    <ActivityLogsPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/activity-logs/:id"
                element={
                  <ProtectedRoute>
                    <ActivityLogDetailsPage />
                  </ProtectedRoute>
                }
              />

              {/* Catch all route */}
              <Route
                path="*"
                element={
                  isAuthenticated ? (
                    <Navigate to="/dashboard" replace />
                  ) : (
                    <Navigate to="/auth/login" replace />
                  )
                }
              />
            </Routes>
          </div>
        </Router>

        {/* Toast Notifications */}
        <Toaster position="top-right" richColors closeButton />

        {/* React Query Devtools */}
        <ReactQueryDevtools initialIsOpen={false} />
      </SidebarProvider>
    </QueryClientProvider>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
