import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService, tokenStorage } from '../../services';
import { showToast } from '../../utils/toast';

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  referrerCode?: string;
  acceptTerms: boolean;
}

export const useRegister = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleRegister = async (data: RegisterData) => {
    setIsLoading(true);

    try {
      const response = await authService.register({
        full_name: `${data.firstName} ${data.lastName}`,
        email: data.email,
        phone: data.phone,
        password: data.password,
        password_confirmation: data.confirmPassword,
        referrer_code: data.referrerCode,
      });

      if (response.status === 'success' && response.data) {
        // Store tokens
        tokenStorage.setAccessToken(response.data.authorization.access_token);
        tokenStorage.setRefreshToken(response.data.authorization.refresh_token);

        // Show success toast
        showToast.auth.registrationSuccess();

        // Redirect to verification page with email after 2 seconds
        setTimeout(() => {
          navigate(
            `/auth/verification?email=${encodeURIComponent(data.email)}`
          );
        }, 2000);
      } else {
        showToast.error(
          response.message || 'Registration failed. Please try again.'
        );
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      showToast.error(
        error.response?.data?.message ||
          'An unexpected error occurred. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleRegister,
  };
};
