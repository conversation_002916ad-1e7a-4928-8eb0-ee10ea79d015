import {
  User,
  X,
  CreditCard,
  Wallet,
  Phone,
  Copy,
  Check,
  Send,
} from 'lucide-react';
import { Button } from '../ui/Button';
import { useCopyToClipboard } from '../../utils/copy';
import { formatCurrencyAmount } from '../../utils/format';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { Currency } from '@/types';

interface WithdrawerModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentMatch: PaymentMatch;
  currency: string;
  index?: number;
}

export function WithdrawerModal({
  isOpen,
  onClose,
  paymentMatch,
  currency,
  index = 1,
}: WithdrawerModalProps) {
  const { copied, copy } = useCopyToClipboard();

  if (!isOpen || !paymentMatch.withdraw_user || !paymentMatch.payment_method) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 overflow-y-auto'>
      <div className='min-h-full flex items-center justify-center p-4'>
        <div className='bg-background rounded-lg max-w-md w-full p-6 border border-border'>
          <div className='flex items-center justify-between mb-4'>
            <div className='flex items-center gap-3'>
              <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
                <User className='h-5 w-5 text-primary' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-foreground'>
                  Withdrawer Information
                </h3>
                <p className='text-sm text-foreground-secondary'>
                  Payment recipient details
                </p>
              </div>
            </div>
            <Button
              variant='ghost'
              size='sm'
              onClick={onClose}
              className='h-8 w-8 p-0'
            >
              <X className='h-4 w-4' />
            </Button>
          </div>

          {/* Payment Amount */}
          <div className='bg-background-secondary rounded-lg p-4 mb-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <div className='w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center'>
                  <Send className='h-4 w-4 text-primary' />
                </div>
                <span className='text-sm font-medium text-foreground'>
                  Payment Amount
                </span>
              </div>
              <div className='text-right'>
                <div className='text-lg font-bold text-primary'>
                  {formatCurrencyAmount(
                    paymentMatch.amount || 0,
                    currency as Currency
                  )}
                </div>
                <div className='text-xs text-foreground-secondary'>
                  Match #{index}
                </div>
              </div>
            </div>
          </div>

          {/* Withdrawer Contact Information - Hide for admin withdrawals */}
          {paymentMatch.withdraw_user && !paymentMatch.is_admin_withdraw && (
            <div className='bg-background-secondary rounded-lg p-4 mb-4'>
              <div className='flex items-center gap-2 mb-3'>
                <User className='h-4 w-4 text-primary' />
                <span className='text-sm font-medium text-foreground'>
                  Recipient Information
                </span>
              </div>

              <div className='space-y-3'>
                {/* Display withdraw user's name */}
                <div>
                  <label className='text-xs text-foreground-secondary'>
                    Full Name
                  </label>
                  <div className='text-sm font-medium text-foreground'>
                    {paymentMatch.withdraw_user.full_name}
                  </div>
                </div>

                {/* Display withdraw user's phone number */}
                {paymentMatch.withdraw_user.phone && (
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Phone Number
                    </label>
                    <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                      <Phone className='h-3 w-3 text-foreground-secondary flex-shrink-0' />
                      <span className='text-sm font-medium text-foreground flex-1'>
                        {paymentMatch.withdraw_user.phone}
                      </span>
                      <button
                        onClick={() => {
                          if (paymentMatch.withdraw_user?.phone) {
                            copy(
                              paymentMatch.withdraw_user.phone,
                              'Phone number copied!',
                              'Failed to copy phone number'
                            );
                          }
                        }}
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                        title='Copy phone number'
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>
                )}

                {/* Display withdraw user's email if available */}
                {paymentMatch.withdraw_user.email && (
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Email Address
                    </label>
                    <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                      <span className='text-sm font-medium text-foreground flex-1'>
                        {paymentMatch.withdraw_user.email}
                      </span>
                      <button
                        onClick={() => {
                          if (paymentMatch.withdraw_user?.email) {
                            copy(
                              paymentMatch.withdraw_user.email,
                              'Email address copied!',
                              'Failed to copy email address'
                            );
                          }
                        }}
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                        title='Copy email address'
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Payment Method Details */}
          <div className='bg-background-secondary rounded-lg p-4 mb-4'>
            <div className='flex items-center gap-2 mb-3'>
              {paymentMatch.payment_method.type === 'bank' ? (
                <CreditCard className='h-4 w-4 text-primary' />
              ) : (
                <Wallet className='h-4 w-4 text-primary' />
              )}
              <span className='text-sm font-medium text-foreground'>
                Payment Details
              </span>
            </div>

            <div className='space-y-3'>
              {paymentMatch.payment_method.type === 'bank' ? (
                /* Bank Account Details */
                <>
                  <div className='grid grid-cols-2 gap-2'>
                    <div>
                      <label className='text-xs text-foreground-secondary'>
                        Bank Name
                      </label>
                      <div className='text-sm font-medium text-foreground'>
                        {paymentMatch.payment_method.bank_name}
                      </div>
                    </div>
                    <div>
                      <label className='text-xs text-foreground-secondary'>
                        Account Name
                      </label>
                      <div className='text-sm font-medium text-foreground'>
                        {paymentMatch.payment_method.account_name}
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Account Number
                    </label>
                    <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                      <span className='text-sm font-mono font-medium text-foreground flex-1'>
                        {paymentMatch.payment_method.account_number}
                      </span>
                      <button
                        onClick={() =>
                          paymentMatch.payment_method?.account_number &&
                          copy(
                            paymentMatch.payment_method.account_number,
                            'Account number copied!',
                            'Failed to copy account number'
                          )
                        }
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                        title='Copy account number'
                        disabled={!paymentMatch.payment_method.account_number}
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Bank Transfer Narration */}
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Transfer Description/Narration
                    </label>
                    <div className='flex items-start gap-2 p-2 bg-background-tertiary rounded'>
                      <span className='text-sm font-mono font-medium text-foreground flex-1 break-all'>
                        {paymentMatch.id}
                      </span>
                      <button
                        onClick={() =>
                          copy(
                            paymentMatch.id,
                            'Narration copied!',
                            'Failed to copy narration'
                          )
                        }
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors flex-shrink-0'
                        title='Copy narration'
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                /* Crypto Wallet Details */
                <>
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Network
                    </label>
                    <div className='text-sm font-medium text-foreground'>
                      {paymentMatch.payment_method.crypto_network}
                    </div>
                  </div>
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Wallet Address
                    </label>
                    <div className='flex items-center gap-2 p-2 bg-background-tertiary rounded'>
                      <span className='text-xs font-mono text-foreground break-all flex-1'>
                        {paymentMatch.payment_method.wallet_address}
                      </span>
                      <button
                        onClick={() =>
                          paymentMatch.payment_method?.wallet_address &&
                          copy(
                            paymentMatch.payment_method.wallet_address,
                            'Wallet address copied!',
                            'Failed to copy wallet address'
                          )
                        }
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors'
                        title='Copy wallet address'
                        disabled={!paymentMatch.payment_method.wallet_address}
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Crypto Transfer Note */}
                  <div>
                    <label className='text-xs text-foreground-secondary'>
                      Transaction Note/Memo
                    </label>
                    <div className='flex items-start gap-2 p-2 bg-background-tertiary rounded'>
                      <span className='text-sm font-mono font-medium text-foreground flex-1 break-all'>
                        {paymentMatch.id}
                      </span>
                      <button
                        onClick={() =>
                          copy(
                            paymentMatch.id,
                            'Transaction note copied!',
                            'Failed to copy transaction note'
                          )
                        }
                        className='flex items-center justify-center w-6 h-6 rounded hover:bg-background-secondary transition-colors flex-shrink-0'
                        title='Copy transaction note'
                      >
                        {copied ? (
                          <Check className='h-3 w-3 text-green-600' />
                        ) : (
                          <Copy className='h-3 w-3 text-foreground-secondary hover:text-foreground' />
                        )}
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Contact Notice */}
          <div className='bg-info/10 border border-info/30 rounded-lg p-3 mb-4'>
            <p className='text-xs text-foreground-secondary'>
              <span className='font-medium text-info'>Note:</span> This
              information can help you verify payment details or contact the
              withdrawer if needed.
            </p>
          </div>

          <Button variant='outline' className='w-full' onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
