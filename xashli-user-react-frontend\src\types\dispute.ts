// Payment Dispute types for user frontend
import type { User } from './auth';
import type { PaymentMatch } from './paymentMatch';

export interface PaymentDispute {
  id: string;
  payment_match_id: string;
  dispute_user_id: string;
  fund_user_id: string;
  withdraw_user_id: string;
  resolve_user_id?: string;
  reason: string;
  status: 'under_review' | 'resolved' | 'rejected';
  resolution?: 'confirmed' | 'cancelled' | 'partial_refund';
  refund_amount?: string;
  resolution_notes?: string;
  disputed_at: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;

  // Relations
  payment_match?: PaymentMatch;
  dispute_user?: User;
  fund_user?: User;
  withdraw_user?: User;
  resolve_user?: User;
}

// Request types for creating disputes
export interface CreateDisputeRequest {
  reason: string;
}

// Response types
export interface CreateDisputeResponse {
  dispute: PaymentDispute;
  message: string;
}

// Export dispute status and resolution types
export type DisputeStatus = 'under_review' | 'resolved' | 'rejected';
export type DisputeResolution = 'confirmed' | 'cancelled' | 'partial_refund';
