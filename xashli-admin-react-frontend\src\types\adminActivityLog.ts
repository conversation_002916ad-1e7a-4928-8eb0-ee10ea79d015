// Admin Activity Log types matching <PERSON><PERSON> backend
import type { PaginationMeta } from './common';

export interface AdminActivityLog {
  id: string;
  admin_id: string;
  action_type: ActionType;
  target_user_id?: string;
  metadata?: Record<string, any>;
  created_at: string;
  // Relations
  admin?: {
    id: string;
    full_name: string;
    email: string;
  };
  targetUser?: {
    id: string;
    full_name: string;
    email: string;
  };
}

export type ActionType =
  | 'user_created'
  | 'user_updated'
  | 'user_deleted'
  | 'dispute_resolved'
  | 'dispute_rejected'
  | 'payment_manually_matched'
  | 'auto_match_triggered'
  | 'settings_created'
  | 'settings_updated'
  | 'settings_deleted'
  | 'platform_fee_collected';

export interface ActionTypeOption {
  value: ActionType;
  label: string;
}

export interface AdminActivityLogFilters {
  action_type?: ActionType;
  admin_id?: string;
  target_user_id?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  per_page?: number;
}

export interface PaginatedAdminActivityLogs {
  adminActivityLogs: AdminActivityLog[];
  pagination: PaginationMeta;
}

export interface AdminActivityStatistics {
  total_logs_count: number;
  action_type_counts: Record<ActionType, number>;
  admin_counts: {
    admin_id: string;
    admin_name: string;
    admin_email: string;
    count: number;
  }[];
  daily_counts: Record<string, number>;
  date_range: {
    start_date?: string;
    end_date?: string;
  };
}

export interface AdminActivityLogFiltersState {
  action_type: ActionType | '';
  admin_id: string;
  target_user_id: string;
  start_date: string;
  end_date: string;
}
