<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenBlacklistedException;
use Ty<PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;

class JwtMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // This will throw exception if token is missing, invalid, or expired
            $user = JWTAuth::parseToken()->authenticate();

            if (! $user) {
                return $this->unauthorizedResponse('User not found');
            }

        } catch (TokenExpiredException $e) {
            return $this->unauthorizedResponse('Token has expired');
        } catch (TokenInvalidException $e) {
            return $this->unauthorizedResponse('Token is invalid');
        } catch (TokenBlacklistedException $e) {
            return $this->unauthorizedResponse('Token has been blacklisted');
        } catch (JWTException $e) {
            return $this->unauthorizedResponse('Token not provided or is malformed');
        } catch (Exception $e) {
            \Log::error('JWT Middleware Exception: ' . $e->getMessage());

            return $this->unauthorizedResponse('Authentication failed');
        }

        return $next($request);
    }

    /**
     * Return standardized unauthorized response.
     */
    private function unauthorizedResponse(string $message): Response
    {
        return response()->json([
            'status' => 'error',
            'message' => $message,
            'data' => null,
        ], 401);
    }
}
