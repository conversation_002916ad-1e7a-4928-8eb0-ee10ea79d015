import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from './Button';
import { cn } from '../../utils/cn';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string; // Make title optional for more flexibility
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showHeader?: boolean; // Allow hiding the header completely
}

const sizeClasses = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
};

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  className,
  showHeader = true,
}: ModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-50 overflow-y-auto'>
      {/* Container for centering */}
      <div className='relative flex justify-center items-start min-h-full'>
        {/* Backdrop - covers entire scrollable area */}
        <div
          className='absolute inset-0 w-full h-full bg-black bg-opacity-50 backdrop-blur-sm'
          onClick={onClose}
        />

        {/* Modal */}
        <div
          className={cn(
            'relative bg-background border border-border rounded-lg shadow-lg w-full mx-4 my-8 z-10',
            sizeClasses[size],
            className
          )}
        >
          {/* Header */}
          {showHeader && (
            <div className='flex items-center justify-between p-6 border-b border-border'>
              <h2 className='text-lg font-semibold text-foreground'>{title}</h2>
              <Button
                variant='outline'
                size='sm'
                onClick={onClose}
                className='h-8 w-8 p-0 border-none hover:bg-background-secondary'
              >
                <X className='h-4 w-4' />
                <span className='sr-only'>Close</span>
              </Button>
            </div>
          )}

          {/* Content */}
          <div className={showHeader ? 'p-6' : ''}>{children}</div>
        </div>
      </div>
    </div>
  );
}
