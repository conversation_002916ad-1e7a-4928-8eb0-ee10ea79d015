import { User, X, Phone, Mail, Copy, Check, CheckCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { useCopyToClipboard } from '../../utils/copy';
import { formatCurrencyAmount } from '../../utils/format';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { Currency } from '@/types';

interface FunderModalProps {
  isOpen: boolean;
  onClose: () => void;
  paymentMatch: PaymentMatch;
  currency: Currency;
}

export function FunderModal({
  isOpen,
  onClose,
  paymentMatch,
  currency,
}: FunderModalProps) {
  const { copied, copy } = useCopyToClipboard();

  if (!isOpen || !paymentMatch.fund_user) {
    return null;
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 overflow-y-auto'>
      <div className='min-h-full flex items-center justify-center p-4'>
        <div className='bg-background rounded-lg max-w-sm w-full p-5 border border-border'>
          <div className='flex items-center justify-between mb-4'>
            <div className='flex items-center gap-3'>
              <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
                <User className='h-5 w-5 text-primary' />
              </div>
              <div>
                <h3 className='text-lg font-semibold text-foreground'>
                  Funder Information
                </h3>
                <p className='text-sm text-foreground-secondary'>
                  Payment sender details
                </p>
              </div>
            </div>
            <Button
              variant='ghost'
              size='sm'
              onClick={onClose}
              className='h-8 w-8 p-0'
            >
              <X className='h-4 w-4' />
            </Button>
          </div>

          <div className='space-y-4'>
            {/* User Name */}
            <div className='flex items-center gap-3'>
              <div className='w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0'>
                <User className='h-4 w-4 text-primary' />
              </div>
              <div className='min-w-0 flex-1'>
                <p className='text-sm font-medium text-foreground truncate'>
                  {paymentMatch.fund_user.full_name || 'N/A'}
                </p>
                <p className='text-xs text-foreground-secondary'>Full Name</p>
              </div>
            </div>

            {/* Email */}
            <div className='flex items-center gap-3'>
              <div className='w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0'>
                <Mail className='h-4 w-4 text-blue-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <p className='text-sm font-medium text-foreground truncate'>
                  {paymentMatch.fund_user.email || 'N/A'}
                </p>
                <p className='text-xs text-foreground-secondary'>Email</p>
              </div>
              {paymentMatch.fund_user?.email && (
                <button
                  onClick={() => {
                    if (paymentMatch.fund_user?.email) {
                      copy(
                        paymentMatch.fund_user.email,
                        'Email copied!',
                        'Failed to copy email'
                      );
                    }
                  }}
                  className='flex items-center justify-center w-8 h-8 rounded hover:bg-blue-50 transition-colors'
                  title='Copy email'
                >
                  {copied ? (
                    <Check className='h-3 w-3 text-green-600' />
                  ) : (
                    <Copy className='h-3 w-3 text-blue-600' />
                  )}
                </button>
              )}
            </div>

            {/* Phone */}
            <div className='flex items-center gap-3'>
              <div className='w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0'>
                <Phone className='h-4 w-4 text-green-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <p className='text-sm font-medium text-foreground truncate'>
                  {paymentMatch.fund_user.phone || 'N/A'}
                </p>
                <p className='text-xs text-foreground-secondary'>Phone</p>
              </div>
              {paymentMatch.fund_user?.phone && (
                <button
                  onClick={() => {
                    if (paymentMatch.fund_user?.phone) {
                      copy(
                        paymentMatch.fund_user.phone,
                        'Phone number copied!',
                        'Failed to copy phone number'
                      );
                    }
                  }}
                  className='flex items-center justify-center w-8 h-8 rounded hover:bg-green-50 transition-colors'
                  title='Copy phone number'
                >
                  {copied ? (
                    <Check className='h-3 w-3 text-green-600' />
                  ) : (
                    <Copy className='h-3 w-3 text-green-600' />
                  )}
                </button>
              )}
            </div>

            {/* Payment Amount */}
            <div className='flex items-center gap-3'>
              <div className='w-8 h-8 bg-orange-50 rounded-full flex items-center justify-center flex-shrink-0'>
                <CheckCircle className='h-4 w-4 text-orange-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <p className='text-sm font-semibold text-foreground'>
                  {formatCurrencyAmount(paymentMatch.amount || 0, currency)}
                </p>
                <p className='text-xs text-foreground-secondary'>
                  Payment Amount
                </p>
              </div>
            </div>

            {/* Contact Notice */}
            <div className='bg-info/10 border border-info/30 rounded-lg p-3 mt-4'>
              <p className='text-xs text-foreground-secondary'>
                <span className='font-medium text-info'>Note:</span> Use this
                information to contact the funder if needed. Please be
                professional in all communications.
              </p>
            </div>
          </div>

          <Button variant='outline' className='w-full mt-5' onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </div>
  );
}
