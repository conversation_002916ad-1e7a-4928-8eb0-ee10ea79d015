import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Coins, Filter, Banknote, Users, Calculator } from 'lucide-react';
import { platformFeeService } from '@/services/platformFeeService';
import { platformSettingsService } from '@/services/platformSettings';
import type {
  PlatformFee,
  PlatformFeeFilters,
  PlatformFeeStatistics,
} from '@/types/platformFee';
import { formatCurrencyAmount, formatDate } from '@/utils/format';
import { LoadingSpinner } from '@/components/ui/loading';
import { Pagination } from '@/components/ui/pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { MainLayout } from '@/components/layout';

interface PlatformFeesPageProps {}

export const PlatformFeesPage: React.FC<PlatformFeesPageProps> = () => {
  const [platformFees, setPlatformFees] = useState<PlatformFee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PlatformFeeFilters>({
    page: 1,
    per_page: 20,
  });
  const [pendingFilters, setPendingFilters] = useState<PlatformFeeFilters>({
    page: 1,
    per_page: 20,
  });
  const [showFilters, setShowFilters] = useState(false);
  const [simulationMode, setSimulationMode] = useState(false);
  const [customFeePercentage, setCustomFeePercentage] = useState<string>('');
  const [platformFeePercentage, setPlatformFeePercentage] =
    useState<string>('');
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    total: 0,
    per_page: 20,
  });

  // Statistics data - using direct backend structure
  const [statisticsData, setStatisticsData] = useState<PlatformFeeStatistics>({
    counts: {
      total: 0,
      fiat: 0,
      crypto: 0,
    },
    amounts: {
      total: {
        fiat: 0,
        crypto: 0,
      },
    },
  });

  useEffect(() => {
    fetchPlatformFees();
    fetchStatistics();
    fetchCurrentFeePercentage();
  }, [filters]);

  // Initialize pending filters with current filters
  useEffect(() => {
    setPendingFilters(filters);
  }, [filters]);

  // Set default fee percentage from calculated average if no setting is available
  useEffect(() => {
    if (
      !platformFeePercentage &&
      platformFees.length > 0 &&
      !customFeePercentage
    ) {
      const totalPercentage = platformFees.reduce(
        (sum, fee) => sum + fee.fee_percentage,
        0
      );
      const averagePercentage = (totalPercentage / platformFees.length).toFixed(
        1
      );
      setCustomFeePercentage(averagePercentage);
    }
  }, [platformFees, platformFeePercentage, customFeePercentage]);

  const fetchPlatformFees = async () => {
    try {
      setLoading(true);
      const response = await platformFeeService.getFees(filters);
      if (response.data) {
        setPlatformFees(response.data.platformFees);
        setPagination({
          current_page: response.data.pagination.current_page,
          last_page: response.data.pagination.last_page,
          total: response.data.pagination.total,
          per_page: response.data.pagination.per_page,
        });
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch platform fee collections');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await platformFeeService.getStatistics();
      if (response.data) {
        // Use the direct statistics response structure
        setStatisticsData(response.data);
      }
    } catch (err) {
      console.error('Failed to fetch statistics data:', err);
    }
  };

  const fetchCurrentFeePercentage = async () => {
    try {
      // Try to fetch platform fee percentage setting
      const response = await platformSettingsService.getByKey(
        'platform_fee_percentage'
      );
      if (response.data && response.data.value) {
        const feePercentage = String(response.data.value);
        setPlatformFeePercentage(feePercentage);
        // Set as default value for simulation input
        if (!customFeePercentage) {
          setCustomFeePercentage(feePercentage);
        }
      }
    } catch (err) {
      console.error('Failed to fetch current fee percentage:', err);
      // Note: We'll handle fallback calculation in the simulation function
      // since platformFees might not be loaded yet when this function runs
    }
  };

  const handleFilterChange = (key: keyof PlatformFeeFilters, value: any) => {
    setPendingFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const applyFilters = () => {
    setFilters({
      ...pendingFilters,
      page: 1, // Reset to first page when filters change
    });
  };

  const clearFilters = () => {
    const clearedFilters = { page: 1, per_page: 20 };
    setFilters(clearedFilters);
    setPendingFilters(clearedFilters);
  };

  const hasFilterChanges = () => {
    const { page, per_page, ...currentFiltersWithoutPagination } = filters;
    const {
      page: pendingPage,
      per_page: pendingPerPage,
      ...pendingFiltersWithoutPagination
    } = pendingFilters;
    return (
      JSON.stringify(currentFiltersWithoutPagination) !==
      JSON.stringify(pendingFiltersWithoutPagination)
    );
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, per_page: pageSize, page: 1 }));
  };

  const getCurrencyBadgeColor = (currency: string) => {
    return currency === 'fiat'
      ? 'bg-green-100 text-green-800'
      : 'bg-blue-100 text-blue-800';
  };

  // Utility function to calculate simulated fee amounts
  const getDisplayFeeAmount = (
    originalFeeAmount: number,
    originalPercentage: number
  ) => {
    if (
      !simulationMode ||
      !customFeePercentage ||
      isNaN(parseFloat(customFeePercentage))
    ) {
      return originalFeeAmount;
    }

    const newPercentage = parseFloat(customFeePercentage);
    // Calculate base amount from original fee, then apply new percentage
    const baseAmount = originalFeeAmount / (originalPercentage / 100);
    return baseAmount * (newPercentage / 100);
  };

  // Utility function to get display percentage
  const getDisplayPercentage = (originalPercentage: number) => {
    if (
      !simulationMode ||
      !customFeePercentage ||
      isNaN(parseFloat(customFeePercentage))
    ) {
      return originalPercentage;
    }
    return parseFloat(customFeePercentage);
  };

  // Calculate simulated statistics
  const getSimulatedStatistics = () => {
    if (
      !simulationMode ||
      !customFeePercentage ||
      isNaN(parseFloat(customFeePercentage))
    ) {
      return statisticsData;
    }

    const newPercentage = parseFloat(customFeePercentage);

    // Use platform fee percentage as baseline, fallback to calculating from visible records
    let averageOriginalPercentage = 7; // Default fallback

    if (platformFeePercentage) {
      // Prefer the platform setting as it represents the baseline
      averageOriginalPercentage = parseFloat(platformFeePercentage);
    } else if (platformFees.length > 0) {
      // Fallback to calculating from visible fee records
      const totalPercentage = platformFees.reduce(
        (sum, fee) => sum + fee.fee_percentage,
        0
      );
      averageOriginalPercentage = totalPercentage / platformFees.length;
    }

    const ratio = newPercentage / averageOriginalPercentage;

    return {
      ...statisticsData,
      amounts: {
        total: {
          fiat: statisticsData.amounts.total.fiat * ratio,
          crypto: statisticsData.amounts.total.crypto * ratio,
        },
      },
    };
  };

  const displayStatistics = getSimulatedStatistics();

  const handleSimulationToggle = () => {
    setSimulationMode(!simulationMode);
    if (simulationMode) {
      setCustomFeePercentage('');
    } else {
      // When entering simulation mode, set current platform fee as default
      setCustomFeePercentage(platformFeePercentage);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Platform Fee Collections
            </h1>
            <p className="text-muted-foreground">
              Monitor and track all platform fee collections
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleSimulationToggle}
              className={`flex items-center gap-2 ${
                simulationMode
                  ? 'bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100 hover:border-blue-300'
                  : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 hover:border-gray-300'
              }`}
            >
              <Calculator className="h-4 w-4" />
              {simulationMode ? 'Exit Simulation' : 'Simulate Fees'}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 ${
                hasFilterChanges()
                  ? 'bg-orange-50 border-orange-200 text-orange-800 hover:bg-orange-100 hover:border-orange-300'
                  : 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100 hover:border-yellow-300'
              }`}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {hasFilterChanges() && <span className="ml-1 text-xs">(*)</span>}
            </Button>
          </div>
        </div>{' '}
        {/* Fee Simulation Input */}
        {simulationMode && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex flex-col gap-4">
                {/* Header */}
                <div className="flex items-center gap-2">
                  <Calculator className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    Fee Simulation Active
                  </span>
                </div>

                {/* Input Section - Always Horizontal */}
                <div className="flex items-center gap-4">
                  <label
                    htmlFor="fee-percentage"
                    className="text-sm font-medium text-blue-700 whitespace-nowrap"
                  >
                    Custom Fee Percentage:
                  </label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="fee-percentage"
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={customFeePercentage}
                      onChange={e => setCustomFeePercentage(e.target.value)}
                      placeholder={platformFeePercentage || '5.0'}
                      className="w-24 h-9 text-center border-blue-300 focus:border-blue-500"
                    />
                    <span className="text-sm text-blue-700">%</span>
                  </div>
                </div>

                {/* Help Text */}
                <p className="text-xs text-blue-600">
                  Enter a percentage to see simulated fee amounts in real-time.
                  This doesn't affect actual data.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Fiat Fees
              </CardTitle>
              <Banknote className="h-4 w-4 text-muted-foreground" />{' '}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrencyAmount(
                  displayStatistics.amounts.total.fiat,
                  'fiat'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                All time fiat collections
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Crypto Fees
              </CardTitle>
              <Coins className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrencyAmount(
                  displayStatistics.amounts.total.crypto,
                  'crypto'
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                All time crypto collections
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Collections
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {displayStatistics.counts.total.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                All time collections
              </p>
            </CardContent>
          </Card>
        </div>
        {/* Filters */}
        {showFilters && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Currency
                  </label>
                  <Select
                    value={pendingFilters.currency || 'all'}
                    onValueChange={value =>
                      handleFilterChange(
                        'currency',
                        value === 'all' ? undefined : value
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All currencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Currencies</SelectItem>
                      <SelectItem value="fiat">Fiat (NGN)</SelectItem>
                      <SelectItem value="crypto">Crypto (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Start Date
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.start_date || ''}
                    onChange={e =>
                      handleFilterChange('start_date', e.target.value)
                    }
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    End Date
                  </label>
                  <Input
                    type="date"
                    value={pendingFilters.end_date || ''}
                    onChange={e =>
                      handleFilterChange('end_date', e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Buttons container - separate div below input fields */}
              <div className="flex justify-end gap-3 mt-4">
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button onClick={applyFilters} disabled={!hasFilterChanges()}>
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        {/* Collections Table */}
        <Card>
          <CardHeader>
            <CardTitle>
              Fee Collections ({pagination.total.toLocaleString()})
            </CardTitle>
          </CardHeader>{' '}
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : error ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-red-600">{error}</div>
              </div>
            ) : platformFees.length === 0 ? (
              <div className="flex justify-center items-center py-8">
                <div className="text-gray-500 text-center">
                  <Coins className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No fee collections found</p>
                  <p className="text-sm">
                    No platform fees have been collected yet.
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>SN</TableHead>
                        <TableHead>Fund</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Fee</TableHead>
                        <TableHead>Currency</TableHead>
                        <TableHead>Admin</TableHead>
                        <TableHead>Collected At</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {platformFees.map((platformFee, index) => (
                        <TableRow key={platformFee.id}>
                          <TableCell>
                            <span className="text-sm font-medium text-gray-900">
                              {(pagination.current_page - 1) *
                                pagination.per_page +
                                index +
                                1}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="font-mono text-sm">
                              {platformFee.fund_id.slice(0, 8)}...
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Fund:{' '}
                              {formatCurrencyAmount(
                                platformFee.fund_amount,
                                platformFee.currency
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {platformFee.fund?.user?.full_name || 'Unknown'}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {platformFee.fund?.user?.email}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrencyAmount(
                                platformFee.fund_amount,
                                platformFee.currency
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {formatCurrencyAmount(
                                getDisplayFeeAmount(
                                  platformFee.fee_amount,
                                  platformFee.fee_percentage
                                ),
                                platformFee.currency
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {getDisplayPercentage(platformFee.fee_percentage)}
                              %
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={getCurrencyBadgeColor(
                                platformFee.currency
                              )}
                            >
                              {platformFee.currency.toUpperCase()}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {platformFee.admin?.full_name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {platformFee.admin?.email}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {formatDate(platformFee.collected_at)}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>{' '}
                {/* Pagination */}
                <Pagination
                  pagination={{
                    current_page: pagination.current_page,
                    last_page: pagination.last_page,
                    per_page: pagination.per_page,
                    total: pagination.total,
                    from: null,
                    to: null,
                  }}
                  currentPage={pagination.current_page}
                  pageSize={pagination.per_page}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  itemLabel="Platform Fees"
                />
              </div>
            )}{' '}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};
