import { Link } from 'react-router-dom';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { WithdrawsStatistics } from '../../components/withdraws/WithdrawsStatistics';
import { useWithdrawStatistics } from '../../hooks/withdraws';

export function WithdrawStatisticsPage() {
  const { statistics, loading, refreshStatistics } = useWithdrawStatistics();

  return (
    <div className='container mx-auto px-6 py-8 bg-background'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8'>
        <div className='flex items-center gap-4'>
          <Link to='/withdraws'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='h-4 w-4' />
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-foreground'>
              Withdraw Statistics
            </h1>
            <p className='text-foreground-secondary mt-2'>
              Overview of your withdrawal performance
            </p>
          </div>
        </div>
        <Button
          variant='outline'
          onClick={refreshStatistics}
          disabled={loading}
          className='flex items-center gap-2 self-start sm:self-center mt-2 sm:mt-0'
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Statistics Component */}
      <WithdrawsStatistics statistics={statistics} loading={loading} />
    </div>
  );
}
