/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Brand Colors: Black, Grey, Gold - Dark Theme Optimized
        brand: {
          black: '#000000',
          white: '#FFFFFF',
          grey: {
            50: '#1F2937', // Darker for dark theme
            100: '#374151', // Darker for dark theme
            200: '#4B5563', // Darker for dark theme
            300: '#6B7280', // Darker for dark theme
            400: '#9CA3AF',
            500: '#D1D5DB', // Lighter for dark theme
            600: '#E5E7EB', // Lighter for dark theme
            700: '#F3F4F6', // Lighter for dark theme
            800: '#F9FAFB', // Lighter for dark theme
            900: '#FFFFFF', // White for dark theme
          },
          gold: {
            50: '#78350F', // Darker gold for dark theme
            100: '#92400E', // Darker gold for dark theme
            200: '#B45309', // Darker gold for dark theme
            300: '#D97706', // Darker gold for dark theme
            400: '#F59E0B',
            500: '#FBBF24', // Main gold
            600: '#FCD34D', // Lighter gold
            700: '#FDE68A', // Lighter gold
            800: '#FEF3C7', // Lighter gold
            900: '#FFFBEB', // Lightest gold
          },
        },
        // Dark theme color system
        background: {
          DEFAULT: '#000000', // Pure black
          secondary: '#111827', // Very dark grey
          tertiary: '#1F2937', // Dark grey
        },
        foreground: {
          DEFAULT: '#F9FAFB', // Light grey text
          secondary: '#E5E7EB', // Slightly darker text
          muted: '#9CA3AF', // Muted text
        },
        primary: {
          DEFAULT: '#FBBF24', // Gold
          foreground: '#000000', // Black text on gold
          dark: '#D97706', // Darker gold
        },
        secondary: {
          DEFAULT: '#374151', // Dark grey
          foreground: '#F9FAFB', // Light text
        },
        muted: {
          DEFAULT: '#1F2937', // Dark grey background
          foreground: '#9CA3AF', // Muted text
        },
        accent: {
          DEFAULT: '#374151', // Dark grey accent
          foreground: '#F9FAFB', // Light text
        },
        border: '#374151', // Dark grey borders
        input: '#1F2937', // Dark input background
        ring: '#FBBF24', // Gold focus ring
        destructive: {
          DEFAULT: '#EF4444', // Red for errors
          foreground: '#F9FAFB', // Light text
        },
        warning: {
          DEFAULT: '#F59E0B', // Orange for warnings
          foreground: '#000000', // Dark text
        },
        success: {
          DEFAULT: '#10B981', // Green for success
          foreground: '#F9FAFB', // Light text
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        'slide-in': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      animation: {
        'fade-in': 'fade-in 0.5s ease-out',
        'slide-in': 'slide-in 0.3s ease-out',
      },
    },
  },
  plugins: [],
};
