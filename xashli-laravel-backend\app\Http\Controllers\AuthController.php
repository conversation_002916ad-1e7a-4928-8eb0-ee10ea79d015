<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserStat;
use App\Models\VerificationCode;
use App\Services\EmailService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    protected EmailService $emailService;

    public function __construct(EmailService $emailService)
    {
        $this->emailService = $emailService;
    }

    /**
     * Register a new user.
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'required|string|max:20',
            'referrer_code' => 'nullable|string|exists:users,referral_code',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Generate a unique referral code
        $referralCode = $this->generateUniqueReferralCode();

        // Find referrer if referred
        $referrerId = null;
        if ($request->has('referrer_code') && ! empty($request->referrer_code)) {
            $referrer = User::where('referral_code', $request->referrer_code)->first();
            if ($referrer) {
                $referrerId = $referrer->id;
                // Increment referrer's referee count
                $referrer->increment('referee_count');
            }
        }

        // Create the user
        $user = User::create([
            'full_name' => $request->full_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'referral_code' => $referralCode,
            'referrer_code' => $request->referrer_code,
            'referrer_id' => $referrerId,
            'referee_count' => 0,
            'is_active' => true,
            'role' => 'user',
        ]);

        // Create user stats
        UserStat::create([
            'user_id' => $user->id,
            'updated_at' => now(),
        ]);

        // Send email verification code
        $verificationData = [];

        try {
            // Create and send email verification code
            $emailCode = VerificationCode::generate($user, 'email');
            $emailSent = $this->emailService->sendVerificationCode($user, $emailCode->code);

            $verificationData['email'] = [
                'sent' => $emailSent,
                'expires_at' => $emailCode->expires_at,
            ];

            if (! $emailSent) {
                $verificationData['email']['error'] = 'Failed to send email verification';
            }
        } catch (\Exception $e) {
            $verificationData['email'] = [
                'sent' => false,
                'error' => 'Failed to send email verification',
            ];
        }

        // Generate access token
        $token = JWTAuth::fromUser($user);

        // Generate refresh token
        $refreshToken = $this->createRefreshToken($user);

        return $this->success([
            'user' => $user,
            'authorization' => [
                'access_token' => $token,
                'refresh_token' => $refreshToken,
                'type' => 'bearer',
                'expires_in' => auth()->factory()->getTTL() * 60,
            ],
            'verification' => $verificationData,
        ], 'User registered successfully. Please check your email for verification code.');
    }

    /**
     * Get a JWT via given credentials.
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $credentials = $request->only('email', 'password');

        if (! $token = auth()->attempt($credentials)) {
            return $this->unauthorized('Invalid credentials');
        }

        $user = auth()->user();

        // Generate refresh token with longer expiry
        $refreshToken = $this->createRefreshToken($user);

        return $this->success([
            'user' => $user,
            'authorization' => [
                'access_token' => $token,
                'refresh_token' => $refreshToken,
                'type' => 'bearer',
                'expires_in' => auth()->factory()->getTTL() * 60,
            ],
        ], 'Login successful');
    }

    /**
     * Get the authenticated User.
     */
    public function me(): JsonResponse
    {
        return $this->success(auth()->user(), 'User profile retrieved successfully');
    }

    /**
     * Log the user out (Invalidate the token).
     */
    public function logout(): JsonResponse
    {
        auth()->logout();

        return $this->success(null, 'Successfully logged out');
    }

    /**
     * Refresh tokens using a refresh token.
     */
    public function refresh(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'refresh_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            // Decode and validate the refresh token
            $payload = JWTAuth::getJWTProvider()->decode($request->refresh_token);

            // Check if it's a refresh token
            if (! isset($payload['type']) || $payload['type'] !== 'refresh') {
                return $this->unauthorized('Invalid refresh token');
            }

            // Check if token is expired
            if (isset($payload['exp']) && time() >= $payload['exp']) {
                return $this->unauthorized('Refresh token has expired');
            }

            // Get the user
            $user = User::find($payload['sub']);

            if (! $user) {
                return $this->unauthorized('User not found');
            }

            // Generate new access token
            $token = JWTAuth::fromUser($user);

            // Generate new refresh token
            $refreshToken = $this->createRefreshToken($user);

            return $this->success([
                'authorization' => [
                    'access_token' => $token,
                    'refresh_token' => $refreshToken,
                    'type' => 'bearer',
                    'expires_in' => auth()->factory()->getTTL() * 60,
                ],
            ], 'Tokens refreshed successfully');
        } catch (\Exception $e) {
            return $this->unauthorized('Invalid refresh token: ' . $e->getMessage());
        }
    }

    /**
     * Request a password reset link.
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $user = User::where('email', $request->email)->first();
        $token = $this->generatePasswordResetToken($user);

        try {
            // Send password reset email
            $emailSent = $this->emailService->sendPasswordResetEmail($user, $token);

            if ($emailSent) {
                return $this->success(null, 'Password reset link sent to your email address');
            } else {
                return $this->error('Failed to send password reset email. Please try again later.', 500);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send password reset email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage(),
            ]);

            return $this->error('Failed to send password reset email. Please try again later.', 500);
        }
    }

    /**
     * Reset the password using the reset token.
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $payload = JWTAuth::getJWTProvider()->decode($request->token);

            if (! isset($payload['type']) || $payload['type'] !== 'password_reset') {
                return $this->unauthorized('Invalid reset token');
            }

            if (isset($payload['exp']) && time() >= $payload['exp']) {
                return $this->unauthorized('Reset token has expired');
            }

            $user = User::find($payload['sub']);

            if (! $user) {
                return $this->unauthorized('User not found');
            }

            $user->password = Hash::make($request->password);
            $user->save();

            return $this->success(null, 'Password reset successfully');
        } catch (\Exception $e) {
            return $this->unauthorized('Invalid reset token: ' . $e->getMessage());
        }
    }

    // ======================================================================
    // Private utility methods
    // ======================================================================

    /**
     * Create a refresh token for the user.
     */
    private function createRefreshToken(User $user): string
    {
        $payload = [
            'sub' => $user->id,
            'jti' => Str::random(16),
            'iat' => time(),
            'exp' => time() + (config('jwt.refresh_ttl', 20160) * 60), // Default: 2 weeks
            'type' => 'refresh',
        ];

        return JWTAuth::getJWTProvider()->encode($payload);
    }

    /**
     * Generate a unique referral code.
     */
    private function generateUniqueReferralCode(): string
    {
        $code = strtoupper(Str::random(8));

        // Check if code already exists
        while (User::where('referral_code', $code)->exists()) {
            $code = strtoupper(Str::random(8));
        }

        return $code;
    }

    /**
     * Generate a password reset token for the user.
     */
    private function generatePasswordResetToken(User $user): string
    {
        $payload = [
            'sub' => $user->id,
            'jti' => Str::random(16),
            'iat' => time(),
            'exp' => time() + (60 * 60), // 1 hour expiry
            'type' => 'password_reset',
        ];

        return JWTAuth::getJWTProvider()->encode($payload);
    }
}
