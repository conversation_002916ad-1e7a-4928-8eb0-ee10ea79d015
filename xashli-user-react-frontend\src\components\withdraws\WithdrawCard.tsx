import {
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Banknote,
  Coins,
  ArrowRight,
  Download,
} from 'lucide-react';
import { Badge } from '../ui';
import { Button } from '../ui/Button';
import { formatCurrencyAmount, formatDateTime } from '../../utils/format';
import type { Withdraw } from '../../types';
import { toNumber } from '../../utils/utils';

interface WithdrawCardProps {
  withdraw: Withdraw;
  onView: () => void;
}

export function WithdrawCard({ withdraw, onView }: WithdrawCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-warning/20 text-warning border-warning/30';
      case 'matched':
        return 'bg-primary/20 text-primary border-primary/30';
      case 'completed':
        return 'bg-success/20 text-success border-success/30';
      default:
        return 'bg-muted/20 text-muted-foreground border-muted/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className='h-3 w-3' />;
      case 'matched':
        return <AlertCircle className='h-3 w-3' />;
      case 'completed':
        return <CheckCircle className='h-3 w-3' />;
      default:
        return null;
    }
  };

  return (
    <div className='bg-background-secondary rounded-xl border border-border p-6 hover:border-primary/20 hover:shadow-lg transition-all duration-200'>
      {/* Header */}
      <div className='flex items-start justify-between mb-4'>
        <div className='flex items-center gap-3'>
          <div className='w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center'>
            <Download className='h-6 w-6 text-primary' />
          </div>
          <div>
            <h3 className='font-semibold text-foreground'>
              {formatCurrencyAmount(
                toNumber(withdraw.base_withdrawable_amount) +
                  toNumber(withdraw.withdrawable_referral_bonus),
                withdraw.fund?.currency
              )}
            </h3>
            <p className='text-sm text-foreground-secondary'>
              Withdrawal Request
            </p>
          </div>
        </div>
        <Badge
          variant='outline'
          className={`${getStatusColor(withdraw.status)} flex items-center gap-1`}
        >
          {getStatusIcon(withdraw.status)}
          {withdraw.status.charAt(0).toUpperCase() + withdraw.status.slice(1)}
        </Badge>
      </div>

      {/* Details */}
      <div className='space-y-3 mb-4'>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Amount Matched</span>
          <span className='font-medium text-foreground'>
            {formatCurrencyAmount(
              withdraw.amount_matched,
              withdraw.fund?.currency
            )}
          </span>
        </div>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Payment Matches</span>
          <span className='font-medium text-foreground'>
            {withdraw.payment_matches?.length || 0} matches
          </span>
        </div>
        <div className='flex items-center justify-between text-sm'>
          <span className='text-foreground-secondary'>Created</span>
          <span className='font-medium text-foreground flex items-center gap-1'>
            <Calendar className='h-3 w-3' />
            {formatDateTime(withdraw.created_at)}
          </span>
        </div>
      </div>

      {/* Payment Method */}
      {withdraw.payment_method && (
        <div className='mb-4 p-3 bg-muted/50 rounded-lg border border-border/30'>
          <div className='flex items-center gap-3'>
            {withdraw.payment_method.type === 'bank' ? (
              <Banknote className='h-4 w-4 text-foreground-secondary' />
            ) : (
              <Coins className='h-4 w-4 text-foreground-secondary' />
            )}
            <div className='flex-1'>
              <p className='text-xs text-foreground-secondary font-medium mb-1'>
                Payment Method
              </p>
              {withdraw.payment_method.type === 'bank' ? (
                <div>
                  <p className='text-sm font-semibold text-foreground'>
                    {withdraw.payment_method.bank_name || 'Bank Transfer'}
                  </p>
                  <p className='text-xs text-foreground-secondary'>
                    {withdraw.payment_method.account_number || 'N/A'}
                  </p>
                </div>
              ) : (
                <div>
                  <p className='text-sm font-semibold text-foreground'>
                    Solana Network
                  </p>
                  <p className='text-xs text-foreground-secondary font-mono'>
                    {withdraw.payment_method.wallet_address
                      ? `${withdraw.payment_method.wallet_address.slice(0, 6)}...${withdraw.payment_method.wallet_address.slice(-4)}`
                      : 'Crypto Wallet'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className='flex gap-2 pt-4 border-t border-border'>
        <Button variant='outline' size='sm' onClick={onView} className='flex-1'>
          View Details
          <ArrowRight className='h-3 w-3 ml-2' />
        </Button>
      </div>

      {/* Status Information */}
      {withdraw.status === 'matched' &&
        withdraw.payment_matches &&
        withdraw.payment_matches.length > 0 && (
          <div className='mt-3 p-2 bg-primary/10 border border-primary/30 rounded-lg'>
            <p className='text-xs text-primary text-center'>
              💰 {withdraw.payment_matches.length} payment match
              {withdraw.payment_matches.length !== 1 ? 'es' : ''} found. Check
              details for payment status.
            </p>
          </div>
        )}

      {withdraw.status === 'completed' && (
        <div className='mt-3 p-2 bg-success/10 border border-success/30 rounded-lg'>
          <p className='text-xs text-success text-center'>
            ✅ Withdrawal completed successfully!
          </p>
        </div>
      )}
    </div>
  );
}
