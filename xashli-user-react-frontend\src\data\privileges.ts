// Platform configuration and privilege data
export const PLATFORM_CONFIG = {
  growth_rates: {
    fiat: 50, // 50%
    crypto: 55, // 55%
  },
  currency: {
    fiat: {
      symbol: '₦',
      name: '<PERSON><PERSON>',
    },
    crypto: {
      symbol: '<PERSON><PERSON>',
      name: '<PERSON><PERSON>',
    },
  },
  referral: {
    levels_rewarded: 1, // Only direct referrers (level 1) are rewarded
  },
} as const;

export interface PrivilegeData {
  id: string;
  name: string;
  level: number;
  badge: string;
  description: string;
  color: {
    primary: string;
    secondary: string;
    bg: string;
    border: string;
  };
  icon: string;
  benefits: {
    max_fiat_fund: number;
    max_crypto_fund: number;
    maturity_period: number;
    multiplier?: number;
  };
  requirements: {
    type: 'default' | 'referral' | 'either';
    description: string;
    conditions?: {
      transactions?: {
        count: number;
        conditions: string[];
      };
      downlines?: {
        count: number;
        requirement: string;
      };
      referral_funding?: {
        fiat_amount?: number;
        crypto_amount?: number;
      };
    };
  };
}

export const PRIVILEGES_DATA: PrivilegeData[] = [
  {
    id: 'standard',
    name: 'Standard',
    level: 1,
    badge: 'Default level',
    description: 'Available by default for all new users',
    color: {
      primary: 'text-gray-600',
      secondary: 'text-gray-500',
      bg: 'bg-gray-100',
      border: 'border-gray-200',
    },
    icon: 'Users',
    benefits: {
      max_fiat_fund: 100000, // ₦100K
      max_crypto_fund: 10, // 10 SOL
      maturity_period: 10, // 10 days
    },
    requirements: {
      type: 'default',
      description: 'Available by default (no special criteria)',
    },
  },
  {
    id: 'premium',
    name: 'Premium',
    level: 2,
    badge: 'Fast Track',
    description: 'Faster maturity with reduced waiting period',
    color: {
      primary: 'text-amber-600',
      secondary: 'text-amber-500',
      bg: 'bg-amber-100',
      border: 'border-amber-200',
    },
    icon: 'Star',
    benefits: {
      max_fiat_fund: 100000, // ₦100K
      max_crypto_fund: 10, // 10 SOL
      maturity_period: 7, // 7 days
    },
    requirements: {
      type: 'referral',
      description: 'Build a strong referral network',
      conditions: {
        referral_funding: {
          fiat_amount: 5000000, // ₦5 million
          crypto_amount: 20, // 20 SOL
        },
      },
    },
  },
  {
    id: 'elite-1',
    name: 'Elite 1',
    level: 3,
    badge: 'Elite I',
    description:
      '2x multiplier with enhanced benefits. Maturity period: 7 days (with Premium) or 10 days (Standard).',
    color: {
      primary: 'text-yellow-600',
      secondary: 'text-yellow-500',
      bg: 'bg-yellow-100',
      border: 'border-yellow-200',
    },
    icon: 'Shield',
    benefits: {
      max_fiat_fund: 200000, // ₦200K
      max_crypto_fund: 20, // 20 SOL
      maturity_period: 7, // 7 days (if premium) / 10 days (if standard)
      multiplier: 2,
    },
    requirements: {
      type: 'either',
      description: 'Elite performance through transactions or referrals',
      conditions: {
        transactions: {
          count: 10,
          conditions: [
            'No fake POP (Proof of Payment)',
            'Funding within 1 hour',
            'Payment confirmation within 30 minutes',
          ],
        },
        downlines: {
          count: 10,
          requirement:
            'Active direct referrers who have each completed 1 transaction cycle',
        },
      },
    },
  },
  {
    id: 'elite-2',
    name: 'Elite 2',
    level: 4,
    badge: 'Elite II',
    description:
      '3x multiplier with advanced benefits. Maturity period: 7 days (with Premium) or 10 days (Standard).',
    color: {
      primary: 'text-orange-600',
      secondary: 'text-orange-500',
      bg: 'bg-orange-100',
      border: 'border-orange-200',
    },
    icon: 'Shield',
    benefits: {
      max_fiat_fund: 300000, // ₦300K
      max_crypto_fund: 30, // 30 SOL
      maturity_period: 7, // 7 days (if premium) / 10 days (if standard)
      multiplier: 3,
    },
    requirements: {
      type: 'either',
      description: 'Advanced elite performance',
      conditions: {
        transactions: {
          count: 15,
          conditions: [
            'No fake POP (Proof of Payment)',
            'Funding within 1 hour',
            'Payment confirmation within 30 minutes',
          ],
        },
        downlines: {
          count: 15,
          requirement:
            'Active direct referrers who have each completed 1 transaction cycle',
        },
      },
    },
  },
  {
    id: 'elite-3',
    name: 'Elite 3',
    level: 5,
    badge: 'Elite III',
    description:
      '4x multiplier with master benefits. Maturity period: 7 days (with Premium) or 10 days (Standard).',
    color: {
      primary: 'text-red-600',
      secondary: 'text-red-500',
      bg: 'bg-red-100',
      border: 'border-red-200',
    },
    icon: 'Shield',
    benefits: {
      max_fiat_fund: 400000, // ₦400K
      max_crypto_fund: 40, // 40 SOL
      maturity_period: 7, // 7 days (if premium) / 10 days (if standard)
      multiplier: 4,
    },
    requirements: {
      type: 'either',
      description: 'Master elite performance',
      conditions: {
        transactions: {
          count: 20,
          conditions: [
            'No fake POP (Proof of Payment)',
            'Funding within 1 hour',
            'Payment confirmation within 30 minutes',
          ],
        },
        downlines: {
          count: 20,
          requirement:
            'Active direct referrers who have each completed 1 transaction cycle',
        },
      },
    },
  },
  {
    id: 'elite-4',
    name: 'Elite 4',
    level: 6,
    badge: 'Elite IV',
    description:
      '5x multiplier with ultimate benefits. Maturity period: 7 days (with Premium) or 10 days (Standard).',
    color: {
      primary: 'text-purple-600',
      secondary: 'text-purple-500',
      bg: 'bg-purple-100',
      border: 'border-purple-200',
    },
    icon: 'Shield',
    benefits: {
      max_fiat_fund: 500000, // ₦500K
      max_crypto_fund: 50, // 50 SOL
      maturity_period: 7, // 7 days (if premium) / 10 days (if standard)
      multiplier: 5,
    },
    requirements: {
      type: 'either',
      description: 'Ultimate elite performance',
      conditions: {
        transactions: {
          count: 25,
          conditions: [
            'No fake POP (Proof of Payment)',
            'Funding within 1 hour',
            'Payment confirmation within 30 minutes',
          ],
        },
        downlines: {
          count: 25,
          requirement:
            'Active direct referrers who have each completed 1 transaction cycle',
        },
      },
    },
  },
];

// Helper functions
export const formatCurrencyAmount = (
  amount: number,
  type: 'fiat' | 'crypto'
): string => {
  if (type === 'fiat') {
    if (amount >= 1000000) {
      // Format millions
      return `${PLATFORM_CONFIG.currency.fiat.symbol}${(amount / 1000000).toFixed(0)}M`;
    } else if (amount >= 1000) {
      // Format thousands
      return `${PLATFORM_CONFIG.currency.fiat.symbol}${(amount / 1000).toFixed(0)}K`;
    } else {
      // Format smaller amounts
      return `${PLATFORM_CONFIG.currency.fiat.symbol}${amount.toLocaleString()}`;
    }
  }
  return `${amount} ${PLATFORM_CONFIG.currency.crypto.symbol}`;
};

export const getPrivilegeById = (id: string): PrivilegeData | undefined => {
  return PRIVILEGES_DATA.find(privilege => privilege.id === id);
};

export const getPrivilegeByLevel = (
  level: number
): PrivilegeData | undefined => {
  return PRIVILEGES_DATA.find(privilege => privilege.level === level);
};
