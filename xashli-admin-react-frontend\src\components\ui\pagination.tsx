import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from './button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';
import { cn } from '../../utils';

// Pagination data interface
export interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number | null;
  to: number | null;
}

// Unified pagination interface
export interface PaginationProps {
  pagination: PaginationData | null;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  isLoading?: boolean;
  showPageSizeSelector?: boolean;
  pageSizeOptions?: number[];
  itemLabel?: string; // e.g., "Items", "Rows", "Results"
  className?: string;
}

// Unified pagination component
export const Pagination: React.FC<PaginationProps> = ({
  pagination,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
  showPageSizeSelector = true,
  pageSizeOptions = [10, 15, 25, 50],
  itemLabel = 'Items',
  className,
}) => {
  if (isLoading || !pagination || pagination.total === 0) {
    return null;
  }

  const { current_page, last_page, total, from, to } = pagination;

  return (
    <div
      className={cn(
        'flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 px-2 py-4 border-t',
        className
      )}
    >
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 lg:gap-8">
        {showPageSizeSelector && (
          <div className="flex items-center gap-2">
            <p className="text-sm font-medium whitespace-nowrap">
              {itemLabel} per page
            </p>
            <Select
              value={pageSize.toString()}
              onValueChange={value => onPageSizeChange(Number(value))}
            >
              <SelectTrigger className="h-8 w-[70px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map(size => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex items-center justify-between sm:justify-start gap-4">
          <div className="text-sm font-medium whitespace-nowrap">
            Page {current_page} of {last_page}
          </div>

          <div className="text-sm text-muted-foreground">
            Showing {from || 0} to {to || 0} of {total} results
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center sm:justify-end gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= last_page}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
