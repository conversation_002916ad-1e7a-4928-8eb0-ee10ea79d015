import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Search } from 'lucide-react';
import type { FundFilters as FundFiltersType } from '../../types/fund';
import type { User } from '../../types/user';

interface FundFiltersProps {
  filters: FundFiltersType;
  users: User[];
  onFilterChange: (key: keyof FundFiltersType, value: string) => void;
  onClearFilters: () => void;
}

export const FundFilters: React.FC<FundFiltersProps> = ({
  filters,
  users,
  onFilterChange,
  onClearFilters,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
        <CardDescription>Filter funds by various criteria</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by user or amount..."
                value={filters.search || ''}
                onChange={e => onFilterChange('search', e.target.value)}
                className="pl-10"
              />
            </div>
          </div>{' '}
          {/* Status Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Status</label>
            <Select
              value={filters.status || 'all'}
              onValueChange={value =>
                onFilterChange('status', value === 'all' ? '' : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>{' '}
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="matched">Matched</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {/* Currency Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Currency</label>{' '}
            <Select
              value={filters.currency || 'all'}
              onValueChange={value =>
                onFilterChange('currency', value === 'all' ? '' : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All currencies" />
              </SelectTrigger>{' '}
              <SelectContent>
                <SelectItem value="all">All currencies</SelectItem>
                <SelectItem value="fiat">Fiat (NGN)</SelectItem>
                <SelectItem value="crypto">Crypto (SOL)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {/* User Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium">User</label>{' '}
            <Select
              value={filters.user_id || 'all'}
              onValueChange={value =>
                onFilterChange('user_id', value === 'all' ? '' : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All users" />
              </SelectTrigger>{' '}
              <SelectContent>
                <SelectItem value="all">All users</SelectItem>
                {users.map(user => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name} ({user.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Date Range */}
          <div className="space-y-2">
            <label className="text-sm font-medium">From Date</label>
            <Input
              type="date"
              value={filters.date_from || ''}
              onChange={e => onFilterChange('date_from', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">To Date</label>
            <Input
              type="date"
              value={filters.date_to || ''}
              onChange={e => onFilterChange('date_to', e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-end mt-4">
          <Button variant="outline" onClick={onClearFilters}>
            Clear Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
