<?php

namespace App\Console\Commands;

use App\Models\Fund;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CompleteFundCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'fund:complete {fund_id : The ID of the fund to mark as completed}';

    /**
     * The console command description.
     */
    protected $description = 'Mark a fund as completed and trigger user stats and referral bonus updates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $fundId = $this->argument('fund_id');

        // Find the fund
        $fund = Fund::find($fundId);

        if (! $fund) {
            $this->error("Fund with ID {$fundId} not found.");

            return 1;
        }

        $this->info("Found fund: {$fund->id} (Amount: {$fund->amount} {$fund->currency}, Current Status: {$fund->status})");

        try {
            DB::beginTransaction();

            // Check if fund is already completed
            if ($fund->status === 'completed') {
                $this->warn("Fund {$fund->id} is already marked as completed.");
                $this->info('User stats and referral bonuses have already been processed. Skipping to avoid duplicates.');

                DB::commit();

                return 0;
            }

            // Check if all non-cancelled payment matches are complete before marking as completed
            $paymentMatches = $fund->paymentMatches()->where('status', '!=', 'cancelled')->get();

            if ($paymentMatches->isEmpty()) {
                $this->error("Fund {$fund->id} has no payment matches. Cannot mark as completed.");
                DB::rollBack();

                return 1;
            }

            $incompleteMatches = $paymentMatches->filter(function ($match) {
                return ! $match->isComplete();
            });

            if ($incompleteMatches->isNotEmpty()) {
                $this->error("Fund {$fund->id} has incomplete payment matches:");
                foreach ($incompleteMatches as $match) {
                    $issues = [];
                    if (! $match->is_payment_sent_confirmed) {
                        $issues[] = 'payment not sent';
                    }
                    if (! $match->is_payment_received_confirmed) {
                        $issues[] = 'payment not received';
                    }
                    if (empty($match->payment_proof_image)) {
                        $issues[] = 'no payment proof';
                    }

                    $this->line("  - Match {$match->id}: " . implode(', ', $issues));
                }

                $this->error('Cannot complete fund until all payment matches are properly confirmed.');
                DB::rollBack();

                return 1;
            } else {
                $this->info('All payment matches are complete. Proceeding with completion.');
            }

            // Update fund status to completed
            $fund->status = 'completed';
            $fund->save();

            $this->info('Updated fund status to completed.');

            // Process referral bonuses (skip for admin funds)
            $fundUser = $fund->user ?? User::find($fund->user_id);
            if ($fundUser && $fundUser->role !== 'admin') {
                $fund->processReferralBonuses();
                $this->info("Processed referral bonuses for fund {$fund->id}");
            } else {
                $this->info('Skipped referral bonuses (fund owner is admin)');
            }

            DB::commit();

            $this->info("✅ Fund {$fund->id} has been successfully marked as completed!");
            $this->info('User stats and referral bonuses have been processed.');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Failed to complete fund: ' . $e->getMessage());

            return 1;
        }

        return 0;
    }
}
