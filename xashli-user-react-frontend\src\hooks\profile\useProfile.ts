import { useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { profileService } from '../../services/profile';
import { useAuth } from '../../contexts/AuthContext';
import type { UpdateProfileRequest } from '../../types/profile';

export const useProfile = () => {
  const { user, refresh } = useAuth();
  const [actionLoading, setActionLoading] = useState(false);

  const updateProfile = useCallback(
    async (data: UpdateProfileRequest) => {
      try {
        setActionLoading(true);
        const response = await profileService.updateProfile(data);

        if (response.status === 'success' && response.data) {
          toast.success(
            response.data.message || 'Profile updated successfully'
          );

          // Refresh user data from auth context
          await refresh();

          return true;
        } else {
          toast.error(response.message || 'Failed to update profile');
          return false;
        }
      } catch (error) {
        console.error('Error updating profile:', error);
        toast.error('Failed to update profile');
        return false;
      } finally {
        setActionLoading(false);
      }
    },
    [refresh]
  );

  return {
    user,
    actionLoading,
    updateProfile,
  };
};
