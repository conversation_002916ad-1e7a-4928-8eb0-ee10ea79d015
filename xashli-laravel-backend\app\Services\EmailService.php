<?php

namespace App\Services;

use App\Mail\ConfirmationCodeMail;
use App\Mail\EmailVerificationMail;
use App\Mail\PasswordResetMail;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailService
{
    /**
     * Send email verification code to user.
     *
     * @throws \Exception
     */
    public function sendVerificationCode(User $user, string $verificationCode): bool
    {
        try {
            if (app()->environment(['production'])) {
                // Send actual email in production
                Mail::to($user->email)->send(new EmailVerificationMail($user, $verificationCode));

                Log::info("Email verification code sent to user: {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                ]);
            } else {
                // Log email data in development/local
                Log::info("Email verification code (DEV MODE - Not sent): {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'verification_code' => $verificationCode,
                    'subject' => 'Verify Your Email - ' . config('app.name'),
                    'environment' => app()->environment(),
                    'note' => 'Email not sent - development mode',
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send email verification code to user: {$user->email}", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'environment' => app()->environment(),
            ]);
            throw $e;
        }
    }

    /**
     * Send password reset email to user.
     *
     * @throws \Exception
     */
    public function sendPasswordResetEmail(User $user, string $resetToken): bool
    {
        try {
            // Generate reset URL based on user role
            if ($user->role === 'admin') {
                $frontendUrl = config('app.admin_frontend_url', config('app.url'));
            } else {
                $frontendUrl = config('app.frontend_url', config('app.url'));
            }

            $resetUrl = $frontendUrl . '/auth/reset-password?token=' . $resetToken;

            if (app()->environment(['production'])) {
                // Send actual email in production
                Mail::to($user->email)->send(new PasswordResetMail($user, $resetToken, $resetUrl));

                Log::info("Password reset email sent to user: {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                ]);
            } else {
                // Log email data in development/local
                Log::info("Password reset email (DEV MODE - Not sent): {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'reset_token' => $resetToken,
                    'reset_url' => $resetUrl,
                    'subject' => 'Reset Your Password - ' . config('app.name'),
                    'environment' => app()->environment(),
                    'note' => 'Email not sent - development mode',
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send password reset email to user: {$user->email}", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'environment' => app()->environment(),
            ]);
            throw $e;
        }
    }

    /**
     * Send confirmation code email to user.
     *
     * @throws \Exception
     */
    public function sendConfirmationCode(User $user, string $confirmationCode, string $action, string $context): bool
    {
        try {
            if (app()->environment(['production'])) {
                // Send actual email in production
                Mail::to($user->email)->send(new ConfirmationCodeMail($user, $confirmationCode, $action, $context));

                Log::info("Confirmation code sent to user: {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'action' => $action,
                    'context' => $context,
                ]);
            } else {
                // Log email data in development/local
                Log::info("Confirmation code (DEV MODE - Not sent): {$user->email}", [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'confirmation_code' => $confirmationCode,
                    'action' => $action,
                    'context' => $context,
                    'subject' => 'Your Confirmation Code - ' . config('app.name'),
                    'environment' => app()->environment(),
                    'note' => 'Email not sent - development mode',
                ]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send confirmation code to user: {$user->email}", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'environment' => app()->environment(),
            ]);
            throw $e;
        }
    }
}
