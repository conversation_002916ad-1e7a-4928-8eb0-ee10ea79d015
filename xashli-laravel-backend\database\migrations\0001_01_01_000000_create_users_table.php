<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('full_name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('profile_image')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('referral_code')->unique();
            $table->string('referrer_code')->nullable();
            $table->uuid('referrer_id')->nullable();
            $table->integer('referee_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamp('deactivated_at')->nullable()->comment('When the user was deactivated');
            $table->integer('deactivation_duration')->nullable()->comment('Duration of deactivation in minutes');
            $table->enum('role', ['user', 'admin'])->default('user');
            $table->boolean('has_premium_privilege')->default(false);
            $table->boolean('has_elite_privilege')->default(false);
            $table->tinyInteger('elite_privilege_level')->default(0)->comment('0=none, 1=level1, 2=level2, 3=level3, 4=level4');
            $table->rememberToken();
            $table->timestamps();

            $table->foreign('referrer_id')->references('id')->on('users')->onDelete('set null');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignUuid('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
