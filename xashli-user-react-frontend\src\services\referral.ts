import { apiClient } from './api';
import type { ApiResponse } from '../types/common';
import type {
  ReferralInfoApiResponse,
  RefereesApiResponse,
  BonusesApiResponse,
} from '../types/referral';

export const referralService = {
  /**
   * Get the current user's referral information
   */
  getReferralInfo: async (): Promise<ApiResponse<ReferralInfoApiResponse>> => {
    return apiClient.get<ReferralInfoApiResponse>('/referrals/info');
  },

  /**
   * Get the current user's referees
   */
  getReferees: async (): Promise<ApiResponse<RefereesApiResponse>> => {
    return apiClient.get<RefereesApiResponse>('/referrals/referees');
  },

  /**
   * Get the current user's referral bonuses
   */
  getBonuses: async (params?: {
    date_from?: string;
    date_to?: string;
    level?: 1 | 2 | 3;
  }): Promise<ApiResponse<BonusesApiResponse>> => {
    return apiClient.get<BonusesApiResponse>('/referrals/bonuses', {
      params,
    });
  },
};
