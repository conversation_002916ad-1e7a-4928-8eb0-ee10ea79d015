import { PublicKey, Connection } from '@solana/web3.js';

// Custom RPC URL for Solana connection
const SOLANA_RPC_URL =
  'https://blissful-methodical-frost.solana-mainnet.quiknode.pro/72d78c16b2818325dd1e7e57be1fdccb7bc6957f/';

/**
 * Transaction details structure
 */
export interface TransactionDetails {
  signature: string;
  sender: string;
  recipient: string;
  amount: number;
  timestamp: Date;
  status: 'success' | 'failed' | 'pending';
  fee: number;
  blockTime: number | null;
}

/**
 * Validates if a string is a valid Solana address
 * @param address - The address string to validate
 * @returns boolean - true if valid, false otherwise
 */
const isValidSolanaAddress = (address: string): boolean => {
  try {
    const cleanAddress = address.trim();
    if (!cleanAddress) return false;

    new PublicKey(cleanAddress);
    return true;
  } catch {
    return false;
  }
};

/**
 * Gets Solana transaction details by transaction hash
 * @param transactionHash - The transaction signature/hash
 * @returns Promise<TransactionDetails | null> - Transaction details or null if not found
 */
const getSolanaTransactionDetails = async (
  transactionHash: string
): Promise<TransactionDetails | null> => {
  try {
    const connection = new Connection(SOLANA_RPC_URL, 'confirmed');

    // Get transaction details
    const transaction = await connection.getTransaction(transactionHash, {
      maxSupportedTransactionVersion: 0,
    });

    if (!transaction) {
      return null;
    }

    // Extract transaction information
    const { meta, transaction: txn, blockTime } = transaction;

    if (!meta || !txn.message) {
      return null;
    }

    // Get account keys - handle both legacy and versioned transactions
    let accountKeys: string[] = [];

    if ('accountKeys' in txn.message) {
      // Legacy transaction
      accountKeys = txn.message.accountKeys.map((key: any) => key.toString());
    } else {
      // Versioned transaction
      const keys = txn.message.getAccountKeys();
      accountKeys = keys
        .keySegments()
        .flat()
        .map((key: any) => key.toString());
    }

    // Find sender (first account that signed and has a negative balance change)
    const sender = accountKeys[0] || ''; // First account is typically the fee payer/sender

    // Find recipient (account with positive balance change, excluding sender)
    let recipient = '';
    let amount = 0;

    if (meta.preBalances && meta.postBalances) {
      for (let i = 0; i < meta.preBalances.length; i++) {
        const balanceChange = meta.postBalances[i] - meta.preBalances[i];

        // Skip the sender account and look for positive balance changes
        if (i !== 0 && balanceChange > 0) {
          recipient = accountKeys[i] || '';
          amount = balanceChange / 1e9; // Convert lamports to SOL
          break;
        }
      }
    }

    const status = meta.err ? 'failed' : 'success';
    const fee = meta.fee / 1e9; // Convert lamports to SOL

    return {
      signature: transactionHash,
      sender,
      recipient,
      amount,
      timestamp: blockTime ? new Date(blockTime * 1000) : new Date(),
      status,
      fee,
      blockTime: blockTime || null,
    };
  } catch (error) {
    console.error('Error fetching Solana transaction details:', error);
    return null;
  }
};

// =============================================================================
// EXPORTED FUNCTIONS (Public API)
// =============================================================================

/**
 * Validates crypto address and returns validation result with error message
 * @param address - The address to validate
 * @param currency - The cryptocurrency type
 * @returns validation result object
 */
export const validateCryptoAddress = (
  address: string,
  currency: string
): {
  isValid: boolean;
  error?: string;
} => {
  const cleanAddress = address.trim();

  if (!cleanAddress) {
    return { isValid: false, error: 'Wallet address is required' };
  }

  switch (currency.toLowerCase()) {
    case 'solana':
    case 'sol':
      return isValidSolanaAddress(cleanAddress)
        ? { isValid: true }
        : {
            isValid: false,
            error: 'Please enter a valid Solana wallet address',
          };
    default:
      return {
        isValid: false,
        error: `Unsupported cryptocurrency: ${currency}`,
      };
  }
};

/**
 * Gets transaction details for any supported cryptocurrency
 * @param transactionHash - The transaction hash
 * @param currency - The cryptocurrency type
 * @returns Promise<TransactionDetails | null> - Transaction details or null if not found
 */
export const getTransactionDetails = async (
  transactionHash: string,
  currency: string
): Promise<TransactionDetails | null> => {
  switch (currency.toLowerCase()) {
    case 'solana':
    case 'sol':
      return getSolanaTransactionDetails(transactionHash);
    default:
      throw new Error(`Unsupported cryptocurrency: ${currency}`);
  }
};
