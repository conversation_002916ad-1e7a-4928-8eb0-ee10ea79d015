export interface User {
  id: string;
  full_name: string;
  email: string;
  phone?: string;
  profile_image?: string;
  role: 'user' | 'admin';
  has_premium_privilege: boolean;
  has_elite_privilege: boolean;
  elite_privilege_level: number;
  is_active: boolean;
  deactivated_at?: string;
  deactivation_duration?: number;
  referral_code: string;
  referrer_code?: string;
  referrer_id?: string;
  referee_count: number;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterRequest {
  full_name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone?: string;
  referrer_code?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  password_confirmation: string;
}

export interface AuthResponse {
  user: User;
  authorization: {
    access_token: string;
    refresh_token: string;
    type: string;
    expires_in: number;
  };
}
