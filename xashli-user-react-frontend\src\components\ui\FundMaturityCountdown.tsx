import { useState, useEffect } from 'react';
import { Calendar, TrendingUp } from 'lucide-react';
import moment from 'moment';
import { Badge } from './Badge';

interface FundMaturityCountdownProps {
  maturityDate: string | null;
  className?: string;
  shouldDisplay?: boolean; // Controls whether the component should render
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  total: number;
}

export function FundMaturityCountdown({
  maturityDate,
  className = '',
  shouldDisplay = true,
}: FundMaturityCountdownProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(
    null
  );
  const [isMatured, setIsMatured] = useState(false);

  // Calculate time remaining
  const calculateTimeRemaining = (): TimeRemaining => {
    if (!maturityDate) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    const now = moment();
    const maturity = moment(maturityDate);
    const diff = maturity.diff(now);

    if (diff <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 };
    }

    const duration = moment.duration(diff);
    return {
      days: Math.floor(duration.asDays()),
      hours: duration.hours(),
      minutes: duration.minutes(),
      seconds: duration.seconds(),
      total: diff,
    };
  };

  // Format countdown display
  const formatCountdown = (time: TimeRemaining): string => {
    if (time.total <= 0) return '00:00:00';

    if (time.days > 0) {
      return `${time.days}d ${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
    }

    return `${time.hours.toString().padStart(2, '0')}:${time.minutes.toString().padStart(2, '0')}:${time.seconds.toString().padStart(2, '0')}`;
  };

  // Update countdown every second
  useEffect(() => {
    const updateCountdown = () => {
      const remaining = calculateTimeRemaining();
      setTimeRemaining(remaining);
      setIsMatured(remaining.total <= 0);
    };

    updateCountdown(); // Initial calculation
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [maturityDate]);

  // Don't render if shouldDisplay is false
  if (!shouldDisplay) {
    return null;
  }

  // Don't render if already matured
  if (isMatured) {
    return null;
  }

  // Don't render if no time remaining data yet
  if (!timeRemaining) {
    return null;
  }

  const isAlmostMatured =
    timeRemaining.total > 0 && timeRemaining.total <= 24 * 60 * 60 * 1000; // Less than 24 hours
  const isVeryClose =
    timeRemaining.total > 0 && timeRemaining.total <= 60 * 60 * 1000; // Less than 1 hour

  return (
    <div
      className={`bg-background-secondary rounded-lg border border-border p-4 ${className}`}
    >
      <div className='space-y-3'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <TrendingUp
              className={`h-4 w-4 ${isVeryClose ? 'text-green-500' : isAlmostMatured ? 'text-blue-500' : 'text-primary'}`}
            />
            <span className='font-medium text-foreground'>Fund Maturity</span>
          </div>
          {isVeryClose && (
            <Badge className='bg-green-500/10 text-green-600 border-green-200'>
              Almost Ready
            </Badge>
          )}
          {isAlmostMatured && !isVeryClose && (
            <Badge className='bg-blue-500/10 text-blue-600 border-blue-200'>
              Soon
            </Badge>
          )}
        </div>

        {/* Maturity Date */}
        <div className='flex items-center gap-2 text-foreground-secondary'>
          <Calendar className='h-4 w-4' />
          <span className='text-sm'>
            Matures on {moment(maturityDate).format('MMM D, YYYY [at] h:mm A')}
          </span>
        </div>

        {/* Countdown */}
        {timeRemaining && timeRemaining.total > 0 && (
          <div
            className={`rounded-lg p-3 border ${
              isVeryClose
                ? 'bg-green-500/5 border-green-500/20'
                : isAlmostMatured
                  ? 'bg-blue-500/5 border-blue-500/20'
                  : 'bg-primary/5 border-primary/20'
            }`}
          >
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium text-foreground'>
                Time to Maturity:
              </span>
              <span
                className={`text-lg font-mono font-bold ${
                  isVeryClose
                    ? 'text-green-600'
                    : isAlmostMatured
                      ? 'text-blue-600'
                      : 'text-primary'
                }`}
              >
                {formatCountdown(timeRemaining)}
              </span>
            </div>
          </div>
        )}

        {/* Info Message */}
        <div
          className={`text-xs rounded-lg p-2 ${
            isVeryClose
              ? 'bg-green-500/5 text-green-600'
              : isAlmostMatured
                ? 'bg-blue-500/5 text-blue-600'
                : 'text-foreground-secondary'
          }`}
        >
          {isVeryClose
            ? 'Your fund will be ready for withdrawal very soon!'
            : isAlmostMatured
              ? 'Your fund will mature within 24 hours'
              : 'Your fund is growing and will be ready for withdrawal when it matures'}
        </div>
      </div>
    </div>
  );
}
