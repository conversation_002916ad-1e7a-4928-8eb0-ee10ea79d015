<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('funds', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->decimal('amount', 20, 8);
            $table->enum('currency', ['fiat', 'crypto']);
            $table->uuid('payment_method_id');
            $table->decimal('platform_fee_percentage', 5, 2);
            $table->timestamp('start_date')->useCurrent();
            $table->timestamp('maturity_date')->nullable();
            $table->integer('maturity_days');
            $table->decimal('growth_percentage', 5, 2);
            $table->decimal('growth_amount', 20, 8);
            $table->decimal('referral_bonus_limit', 20, 8);
            $table->boolean('is_eligible_for_withdrawal')->default(false);
            $table->decimal('amount_matched', 20, 8)->default(0);
            $table->enum('status', ['pending', 'matched', 'completed', 'cancelled'])->default('pending');
            $table->uuid('prev_fund_id')->nullable();
            $table->uuid('next_fund_id')->nullable();
            $table->uuid('withdraw_id')->nullable()->comment('References withdraws.id - soft reference to avoid circular dependency');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('payment_method_id')->references('id')->on('payment_methods')->onDelete('restrict');
            $table->foreign('prev_fund_id')->references('id')->on('funds')->onDelete('set null');
            $table->foreign('next_fund_id')->references('id')->on('funds')->onDelete('set null');

            // Index for performance on withdraw_id lookups
            $table->index('withdraw_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('funds');
    }
};
