import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from 'react';
import type { ReactNode } from 'react';
import type { User } from '@/types';
import { authService, tokenStorage, handleApiError } from '@/services';
import { showToast } from '@/utils';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = !!user && tokenStorage.getAccessToken() !== null;

  // Check authentication status on app load
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = useCallback(async () => {
    const token = tokenStorage.getAccessToken();

    if (!token) {
      setUser(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await authService.me();

      if (response.status === 'success') {
        const userData = response.data;

        // Check if user is still admin
        if (userData && userData.role !== 'admin') {
          throw new Error('Access denied. Admin privileges required.');
        }

        setUser(userData);
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      tokenStorage.clearTokens();
      setUser(null);
      setError(null); // Don't show error for auth check failures
    } finally {
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await authService.login({ email, password });
      if (response.status === 'success' && response.data) {
        const { user: userData, authorization } = response.data;

        // Check if user is admin
        if (userData.role !== 'admin') {
          showToast.auth.accessDenied();
          throw new Error('Access denied. Admin privileges required.');
        }

        // Store tokens
        tokenStorage.setAccessToken(authorization.access_token);
        tokenStorage.setRefreshToken(authorization.refresh_token);

        setUser(userData);
        setError(null);

        // Show success toast
        showToast.auth.loginSuccess(userData.full_name || userData.email);
      } else {
        showToast.auth.loginError(response.message);
        throw new Error(response.message);
      }
    } catch (error) {
      const apiError = handleApiError(error);

      // Only show toast if it's not already shown (to avoid duplicate toasts)
      if (!apiError.message.includes('Access denied')) {
        showToast.auth.loginError(apiError.message);
      }

      setUser(null);
      setError(apiError.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      // Call logout API if authenticated
      if (isAuthenticated) {
        await authService.logout();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens and state regardless of API call result
      tokenStorage.clearTokens();
      setUser(null);
      setError(null);
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    checkAuth,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
