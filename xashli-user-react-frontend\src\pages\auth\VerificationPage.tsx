import { useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Mail, CheckCircle } from 'lucide-react';
import { useVerification } from '@/hooks/auth';

const verificationSchema = z.object({
  emailCode: z
    .string()
    .min(6, 'Email verification code must be 6 digits')
    .max(6, 'Email verification code must be 6 digits'),
});

type VerificationFormData = z.infer<typeof verificationSchema>;

export function VerificationPage() {
  const [searchParams] = useSearchParams();
  const email = searchParams.get('email');

  const {
    handleVerification,
    resendEmailCode,
    isLoading,
    isResending,
    verificationStatus,
    countdown,
    maskEmail,
  } = useVerification();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VerificationFormData>({
    resolver: zodResolver(verificationSchema),
  });

  const onSubmit = async (data: VerificationFormData) => {
    await handleVerification({
      email_code: data.emailCode,
    });
  };

  const handleResendCode = async () => {
    await resendEmailCode(reset);
  };

  return (
    <div className='space-y-6'>
      <div className='text-center'>
        <h2 className='text-3xl font-bold text-foreground'>
          Verify Your Email
        </h2>
        <p className='mt-2 text-foreground-secondary'>
          We've sent a verification code to your email address
        </p>
        <p className='mt-2 text-sm font-medium text-warning'>
          💡 Don't see it? Check your spam or junk folder
        </p>
      </div>

      <Card>
        <CardContent>
          {/* Contact Info Display */}
          <div className='mb-6'>
            <div className='flex items-center space-x-3 p-4 bg-muted/30 rounded-lg border border-primary/20'>
              <div className='w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center'>
                <Mail className='h-5 w-5 text-primary' />
              </div>
              <div className='flex-1'>
                <p className='text-sm font-medium text-foreground'>
                  Email Address
                </p>
                <p className='text-sm text-foreground-secondary'>
                  {maskEmail(email || '')}
                </p>
              </div>
              {verificationStatus?.email_verified && (
                <div className='w-8 h-8 bg-success/10 rounded-full flex items-center justify-center'>
                  <CheckCircle className='h-5 w-5 text-success' />
                </div>
              )}
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
            <Input
              label='Email Verification Code'
              placeholder='Enter 6-digit code'
              maxLength={6}
              error={errors.emailCode?.message}
              disabled={verificationStatus?.email_verified}
              className='text-center text-lg tracking-widest font-mono'
              {...register('emailCode')}
            />

            <Button
              type='submit'
              className='w-full'
              isLoading={isLoading}
              disabled={verificationStatus?.email_verified}
            >
              {isLoading ? 'Verifying...' : 'Verify Email'}
            </Button>
          </form>

          {/* Resend Section */}
          <div className='mt-6 pt-6 border-t border-border'>
            <div className='text-center space-y-3'>
              <p className='text-sm text-foreground-secondary'>
                Didn't receive the code?{' '}
                {countdown > 0 && (
                  <span className='font-semibold'>
                    Resend in {countdown} seconds
                  </span>
                )}
              </p>

              <Button
                variant='outline'
                onClick={handleResendCode}
                isLoading={isResending}
                disabled={countdown > 0}
                className='border-primary text-primary hover:bg-primary hover:text-white disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {isResending ? 'Sending...' : 'Resend Code'}
              </Button>
            </div>
          </div>

          <p className='mt-6 text-center text-sm text-foreground-secondary'>
            Need help?{' '}
            <a
              href='/support'
              className='font-medium text-primary hover:text-primary/90'
            >
              Contact Support
            </a>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
