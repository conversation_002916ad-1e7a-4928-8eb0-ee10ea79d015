import { Link, useNavigate } from 'react-router-dom';
import { Plus, CreditCard } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import PaymentMethodCard from '../../components/payment-methods/PaymentMethodCard';
import { DeletePaymentMethodDialog } from '../../components/payment-methods/DeletePaymentMethodDialog';
import { usePaymentMethods } from '../../hooks/payment-methods';

export function PaymentMethodsPage() {
  const navigate = useNavigate();
  const {
    paymentMethods,
    loading,
    deleteDialog,
    handleDeletePaymentMethod,
    openDeleteDialog,
    closeDeleteDialog,
  } = usePaymentMethods();

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Payment Methods
          </h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your bank accounts and crypto wallets
          </p>
        </div>
        <Link to='/payment-methods/create' className='shrink-0'>
          <Button className='flex items-center gap-2 w-full sm:w-auto'>
            <Plus className='h-4 w-4' />
            Add Payment Method
          </Button>
        </Link>
      </div>

      {/* Payment Methods Grid */}
      {paymentMethods.length === 0 ? (
        <div className='text-center py-12'>
          <div className='mx-auto w-24 h-24 bg-background-secondary rounded-full flex items-center justify-center mb-4'>
            <CreditCard className='h-12 w-12 text-foreground-muted' />
          </div>
          <h3 className='text-xl font-semibold text-foreground mb-2'>
            No Payment Methods
          </h3>{' '}
          <p className='text-foreground-secondary mb-6 max-w-md mx-auto'>
            Add your bank account or crypto wallet to start sending and
            receiving payments.
          </p>
          <Link to='/payment-methods/create'>
            <Button>
              <Plus className='h-4 w-4 mr-2' />
              Add Your First Payment Method
            </Button>
          </Link>
        </div>
      ) : (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {' '}
          {paymentMethods.map(paymentMethod => (
            <PaymentMethodCard
              key={paymentMethod.id}
              paymentMethod={paymentMethod}
              onEdit={() =>
                navigate(`/payment-methods/${paymentMethod.id}/edit`)
              }
              onView={() => navigate(`/payment-methods/${paymentMethod.id}`)}
              onDelete={() => openDeleteDialog(paymentMethod)}
            />
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <DeletePaymentMethodDialog
        open={deleteDialog.open}
        paymentMethod={deleteDialog.paymentMethod}
        onConfirm={handleDeletePaymentMethod}
        onCancel={closeDeleteDialog}
      />
    </div>
  );
}
