import { useState } from 'react';
import { paymentMethodService } from '../../services';
import toast from 'react-hot-toast';

export const useAccountValidation = () => {
  const [validatingAccount, setValidatingAccount] = useState(false);
  const [accountValidated, setAccountValidated] = useState(false);

  const validateAccount = async (
    bankCode: string,
    accountNumber: string,
    onSuccess?: (accountName: string) => void,
    onError?: (error: string) => void
  ) => {
    if (!bankCode || !accountNumber) {
      return;
    }

    try {
      setValidatingAccount(true);
      const response = await paymentMethodService.validateBankAccount({
        bank_code: bankCode,
        account_number: accountNumber,
      });

      if (
        response.status === 'success' &&
        response.data &&
        response.data.account_name
      ) {
        setAccountValidated(true);
        toast.success('Account verified successfully');
        onSuccess?.(response.data.account_name);
      } else {
        const errorMessage = 'Invalid account number';
        toast.error(errorMessage);
        onError?.(errorMessage);
      }
    } catch (error) {
      console.error('Error validating account:', error);
      const errorMessage = 'Failed to verify account';
      toast.error(errorMessage);
      onError?.(errorMessage);
    } finally {
      setValidatingAccount(false);
    }
  };

  const resetValidation = () => {
    setAccountValidated(false);
  };

  return {
    validatingAccount,
    accountValidated,
    validateAccount,
    resetValidation,
    setAccountValidated,
  };
};
