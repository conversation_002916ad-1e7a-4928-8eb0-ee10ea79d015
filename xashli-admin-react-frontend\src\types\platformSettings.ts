export interface PlatformSetting {
  id: string;
  key: string;
  value: string | number | boolean;
  type: 'string' | 'int' | 'decimal' | 'boolean';
  description?: string;
  updated_at: string;
}

export interface CreatePlatformSettingRequest {
  key: string;
  value: string;
  type: 'string' | 'int' | 'decimal' | 'boolean';
  description?: string;
}

export interface UpdatePlatformSettingRequest {
  key?: string;
  value?: string;
  type?: 'string' | 'int' | 'decimal' | 'boolean';
  description?: string;
}

export interface PlatformSettingsGroup {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  settings: PlatformSetting[];
}

// Predefined setting categories for better UX
export interface SettingCategory {
  key: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  settings: SettingConfig[];
}

export interface SettingConfig {
  key: string;
  title: string;
  description: string;
  type: 'string' | 'int' | 'decimal' | 'boolean';
  validation?: {
    min?: number;
    max?: number;
    step?: number;
    required?: boolean;
  };
  format?: 'percentage' | 'currency' | 'hours' | 'days' | 'amount';
  unit?: string;
  options?: {
    value: string;
    label: string;
    description?: string;
  }[];
}

export type PlatformSettingsFormData = Record<
  string,
  string | number | boolean
>;

export interface FundingPeriod {
  start_time: string;
  end_time: string;
}

export interface FundingScheduleSettings {
  funding_schedule_enabled: boolean;
  funding_schedule_periods: FundingPeriod[];
}
