import { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  ArrowLeft,
  CreditCard,
  Wallet,
  Edit,
  Trash2,
  Calendar,
  User,
  Building,
  Hash,
  Shield,
} from 'lucide-react';
import {
  usePaymentMethod,
  usePaymentMethods,
} from '../../hooks/payment-methods';
import { DeletePaymentMethodDialog } from '../../components/payment-methods/DeletePaymentMethodDialog';

export function PaymentMethodDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const { paymentMethod, loading } = usePaymentMethod(id);
  const { handleDeletePaymentMethod } = usePaymentMethods();

  const onDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async (
    paymentMethod: any,
    confirmationCode: string
  ) => {
    await handleDeletePaymentMethod(paymentMethod, confirmationCode);
    setShowDeleteDialog(false);
    navigate('/payment-methods');
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
        </div>
      </div>
    );
  }

  if (!paymentMethod) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-foreground'>
            Payment Method Not Found
          </h1>
          <p className='text-foreground-secondary mt-2'>
            The payment method you're looking for doesn't exist.
          </p>
          <Link
            to='/payment-methods'
            className='inline-flex items-center gap-2 mt-4 text-primary hover:underline'
          >
            <ArrowLeft className='h-4 w-4' />
            Back to Payment Methods
          </Link>
        </div>
      </div>
    );
  }

  const Icon = paymentMethod.type === 'bank' ? CreditCard : Wallet;

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='mb-8'>
        {/* Desktop layout */}
        <div className='hidden md:flex items-center gap-4'>
          <button
            onClick={() => navigate('/payment-methods')}
            className='p-2 hover:bg-background-secondary rounded-lg transition-colors'
          >
            <ArrowLeft className='h-5 w-5' />
          </button>
          <div className='flex-1'>
            <h1 className='text-3xl font-bold text-foreground'>
              Payment Method Details
            </h1>
            <p className='text-foreground-secondary mt-2'>
              View and manage your payment method
            </p>
          </div>
          <div className='flex items-center gap-2'>
            <Link
              to={`/payment-methods/${paymentMethod.id}/edit`}
              className='inline-flex items-center gap-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors'
            >
              <Edit className='h-4 w-4' />
              Edit
            </Link>
            <button
              onClick={onDelete}
              className='inline-flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors'
            >
              <Trash2 className='h-4 w-4' />
              Delete
            </button>
          </div>
        </div>

        {/* Mobile layout */}
        <div className='md:hidden'>
          <div className='flex items-center gap-4 mb-4'>
            <button
              onClick={() => navigate('/payment-methods')}
              className='p-2 hover:bg-background-secondary rounded-lg transition-colors'
            >
              <ArrowLeft className='h-5 w-5' />
            </button>
            <div className='flex-1'>
              <h1 className='text-2xl font-bold text-foreground'>
                Payment Method Details
              </h1>
              <p className='text-foreground-secondary mt-1'>
                View and manage your payment method
              </p>
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <Link
              to={`/payment-methods/${paymentMethod.id}/edit`}
              className='flex-1 inline-flex items-center justify-center gap-2 bg-primary text-primary-foreground px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors'
            >
              <Edit className='h-4 w-4' />
              Edit
            </Link>
            <button
              onClick={onDelete}
              className='flex-1 inline-flex items-center justify-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors'
            >
              <Trash2 className='h-4 w-4' />
              Delete
            </button>
          </div>
        </div>
      </div>

      {/* Payment Method Card */}
      <div className='max-w-4xl'>
        <div className='bg-background-secondary border border-border rounded-lg overflow-hidden'>
          {/* Header Section */}
          <div className='bg-gradient-to-r from-primary to-primary/80 p-6 text-primary-foreground'>
            <div className='flex items-center gap-4'>
              <div className='bg-white/20 p-3 rounded-lg'>
                <Icon className='h-8 w-8' />
              </div>
              <div>
                <h2 className='text-2xl font-bold'>
                  {paymentMethod.type === 'bank'
                    ? `${paymentMethod.account_name} - ${paymentMethod.bank_name}`
                    : `${paymentMethod.crypto_network} Wallet`}
                </h2>
                <p className='text-primary-foreground/80 capitalize'>
                  {paymentMethod.type} Payment Method
                </p>
              </div>
              <div className='ml-auto'>
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    paymentMethod.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {paymentMethod.status}
                </span>
              </div>
            </div>
          </div>

          {/* Details Section */}
          <div className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {paymentMethod.type === 'bank' ? (
                <>
                  {/* Bank Details */}
                  <div className='space-y-4'>
                    <h3 className='text-lg font-semibold text-foreground'>
                      Bank Information
                    </h3>

                    <div className='space-y-3'>
                      <div className='flex items-center gap-3'>
                        <Building className='h-5 w-5 text-foreground-muted' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Bank Name
                          </p>
                          <p className='font-medium text-foreground'>
                            {paymentMethod.bank_name}
                          </p>
                        </div>
                      </div>

                      <div className='flex items-center gap-3'>
                        <Hash className='h-5 w-5 text-foreground-muted' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Bank Code
                          </p>
                          <p className='font-medium text-foreground'>
                            {paymentMethod.bank_code}
                          </p>
                        </div>
                      </div>

                      <div className='flex items-center gap-3'>
                        <CreditCard className='h-5 w-5 text-foreground-muted' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Account Number
                          </p>
                          <p className='font-medium text-foreground'>
                            {paymentMethod.account_number}
                          </p>
                        </div>
                      </div>

                      <div className='flex items-center gap-3'>
                        <User className='h-5 w-5 text-foreground-muted' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Account Name
                          </p>
                          <p className='font-medium text-foreground'>
                            {paymentMethod.account_name}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* Crypto Details */}
                  <div className='space-y-4'>
                    <h3 className='text-lg font-semibold text-foreground'>
                      Wallet Information
                    </h3>

                    <div className='space-y-3'>
                      <div className='flex items-center gap-3'>
                        <Wallet className='h-5 w-5 text-foreground-muted' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Network
                          </p>
                          <p className='font-medium text-foreground'>
                            {paymentMethod.crypto_network}
                          </p>
                        </div>
                      </div>

                      <div className='flex items-start gap-3'>
                        <Hash className='h-5 w-5 text-foreground-muted mt-1' />
                        <div>
                          <p className='text-sm text-foreground-muted'>
                            Wallet Address
                          </p>
                          <p className='font-medium text-foreground font-mono text-sm break-all'>
                            {paymentMethod.wallet_address}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Metadata */}
              <div className='space-y-4'>
                <h3 className='text-lg font-semibold text-foreground'>
                  Metadata
                </h3>

                <div className='space-y-3'>
                  <div className='flex items-center gap-3'>
                    <Calendar className='h-5 w-5 text-foreground-muted' />
                    <div>
                      <p className='text-sm text-foreground-muted'>Created</p>
                      <p className='font-medium text-foreground'>
                        {new Date(paymentMethod.created_at).toLocaleDateString(
                          'en-US',
                          {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          }
                        )}
                      </p>
                    </div>
                  </div>

                  <div className='flex items-center gap-3'>
                    <Calendar className='h-5 w-5 text-foreground-muted' />
                    <div>
                      <p className='text-sm text-foreground-muted'>
                        Last Updated
                      </p>
                      <p className='font-medium text-foreground'>
                        {new Date(paymentMethod.updated_at).toLocaleDateString(
                          'en-US',
                          {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                          }
                        )}
                      </p>
                    </div>
                  </div>

                  <div className='flex items-center gap-3'>
                    <Hash className='h-5 w-5 text-foreground-muted' />
                    <div>
                      <p className='text-sm text-foreground-muted'>
                        Payment Method ID
                      </p>
                      <p className='font-medium text-foreground font-mono text-xs'>
                        {paymentMethod.id}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Info Banner */}
        <div className='mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <div className='flex items-start gap-3'>
            <Shield className='h-5 w-5 text-blue-500 mt-0.5' />
            <div>
              <h4 className='font-medium text-blue-900'>
                Security Information
              </h4>
              <p className='text-sm text-blue-700 mt-1'>
                {paymentMethod.type === 'bank'
                  ? 'Your bank account details are securely stored and encrypted.'
                  : 'Your wallet address is stored securely. Always verify the address before making transactions.'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeletePaymentMethodDialog
        open={showDeleteDialog}
        paymentMethod={paymentMethod}
        onConfirm={handleConfirmDelete}
        onCancel={() => setShowDeleteDialog(false)}
      />
    </div>
  );
}
