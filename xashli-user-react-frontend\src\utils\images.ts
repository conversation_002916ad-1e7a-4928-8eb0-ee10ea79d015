/**
 * Utility functions for handling image URLs
 */

const isProduction = (): boolean => {
  const apiBaseUrl =
    import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
  return !apiBaseUrl.includes('localhost');
};

export const getImageUrl = (
  imagePath: string | null | undefined
): string | null => {
  if (!imagePath) {
    return null;
  }

  // Return as-is if already a complete URL
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  const apiBaseUrl =
    import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
  const baseServerUrl = apiBaseUrl.replace(/\/api$/, ''); // Only replace '/api' at the end

  // Clean up the image path
  let cleanImagePath = imagePath.startsWith('/')
    ? imagePath.substring(1)
    : imagePath;

  // Ensure proper storage/uploads structure
  if (
    cleanImagePath.startsWith('storage/') &&
    !cleanImagePath.startsWith('storage/uploads/')
  ) {
    // If path starts with 'storage/' but not 'storage/uploads/', insert 'uploads/'
    cleanImagePath = cleanImagePath.replace('storage/', 'storage/uploads/');
  } else if (!cleanImagePath.startsWith('storage/')) {
    // If path doesn't start with 'storage/', assume it's a relative path from uploads
    cleanImagePath = `storage/uploads/${cleanImagePath}`;
  }

  // Build URL based on environment
  if (isProduction()) {
    // Production: add /public/ prefix
    const fullUrl = `${baseServerUrl}/public/${cleanImagePath}`;
    return fullUrl;
  } else {
    // Development: use path as-is
    const fullUrl = `${baseServerUrl}/${cleanImagePath}`;
    return fullUrl;
  }
};

export const getImageUrlWithFallback = (
  imagePath: string | null | undefined,
  fallback: string = ''
): string => {
  return getImageUrl(imagePath) || fallback;
};
