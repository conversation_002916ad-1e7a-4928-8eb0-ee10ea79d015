{"name": "xashli-user-react-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "format:verbose": "prettier --list-different . && prettier --write ."}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@paystack/inline-js": "^2.22.6", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@solana/web3.js": "^1.98.2", "@tanstack/react-query": "^5.80.7", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "moment": "^2.30.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "zod": "^3.25.46"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}